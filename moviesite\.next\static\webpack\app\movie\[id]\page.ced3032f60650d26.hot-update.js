"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/movie/[id]/page",{

/***/ "(app-pages-browser)/./src/components/VideoPlayer.tsx":
/*!****************************************!*\
  !*** ./src/components/VideoPlayer.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VideoPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./src/components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction VideoPlayer(param) {\n    let { src, title, onClose, controls, metadata } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showControls, setShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoPlayer.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"VideoPlayer.useEffect.timer\": ()=>{\n                    setIsLoading(false);\n                }\n            }[\"VideoPlayer.useEffect.timer\"], 3000);\n            return ({\n                \"VideoPlayer.useEffect\": ()=>clearTimeout(timer)\n            })[\"VideoPlayer.useEffect\"];\n        }\n    }[\"VideoPlayer.useEffect\"], [\n        src\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VideoPlayer.useEffect\": ()=>{\n            let hideTimer;\n            const resetTimer = {\n                \"VideoPlayer.useEffect.resetTimer\": ()=>{\n                    setShowControls(true);\n                    clearTimeout(hideTimer);\n                    hideTimer = setTimeout({\n                        \"VideoPlayer.useEffect.resetTimer\": ()=>{\n                            setShowControls(false);\n                        }\n                    }[\"VideoPlayer.useEffect.resetTimer\"], 3000);\n                }\n            }[\"VideoPlayer.useEffect.resetTimer\"];\n            const handleMouseMove = {\n                \"VideoPlayer.useEffect.handleMouseMove\": ()=>resetTimer()\n            }[\"VideoPlayer.useEffect.handleMouseMove\"];\n            const handleKeyPress = {\n                \"VideoPlayer.useEffect.handleKeyPress\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    }\n                    resetTimer();\n                }\n            }[\"VideoPlayer.useEffect.handleKeyPress\"];\n            document.addEventListener('mousemove', handleMouseMove);\n            document.addEventListener('keydown', handleKeyPress);\n            resetTimer();\n            return ({\n                \"VideoPlayer.useEffect\": ()=>{\n                    document.removeEventListener('mousemove', handleMouseMove);\n                    document.removeEventListener('keydown', handleKeyPress);\n                    clearTimeout(hideTimer);\n                }\n            })[\"VideoPlayer.useEffect\"];\n        }\n    }[\"VideoPlayer.useEffect\"], [\n        onClose\n    ]);\n    const toggleFullscreen = ()=>{\n        if (!document.fullscreenElement) {\n            document.documentElement.requestFullscreen();\n            setIsFullscreen(true);\n        } else {\n            document.exitFullscreen();\n            setIsFullscreen(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black z-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black via-black/50 to-transparent p-4 transition-opacity duration-300 \".concat(showControls ? 'opacity-100' : 'opacity-0'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-white text-xl hover:text-gray-300 flex items-center space-x-2 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"←\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Back\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        (metadata === null || metadata === void 0 ? void 0 : metadata.subtitle) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: metadata.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleFullscreen,\n                                    className: \"text-white hover:text-gray-300 p-2 transition-colors\",\n                                    title: \"Toggle Fullscreen\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: isFullscreen ? '⛶' : '⛶'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"text-white text-2xl hover:text-gray-300 transition-colors\",\n                                    title: \"Close Player\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex-1 w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                        src: src,\n                        className: \"w-full h-full border-0\",\n                        allowFullScreen: true,\n                        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen\",\n                        sandbox: \"allow-same-origin allow-scripts allow-popups allow-forms allow-pointer-lock allow-top-navigation\",\n                        referrerPolicy: \"no-referrer-when-downgrade\",\n                        title: title,\n                        onLoad: ()=>setIsLoading(false),\n                        style: {\n                            minHeight: '400px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                message: \"Loading video... Please wait while the stream loads\",\n                                size: \"lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black via-black/50 to-transparent p-4 transition-opacity duration-300 \".concat(showControls ? 'opacity-100' : 'opacity-0'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-white\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"⏸️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"\\uD83D\\uDD0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                (metadata === null || metadata === void 0 ? void 0 : metadata.progress) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: metadata.progress\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                (metadata === null || metadata === void 0 ? void 0 : metadata.duration) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: [\n                                        \"/ \",\n                                        metadata.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        controls && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                controls.previous && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: controls.previous,\n                                    disabled: controls.previousDisabled,\n                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded text-sm font-medium transition-all\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this),\n                                controls.next && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: controls.next,\n                                    disabled: controls.nextDisabled,\n                                    className: \"bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded text-sm font-medium transition-all\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"⚙️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleFullscreen,\n                                    className: \"bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: \"⛶\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                onClick: ()=>setShowControls(!showControls)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\siam web\\\\moviesite\\\\src\\\\components\\\\VideoPlayer.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoPlayer, \"4cZWXb6m0EbrMDxUYGZ1GvVTgGc=\");\n_c = VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/VideoPlayer.tsx\n"));

/***/ })

});