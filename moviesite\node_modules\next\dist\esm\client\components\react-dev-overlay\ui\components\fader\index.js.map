{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/fader/index.tsx"], "sourcesContent": ["import { type CSSProperties, type Ref, forwardRef } from 'react'\n\nexport const Fader = forwardRef(function Fader(\n  {\n    stop,\n    blur,\n    side,\n    style,\n    height,\n  }: {\n    stop?: string\n    blur?: string\n    height?: number\n    side: 'top' | 'bottom' | 'left' | 'right'\n    className?: string\n    style?: CSSProperties\n  },\n  ref: Ref<HTMLDivElement>\n) {\n  return (\n    <div\n      ref={ref}\n      aria-hidden\n      data-nextjs-scroll-fader\n      className=\"nextjs-scroll-fader\"\n      data-side={side}\n      style={\n        {\n          '--stop': stop,\n          '--blur': blur,\n          '--height': `${height}px`,\n          ...style,\n        } as React.CSSProperties\n      }\n    />\n  )\n})\n\nexport const FADER_STYLES = `\n  .nextjs-scroll-fader {\n    --blur: 1px;\n    --stop: 25%;\n    --height: 150px;\n    --color-bg: var(--color-background-100);\n    position: absolute;\n    pointer-events: none;\n    user-select: none;\n    width: 100%;\n    height: var(--height);\n    left: 0;\n    backdrop-filter: blur(var(--blur));\n\n    &[data-side=\"top\"] {\n      top: 0;\n      background: linear-gradient(to top, transparent, var(--color-bg));\n      mask-image: linear-gradient(to bottom, var(--color-bg) var(--stop), transparent);\n    }\n  }\n\n`\n"], "names": ["forwardRef", "Fader", "ref", "stop", "blur", "side", "style", "height", "div", "aria-hidden", "data-nextjs-scroll-fader", "className", "data-side", "FADER_STYLES"], "mappings": ";AAAA,SAAuCA,UAAU,QAAQ,QAAO;AAEhE,OAAO,MAAMC,sBAAQD,WAAW,SAASC,MACvC,KAaC,EACDC,GAAwB;IAdxB,IAAA,EACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EAQP,GAbD;IAgBA,qBACE,KAACC;QACCN,KAAKA;QACLO,aAAW;QACXC,0BAAwB;QACxBC,WAAU;QACVC,aAAWP;QACXC,OACE;YACE,UAAUH;YACV,UAAUC;YACV,YAAY,AAAC,KAAEG,SAAO;YACtB,GAAGD,KAAK;QACV;;AAIR,GAAE;AAEF,OAAO,MAAMO,eAAgB,ohBAqB5B"}