{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-wasm-loader.ts"], "sourcesContent": ["import { getModuleBuildInfo } from './get-module-build-info'\nimport crypto from 'crypto'\n\nfunction sha1(source: string | Buffer) {\n  return crypto.createHash('sha1').update(source).digest('hex')\n}\n\nexport default function MiddlewareWasmLoader(this: any, source: Buffer) {\n  const name = `wasm_${sha1(source)}`\n  const filePath = `edge-chunks/${name}.wasm`\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextWasmMiddlewareBinding = { filePath: `server/${filePath}`, name }\n  this.emitFile(`/${filePath}`, source, null)\n  return `module.exports = ${name};`\n}\n\nexport const raw = true\n"], "names": ["getModuleBuildInfo", "crypto", "sha1", "source", "createHash", "update", "digest", "MiddlewareWasmLoader", "name", "filePath", "buildInfo", "_module", "nextWasmMiddlewareBinding", "emitFile", "raw"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,0BAAyB;AAC5D,OAAOC,YAAY,SAAQ;AAE3B,SAASC,KAAKC,MAAuB;IACnC,OAAOF,OAAOG,UAAU,CAAC,QAAQC,MAAM,CAACF,QAAQG,MAAM,CAAC;AACzD;AAEA,eAAe,SAASC,qBAAgCJ,MAAc;IACpE,MAAMK,OAAO,CAAC,KAAK,EAAEN,KAAKC,SAAS;IACnC,MAAMM,WAAW,CAAC,YAAY,EAAED,KAAK,KAAK,CAAC;IAC3C,MAAME,YAAYV,mBAAmB,IAAI,CAACW,OAAO;IACjDD,UAAUE,yBAAyB,GAAG;QAAEH,UAAU,CAAC,OAAO,EAAEA,UAAU;QAAED;IAAK;IAC7E,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,EAAEJ,UAAU,EAAEN,QAAQ;IACtC,OAAO,CAAC,iBAAiB,EAAEK,KAAK,CAAC,CAAC;AACpC;AAEA,OAAO,MAAMM,MAAM,KAAI"}