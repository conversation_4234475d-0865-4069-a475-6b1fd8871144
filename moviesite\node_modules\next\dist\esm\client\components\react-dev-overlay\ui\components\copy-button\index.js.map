{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/copy-button/index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cx } from '../../utils/cx'\n\nfunction useCopyLegacy(content: string) {\n  type CopyState =\n    | {\n        state: 'initial'\n      }\n    | {\n        state: 'error'\n        error: unknown\n      }\n    | { state: 'success' }\n    | { state: 'pending' }\n\n  // This would be simpler with useActionState but we need to support React 18 here.\n  // React 18 also doesn't have async transitions.\n  const [copyState, dispatch] = React.useReducer(\n    (\n      state: CopyState,\n      action:\n        | { type: 'reset' | 'copied' | 'copying' }\n        | { type: 'error'; error: unknown }\n    ): CopyState => {\n      if (action.type === 'reset') {\n        return { state: 'initial' }\n      }\n      if (action.type === 'copied') {\n        return { state: 'success' }\n      }\n      if (action.type === 'copying') {\n        return { state: 'pending' }\n      }\n      if (action.type === 'error') {\n        return { state: 'error', error: action.error }\n      }\n      return state\n    },\n    {\n      state: 'initial',\n    }\n  )\n  function copy() {\n    if (isPending) {\n      return\n    }\n\n    if (!navigator.clipboard) {\n      dispatch({\n        type: 'error',\n        error: new Error('Copy to clipboard is not supported in this browser'),\n      })\n    } else {\n      dispatch({ type: 'copying' })\n      navigator.clipboard.writeText(content).then(\n        () => {\n          dispatch({ type: 'copied' })\n        },\n        (error) => {\n          dispatch({ type: 'error', error })\n        }\n      )\n    }\n  }\n  const reset = React.useCallback(() => {\n    dispatch({ type: 'reset' })\n  }, [])\n\n  const isPending = copyState.state === 'pending'\n\n  return [copyState, copy, reset, isPending] as const\n}\n\nfunction useCopyModern(content: string) {\n  type CopyState =\n    | {\n        state: 'initial'\n      }\n    | {\n        state: 'error'\n        error: unknown\n      }\n    | { state: 'success' }\n\n  const [copyState, dispatch, isPending] = React.useActionState(\n    (\n      state: CopyState,\n      action: 'reset' | 'copy'\n    ): CopyState | Promise<CopyState> => {\n      if (action === 'reset') {\n        return { state: 'initial' }\n      }\n      if (action === 'copy') {\n        if (!navigator.clipboard) {\n          return {\n            state: 'error',\n            error: new Error(\n              'Copy to clipboard is not supported in this browser'\n            ),\n          }\n        }\n        return navigator.clipboard.writeText(content).then(\n          () => {\n            return { state: 'success' }\n          },\n          (error) => {\n            return { state: 'error', error }\n          }\n        )\n      }\n      return state\n    },\n    {\n      state: 'initial',\n    }\n  )\n\n  function copy() {\n    React.startTransition(() => {\n      dispatch('copy')\n    })\n  }\n\n  const reset = React.useCallback(() => {\n    dispatch('reset')\n  }, [\n    // TODO: `dispatch` from `useActionState` is not reactive.\n    // Remove from dependencies once https://github.com/facebook/react/pull/29665 is released.\n    dispatch,\n  ])\n\n  return [copyState, copy, reset, isPending] as const\n}\n\nconst useCopy =\n  typeof React.useActionState === 'function' ? useCopyModern : useCopyLegacy\n\nexport function CopyButton({\n  actionLabel,\n  successLabel,\n  content,\n  icon,\n  disabled,\n  ...props\n}: React.HTMLProps<HTMLButtonElement> & {\n  actionLabel: string\n  successLabel: string\n  content: string\n  icon?: React.ReactNode\n}) {\n  const [copyState, copy, reset, isPending] = useCopy(content)\n\n  const error = copyState.state === 'error' ? copyState.error : null\n  React.useEffect(() => {\n    if (error !== null) {\n      // Additional console.error to get the stack.\n      console.error(error)\n    }\n  }, [error])\n  React.useEffect(() => {\n    if (copyState.state === 'success') {\n      const timeoutId = setTimeout(() => {\n        reset()\n      }, 2000)\n\n      return () => {\n        clearTimeout(timeoutId)\n      }\n    }\n  }, [isPending, copyState.state, reset])\n  const isDisabled = isPending || disabled\n  const label = copyState.state === 'success' ? successLabel : actionLabel\n\n  // Assign default icon\n  const renderedIcon =\n    copyState.state === 'success' ? (\n      <CopySuccessIcon />\n    ) : (\n      icon || (\n        <CopyIcon\n          width={14}\n          height={14}\n          className=\"error-overlay-toolbar-button-icon\"\n        />\n      )\n    )\n\n  return (\n    <button\n      {...props}\n      type=\"button\"\n      title={label}\n      aria-label={label}\n      aria-disabled={isDisabled}\n      disabled={isDisabled}\n      data-nextjs-copy-button\n      className={cx(\n        props.className,\n        'nextjs-data-copy-button',\n        `nextjs-data-copy-button--${copyState.state}`\n      )}\n      onClick={() => {\n        if (!isDisabled) {\n          copy()\n        }\n      }}\n    >\n      {renderedIcon}\n      {copyState.state === 'error' ? ` ${copyState.error}` : null}\n    </button>\n  )\n}\n\nfunction CopyIcon(props: React.SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M2.406.438c-.845 0-1.531.685-1.531 1.53v6.563c0 .846.686 1.531 1.531 1.531H3.937V8.75H2.406a.219.219 0 0 1-.219-.219V1.97c0-.121.098-.219.22-.219h4.812c.12 0 .218.098.218.219v.656H8.75v-.656c0-.846-.686-1.532-1.531-1.532H2.406zm4.375 3.5c-.845 0-1.531.685-1.531 1.53v6.563c0 .846.686 1.531 1.531 1.531h4.813c.845 0 1.531-.685 1.531-1.53V5.468c0-.846-.686-1.532-1.531-1.532H6.78zm-.218 1.53c0-.12.097-.218.218-.218h4.813c.12 0 .219.098.219.219v6.562c0 .121-.098.219-.22.219H6.782a.219.219 0 0 1-.218-.219V5.47z\"\n        fill=\"currentColor\"\n      />\n    </svg>\n  )\n}\n\nfunction CopySuccessIcon() {\n  return (\n    <svg\n      height=\"16\"\n      xlinkTitle=\"copied\"\n      viewBox=\"0 0 16 16\"\n      width=\"16\"\n      stroke=\"currentColor\"\n      fill=\"currentColor\"\n    >\n      <path d=\"M13.78 4.22a.75.75 0 0 1 0 1.06l-7.25 7.25a.75.75 0 0 1-1.06 0L2.22 9.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018L6 10.94l6.72-6.72a.75.75 0 0 1 1.06 0Z\" />\n    </svg>\n  )\n}\n\nexport const COPY_BUTTON_STYLES = `\n  .nextjs-data-copy-button {\n    color: inherit;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n  .nextjs-data-copy-button--initial:hover {\n    cursor: pointer;\n  }\n  .nextjs-data-copy-button--error,\n  .nextjs-data-copy-button--error:hover {\n    color: var(--color-ansi-red);\n  }\n  .nextjs-data-copy-button--success {\n    color: var(--color-ansi-green);\n  }\n`\n"], "names": ["React", "cx", "useCopyLegacy", "content", "copyState", "dispatch", "useReducer", "state", "action", "type", "error", "copy", "isPending", "navigator", "clipboard", "Error", "writeText", "then", "reset", "useCallback", "useCopyModern", "useActionState", "startTransition", "useCopy", "Copy<PERSON><PERSON><PERSON>", "actionLabel", "successLabel", "icon", "disabled", "props", "useEffect", "console", "timeoutId", "setTimeout", "clearTimeout", "isDisabled", "label", "renderedIcon", "CopySuccessIcon", "CopyIcon", "width", "height", "className", "button", "title", "aria-label", "aria-disabled", "data-nextjs-copy-button", "onClick", "svg", "viewBox", "fill", "xmlns", "path", "fillRule", "clipRule", "d", "xlinkTitle", "stroke", "COPY_BUTTON_STYLES"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,EAAE,QAAQ,iBAAgB;AAEnC,SAASC,cAAcC,OAAe;IAYpC,kFAAkF;IAClF,gDAAgD;IAChD,MAAM,CAACC,WAAWC,SAAS,GAAGL,MAAMM,UAAU,CAC5C,CACEC,OACAC;QAIA,IAAIA,OAAOC,IAAI,KAAK,SAAS;YAC3B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,UAAU;YAC5B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,WAAW;YAC7B,OAAO;gBAAEF,OAAO;YAAU;QAC5B;QACA,IAAIC,OAAOC,IAAI,KAAK,SAAS;YAC3B,OAAO;gBAAEF,OAAO;gBAASG,OAAOF,OAAOE,KAAK;YAAC;QAC/C;QACA,OAAOH;IACT,GACA;QACEA,OAAO;IACT;IAEF,SAASI;QACP,IAAIC,WAAW;YACb;QACF;QAEA,IAAI,CAACC,UAAUC,SAAS,EAAE;YACxBT,SAAS;gBACPI,MAAM;gBACNC,OAAO,qBAA+D,CAA/D,IAAIK,MAAM,uDAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8D;YACvE;QACF,OAAO;YACLV,SAAS;gBAAEI,MAAM;YAAU;YAC3BI,UAAUC,SAAS,CAACE,SAAS,CAACb,SAASc,IAAI,CACzC;gBACEZ,SAAS;oBAAEI,MAAM;gBAAS;YAC5B,GACA,CAACC;gBACCL,SAAS;oBAAEI,MAAM;oBAASC;gBAAM;YAClC;QAEJ;IACF;IACA,MAAMQ,QAAQlB,MAAMmB,WAAW,CAAC;QAC9Bd,SAAS;YAAEI,MAAM;QAAQ;IAC3B,GAAG,EAAE;IAEL,MAAMG,YAAYR,UAAUG,KAAK,KAAK;IAEtC,OAAO;QAACH;QAAWO;QAAMO;QAAON;KAAU;AAC5C;AAEA,SAASQ,cAAcjB,OAAe;IAWpC,MAAM,CAACC,WAAWC,UAAUO,UAAU,GAAGZ,MAAMqB,cAAc,CAC3D,CACEd,OACAC;QAEA,IAAIA,WAAW,SAAS;YACtB,OAAO;gBAAED,OAAO;YAAU;QAC5B;QACA,IAAIC,WAAW,QAAQ;YACrB,IAAI,CAACK,UAAUC,SAAS,EAAE;gBACxB,OAAO;oBACLP,OAAO;oBACPG,OAAO,qBAEN,CAFM,IAAIK,MACT,uDADK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEP;gBACF;YACF;YACA,OAAOF,UAAUC,SAAS,CAACE,SAAS,CAACb,SAASc,IAAI,CAChD;gBACE,OAAO;oBAAEV,OAAO;gBAAU;YAC5B,GACA,CAACG;gBACC,OAAO;oBAAEH,OAAO;oBAASG;gBAAM;YACjC;QAEJ;QACA,OAAOH;IACT,GACA;QACEA,OAAO;IACT;IAGF,SAASI;QACPX,MAAMsB,eAAe,CAAC;YACpBjB,SAAS;QACX;IACF;IAEA,MAAMa,QAAQlB,MAAMmB,WAAW,CAAC;QAC9Bd,SAAS;IACX,GAAG;QACD,0DAA0D;QAC1D,0FAA0F;QAC1FA;KACD;IAED,OAAO;QAACD;QAAWO;QAAMO;QAAON;KAAU;AAC5C;AAEA,MAAMW,UACJ,OAAOvB,MAAMqB,cAAc,KAAK,aAAaD,gBAAgBlB;AAE/D,OAAO,SAASsB,WAAW,KAY1B;IAZ0B,IAAA,EACzBC,WAAW,EACXC,YAAY,EACZvB,OAAO,EACPwB,IAAI,EACJC,QAAQ,EACR,GAAGC,OAMJ,GAZ0B;IAazB,MAAM,CAACzB,WAAWO,MAAMO,OAAON,UAAU,GAAGW,QAAQpB;IAEpD,MAAMO,QAAQN,UAAUG,KAAK,KAAK,UAAUH,UAAUM,KAAK,GAAG;IAC9DV,MAAM8B,SAAS,CAAC;QACd,IAAIpB,UAAU,MAAM;YAClB,6CAA6C;YAC7CqB,QAAQrB,KAAK,CAACA;QAChB;IACF,GAAG;QAACA;KAAM;IACVV,MAAM8B,SAAS,CAAC;QACd,IAAI1B,UAAUG,KAAK,KAAK,WAAW;YACjC,MAAMyB,YAAYC,WAAW;gBAC3Bf;YACF,GAAG;YAEH,OAAO;gBACLgB,aAAaF;YACf;QACF;IACF,GAAG;QAACpB;QAAWR,UAAUG,KAAK;QAAEW;KAAM;IACtC,MAAMiB,aAAavB,aAAagB;IAChC,MAAMQ,QAAQhC,UAAUG,KAAK,KAAK,YAAYmB,eAAeD;IAE7D,sBAAsB;IACtB,MAAMY,eACJjC,UAAUG,KAAK,KAAK,0BAClB,KAAC+B,uBAEDX,sBACE,KAACY;QACCC,OAAO;QACPC,QAAQ;QACRC,WAAU;;IAKlB,qBACE,MAACC;QACE,GAAGd,KAAK;QACTpB,MAAK;QACLmC,OAAOR;QACPS,cAAYT;QACZU,iBAAeX;QACfP,UAAUO;QACVY,yBAAuB;QACvBL,WAAWzC,GACT4B,MAAMa,SAAS,EACf,2BACA,AAAC,8BAA2BtC,UAAUG,KAAK;QAE7CyC,SAAS;YACP,IAAI,CAACb,YAAY;gBACfxB;YACF;QACF;;YAEC0B;YACAjC,UAAUG,KAAK,KAAK,UAAU,AAAC,MAAGH,UAAUM,KAAK,GAAK;;;AAG7D;AAEA,SAAS6B,SAASV,KAAoC;IACpD,qBACE,KAACoB;QACCT,OAAM;QACNC,QAAO;QACPS,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGvB,KAAK;kBAET,cAAA,KAACwB;YACCC,UAAS;YACTC,UAAS;YACTC,GAAE;YACFL,MAAK;;;AAIb;AAEA,SAASb;IACP,qBACE,KAACW;QACCR,QAAO;QACPgB,YAAW;QACXP,SAAQ;QACRV,OAAM;QACNkB,QAAO;QACPP,MAAK;kBAEL,cAAA,KAACE;YAAKG,GAAE;;;AAGd;AAEA,OAAO,MAAMG,qBAAsB,8ZAmBlC"}