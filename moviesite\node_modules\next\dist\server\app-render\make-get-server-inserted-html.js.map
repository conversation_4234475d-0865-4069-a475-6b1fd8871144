{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "sourcesContent": ["import React, { type JSX } from 'react'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { renderToReadableStream } from 'react-dom/server.edge'\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport type { ClientTraceDataEntry } from '../lib/trace/tracer'\n\nexport function makeGetServerInsertedHTML({\n  polyfills,\n  renderServerInsertedHTML,\n  serverCapturedErrors,\n  tracingMetadata,\n  basePath,\n}: {\n  polyfills: JSX.IntrinsicElements['script'][]\n  renderServerInsertedHTML: () => React.ReactNode\n  tracingMetadata: ClientTraceDataEntry[] | undefined\n  serverCapturedErrors: Array<unknown>\n  basePath: string\n}) {\n  let flushedErrorMetaTagsUntilIndex = 0\n  // flag for static content that only needs to be flushed once\n  let hasFlushedInitially = false\n\n  const polyfillTags = polyfills.map((polyfill) => {\n    return <script key={polyfill.src} {...polyfill} />\n  })\n\n  return async function getServerInsertedHTML() {\n    // Loop through all the errors that have been captured but not yet\n    // flushed.\n    const errorMetaTags = []\n    while (flushedErrorMetaTagsUntilIndex < serverCapturedErrors.length) {\n      const error = serverCapturedErrors[flushedErrorMetaTagsUntilIndex]\n      flushedErrorMetaTagsUntilIndex++\n\n      if (isHTTPAccessFallbackError(error)) {\n        errorMetaTags.push(\n          <meta name=\"robots\" content=\"noindex\" key={error.digest} />,\n          process.env.NODE_ENV === 'development' ? (\n            <meta name=\"next-error\" content=\"not-found\" key=\"next-error\" />\n          ) : null\n        )\n      } else if (isRedirectError(error)) {\n        const redirectUrl = addPathPrefix(\n          getURLFromRedirectError(error),\n          basePath\n        )\n        const statusCode = getRedirectStatusCodeFromError(error)\n        const isPermanent =\n          statusCode === RedirectStatusCode.PermanentRedirect ? true : false\n        if (redirectUrl) {\n          errorMetaTags.push(\n            <meta\n              id=\"__next-page-redirect\"\n              httpEquiv=\"refresh\"\n              content={`${isPermanent ? 0 : 1};url=${redirectUrl}`}\n              key={error.digest}\n            />\n          )\n        }\n      }\n    }\n\n    const traceMetaTags = (tracingMetadata || []).map(\n      ({ key, value }, index) => (\n        <meta key={`next-trace-data-${index}`} name={key} content={value} />\n      )\n    )\n\n    const serverInsertedHTML = renderServerInsertedHTML()\n\n    // Skip React rendering if we know the content is empty.\n    if (\n      polyfillTags.length === 0 &&\n      traceMetaTags.length === 0 &&\n      errorMetaTags.length === 0 &&\n      Array.isArray(serverInsertedHTML) &&\n      serverInsertedHTML.length === 0\n    ) {\n      return ''\n    }\n\n    const stream = await renderToReadableStream(\n      <>\n        {\n          /* Insert the polyfills if they haven't been flushed yet. */\n          hasFlushedInitially ? null : polyfillTags\n        }\n        {serverInsertedHTML}\n        {hasFlushedInitially ? null : traceMetaTags}\n        {errorMetaTags}\n      </>,\n      {\n        // Larger chunk because this isn't sent over the network.\n        // Let's set it to 1MB.\n        progressiveChunkSize: 1024 * 1024,\n      }\n    )\n\n    hasFlushedInitially = true\n\n    // There's no need to wait for the stream to be ready\n    // e.g. calling `await stream.allReady` because `streamToString` will\n    // wait and decode the stream progressively with better parallelism.\n    return streamToString(stream)\n  }\n}\n"], "names": ["makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "serverCapturedErrors", "tracingMetadata", "basePath", "flushedErrorMetaTagsUntilIndex", "hasFlushedInitially", "polyfillTags", "map", "polyfill", "script", "src", "getServerInsertedHTML", "errorMetaTags", "length", "error", "isHTTPAccessFallbackError", "push", "meta", "name", "content", "digest", "process", "env", "NODE_ENV", "isRedirectError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "statusCode", "getRedirectStatusCodeFromError", "isPermanent", "RedirectStatusCode", "PermanentRedirect", "id", "httpEquiv", "traceMetaTags", "key", "value", "index", "serverInsertedHTML", "Array", "isArray", "stream", "renderToReadableStream", "progressiveChunkSize", "streamToString"], "mappings": ";;;;+<PERSON>ag<PERSON>;;;eAAAA;;;;8DAbgB;oCACU;0BAInC;+BACyB;4BACO;sCACR;oCACI;+BACL;;;;;;AAGvB,SAASA,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,oBAAoB,EACpBC,eAAe,EACfC,QAAQ,EAOT;IACC,IAAIC,iCAAiC;IACrC,6DAA6D;IAC7D,IAAIC,sBAAsB;IAE1B,MAAMC,eAAeP,UAAUQ,GAAG,CAAC,CAACC;QAClC,qBAAO,qBAACC;YAA2B,GAAGD,QAAQ;WAA1BA,SAASE,GAAG;IAClC;IAEA,OAAO,eAAeC;QACpB,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOR,iCAAiCH,qBAAqBY,MAAM,CAAE;YACnE,MAAMC,QAAQb,oBAAoB,CAACG,+BAA+B;YAClEA;YAEA,IAAIW,IAAAA,6CAAyB,EAACD,QAAQ;gBACpCF,cAAcI,IAAI,eAChB,qBAACC;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeL,MAAMM,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,qBAACN;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIK,IAAAA,8BAAe,EAACV,QAAQ;gBACjC,MAAMW,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACb,QACxBX;gBAEF,MAAMyB,aAAaC,IAAAA,wCAA8B,EAACf;gBAClD,MAAMgB,cACJF,eAAeG,sCAAkB,CAACC,iBAAiB,GAAG,OAAO;gBAC/D,IAAIP,aAAa;oBACfb,cAAcI,IAAI,eAChB,qBAACC;wBACCgB,IAAG;wBACHC,WAAU;wBACVf,SAAS,GAAGW,cAAc,IAAI,EAAE,KAAK,EAAEL,aAAa;uBAC/CX,MAAMM,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMe,gBAAgB,AAACjC,CAAAA,mBAAmB,EAAE,AAAD,EAAGK,GAAG,CAC/C,CAAC,EAAE6B,GAAG,EAAEC,KAAK,EAAE,EAAEC,sBACf,qBAACrB;gBAAsCC,MAAMkB;gBAAKjB,SAASkB;eAAhD,CAAC,gBAAgB,EAAEC,OAAO;QAIzC,MAAMC,qBAAqBvC;QAE3B,wDAAwD;QACxD,IACEM,aAAaO,MAAM,KAAK,KACxBsB,cAActB,MAAM,KAAK,KACzBD,cAAcC,MAAM,KAAK,KACzB2B,MAAMC,OAAO,CAACF,uBACdA,mBAAmB1B,MAAM,KAAK,GAC9B;YACA,OAAO;QACT;QAEA,MAAM6B,SAAS,MAAMC,IAAAA,kCAAsB,gBACzC;;gBAEI,0DAA0D,GAC1DtC,sBAAsB,OAAOC;gBAE9BiC;gBACAlC,sBAAsB,OAAO8B;gBAC7BvB;;YAEH;YACE,yDAAyD;YACzD,uBAAuB;YACvBgC,sBAAsB,OAAO;QAC/B;QAGFvC,sBAAsB;QAEtB,qDAAqD;QACrD,qEAAqE;QACrE,oEAAoE;QACpE,OAAOwC,IAAAA,oCAAc,EAACH;IACxB;AACF"}