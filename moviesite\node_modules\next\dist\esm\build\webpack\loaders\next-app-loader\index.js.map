{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/index.ts"], "sourcesContent": ["import type webpack from 'next/dist/compiled/webpack/webpack'\nimport {\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  type ValueOf,\n} from '../../../../shared/lib/constants'\nimport type { ModuleTuple, CollectedMetadata } from '../metadata/types'\n\nimport path from 'path'\nimport { bold } from '../../../../lib/picocolors'\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport { verifyRootLayout } from '../../../../lib/verify-root-layout'\nimport * as Log from '../../../output/log'\nimport { APP_DIR_ALIAS } from '../../../../lib/constants'\nimport {\n  createMetadataExportsCode,\n  createStaticMetadataFromRoute,\n} from '../metadata/discover'\nimport { promises as fs } from 'fs'\nimport { isAppRouteRoute } from '../../../../lib/is-app-route-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\nimport { isAppBuiltinNotFoundPage } from '../../../utils'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../../shared/lib/segment'\nimport { getFilesInDir } from '../../../../lib/get-files-in-dir'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../../../client/components/parallel-route-default'\nimport type { Compilation } from 'webpack'\nimport { createAppRouteCode } from './create-app-route-code'\n\nexport type AppLoaderOptions = {\n  name: string\n  page: string\n  pagePath: string\n  appDir: string\n  appPaths: readonly string[] | null\n  preferredRegion: string | string[] | undefined\n  pageExtensions: PageExtensions\n  assetPrefix: string\n  rootDir?: string\n  tsconfigPath?: string\n  isDev?: true\n  basePath: string\n  nextConfigOutput?: NextConfig['output']\n  nextConfigExperimentalUseEarlyImport?: true\n  middlewareConfig: string\n}\ntype AppLoader = webpack.LoaderDefinitionFunction<AppLoaderOptions>\n\nconst HTTP_ACCESS_FALLBACKS = {\n  'not-found': 'not-found',\n  forbidden: 'forbidden',\n  unauthorized: 'unauthorized',\n} as const\nconst defaultHTTPAccessFallbackPaths = {\n  'not-found': 'next/dist/client/components/not-found-error',\n  forbidden: 'next/dist/client/components/forbidden-error',\n  unauthorized: 'next/dist/client/components/unauthorized-error',\n} as const\n\nconst FILE_TYPES = {\n  layout: 'layout',\n  template: 'template',\n  error: 'error',\n  loading: 'loading',\n  'global-error': 'global-error',\n  ...HTTP_ACCESS_FALLBACKS,\n} as const\n\nconst GLOBAL_ERROR_FILE_TYPE = 'global-error'\nconst PAGE_SEGMENT = 'page$'\nconst PARALLEL_CHILDREN_SEGMENT = 'children$'\n\nconst defaultGlobalErrorPath = 'next/dist/client/components/error-boundary'\nconst defaultLayoutPath = 'next/dist/client/components/default-layout'\n\ntype DirResolver = (pathToResolve: string) => string\ntype PathResolver = (\n  pathname: string\n) => Promise<string | undefined> | string | undefined\nexport type MetadataResolver = (\n  dir: string,\n  filename: string,\n  extensions: readonly string[]\n) => Promise<string | undefined>\n\nexport type AppDirModules = {\n  readonly [moduleKey in ValueOf<typeof FILE_TYPES>]?: ModuleTuple\n} & {\n  readonly page?: ModuleTuple\n} & {\n  readonly metadata?: CollectedMetadata\n} & {\n  readonly defaultPage?: ModuleTuple\n}\n\nconst normalizeParallelKey = (key: string) =>\n  key.startsWith('@') ? key.slice(1) : key\n\nconst isDirectory = async (pathname: string) => {\n  try {\n    const stat = await fs.stat(pathname)\n    return stat.isDirectory()\n  } catch (err) {\n    return false\n  }\n}\n\nasync function createTreeCodeFromPath(\n  pagePath: string,\n  {\n    page,\n    resolveDir,\n    resolver,\n    resolveParallelSegments,\n    metadataResolver,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n  }: {\n    page: string\n    resolveDir: DirResolver\n    resolver: PathResolver\n    metadataResolver: MetadataResolver\n    resolveParallelSegments: (\n      pathname: string\n    ) => [key: string, segment: string | string[]][]\n    loaderContext: webpack.LoaderContext<AppLoaderOptions>\n    pageExtensions: PageExtensions\n    basePath: string\n    collectedDeclarations: [string, string][]\n  }\n): Promise<{\n  treeCode: string\n  pages: string\n  rootLayout: string | undefined\n  globalError: string\n}> {\n  const splittedPath = pagePath.split(/[\\\\/]/, 1)\n  const isNotFoundRoute = page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n\n  const isDefaultNotFound = isAppBuiltinNotFoundPage(pagePath)\n  const appDirPrefix = isDefaultNotFound ? APP_DIR_ALIAS : splittedPath[0]\n  const pages: string[] = []\n\n  let rootLayout: string | undefined\n  let globalError: string | undefined\n\n  async function resolveAdjacentParallelSegments(\n    segmentPath: string\n  ): Promise<string[]> {\n    const absoluteSegmentPath = await resolveDir(\n      `${appDirPrefix}${segmentPath}`\n    )\n\n    if (!absoluteSegmentPath) {\n      return []\n    }\n\n    const segmentIsDirectory = await isDirectory(absoluteSegmentPath)\n\n    if (!segmentIsDirectory) {\n      return []\n    }\n\n    // We need to resolve all parallel routes in this level.\n    const files = await fs.opendir(absoluteSegmentPath)\n\n    const parallelSegments: string[] = ['children']\n\n    for await (const dirent of files) {\n      // Make sure name starts with \"@\" and is a directory.\n      if (dirent.isDirectory() && dirent.name.charCodeAt(0) === 64) {\n        parallelSegments.push(dirent.name)\n      }\n    }\n\n    return parallelSegments\n  }\n\n  async function createSubtreePropsFromSegmentPath(\n    segments: string[],\n    nestedCollectedDeclarations: [string, string][]\n  ): Promise<{\n    treeCode: string\n  }> {\n    const segmentPath = segments.join('/')\n\n    // Existing tree are the children of the current segment\n    const props: Record<string, string> = {}\n    // Root layer could be 1st layer of normal routes\n    const isRootLayer = segments.length === 0\n    const isRootLayoutOrRootPage = segments.length <= 1\n\n    // We need to resolve all parallel routes in this level.\n    const parallelSegments: [key: string, segment: string | string[]][] = []\n    if (isRootLayer) {\n      parallelSegments.push(['children', ''])\n    } else {\n      parallelSegments.push(...resolveParallelSegments(segmentPath))\n    }\n\n    let metadata: Awaited<ReturnType<typeof createStaticMetadataFromRoute>> =\n      null\n    const routerDirPath = `${appDirPrefix}${segmentPath}`\n    // For default not-found, don't traverse the directory to find metadata.\n    const resolvedRouteDir = isDefaultNotFound\n      ? ''\n      : await resolveDir(routerDirPath)\n\n    if (resolvedRouteDir) {\n      metadata = await createStaticMetadataFromRoute(resolvedRouteDir, {\n        basePath,\n        segment: segmentPath,\n        metadataResolver,\n        isRootLayoutOrRootPage,\n        pageExtensions,\n      })\n    }\n\n    for (const [parallelKey, parallelSegment] of parallelSegments) {\n      // if parallelSegment is the page segment (ie, `page$` and not ['page$']), it gets loaded into the __PAGE__ slot\n      // as it's the page for the current route.\n      if (parallelSegment === PAGE_SEGMENT) {\n        const matchedPagePath = `${appDirPrefix}${segmentPath}${\n          parallelKey === 'children' ? '' : `/${parallelKey}`\n        }/page`\n\n        const resolvedPagePath = await resolver(matchedPagePath)\n        if (resolvedPagePath) {\n          pages.push(resolvedPagePath)\n\n          const varName = `page${nestedCollectedDeclarations.length}`\n          nestedCollectedDeclarations.push([varName, resolvedPagePath])\n\n          // Use '' for segment as it's the page. There can't be a segment called '' so this is the safest way to add it.\n          props[normalizeParallelKey(parallelKey)] =\n            `['${PAGE_SEGMENT_KEY}', {}, {\n          page: [${varName}, ${JSON.stringify(resolvedPagePath)}],\n          ${createMetadataExportsCode(metadata)}\n        }]`\n          continue\n        }\n      }\n\n      // if the parallelSegment was not matched to the __PAGE__ slot, then it's a parallel route at this level.\n      // the code below recursively traverses the parallel slots directory to match the corresponding __PAGE__ for each parallel slot\n      // while also filling in layout/default/etc files into the loader tree at each segment level.\n\n      const subSegmentPath = [...segments]\n      if (parallelKey !== 'children') {\n        // A `children` parallel key should have already been processed in the above segment\n        // So we exclude it when constructing the subsegment path for the remaining segment levels\n        subSegmentPath.push(parallelKey)\n      }\n\n      const normalizedParallelSegment = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      if (\n        normalizedParallelSegment !== PAGE_SEGMENT &&\n        normalizedParallelSegment !== PARALLEL_CHILDREN_SEGMENT\n      ) {\n        // If we don't have a page segment, nor a special $children marker, it means we need to traverse the next directory\n        // (ie, `normalizedParallelSegment` would correspond with the folder that contains the next level of pages/layout/etc)\n        // we push it to the subSegmentPath so that we can fill in the loader tree for that segment.\n        subSegmentPath.push(normalizedParallelSegment)\n      }\n\n      const parallelSegmentPath = subSegmentPath.join('/')\n\n      // Fill in the loader tree for all of the special files types (layout, default, etc) at this level\n      // `page` is not included here as it's added above.\n      const filePaths = await Promise.all(\n        Object.values(FILE_TYPES).map(async (file) => {\n          return [\n            file,\n            await resolver(\n              `${appDirPrefix}${\n                // TODO-APP: parallelSegmentPath sometimes ends in `/` but sometimes it doesn't. This should be consistent.\n                parallelSegmentPath.endsWith('/')\n                  ? parallelSegmentPath\n                  : parallelSegmentPath + '/'\n              }${file}`\n            ),\n          ] as const\n        })\n      )\n\n      const definedFilePaths = filePaths.filter(\n        ([, filePath]) => filePath !== undefined\n      ) as [ValueOf<typeof FILE_TYPES>, string][]\n\n      // Add default access fallback as root fallback if not present\n      const existedConventionNames = new Set(\n        definedFilePaths.map(([type]) => type)\n      )\n      // If the first layer is a group route, we treat it as root layer\n      const isFirstLayerGroupRoute =\n        segments.length === 1 &&\n        subSegmentPath.filter((seg) => isGroupSegment(seg)).length === 1\n\n      if (isRootLayer || isFirstLayerGroupRoute) {\n        const accessFallbackTypes = Object.keys(\n          defaultHTTPAccessFallbackPaths\n        ) as (keyof typeof defaultHTTPAccessFallbackPaths)[]\n        for (const type of accessFallbackTypes) {\n          const hasRootFallbackFile = await resolver(\n            `${appDirPrefix}/${FILE_TYPES[type]}`\n          )\n          const hasLayerFallbackFile = existedConventionNames.has(type)\n\n          // If you already have a root access error fallback, don't insert default access error boundary to group routes root\n          if (\n            // Is treated as root layout and without boundary\n            !(hasRootFallbackFile && isFirstLayerGroupRoute) &&\n            // Does not have a fallback boundary file\n            !hasLayerFallbackFile\n          ) {\n            const defaultFallbackPath = defaultHTTPAccessFallbackPaths[type]\n            definedFilePaths.push([type, defaultFallbackPath])\n          }\n        }\n      }\n\n      if (!rootLayout) {\n        const layoutPath = definedFilePaths.find(\n          ([type]) => type === 'layout'\n        )?.[1]\n        rootLayout = layoutPath\n\n        if (isDefaultNotFound && !layoutPath && !rootLayout) {\n          rootLayout = defaultLayoutPath\n          definedFilePaths.push(['layout', rootLayout])\n        }\n      }\n\n      if (!globalError) {\n        const resolvedGlobalErrorPath = await resolver(\n          `${appDirPrefix}/${GLOBAL_ERROR_FILE_TYPE}`\n        )\n        if (resolvedGlobalErrorPath) {\n          globalError = resolvedGlobalErrorPath\n        }\n      }\n\n      let parallelSegmentKey = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      // normalize the parallel segment key to remove any special markers that we inserted in the\n      // earlier logic (such as children$ and page$). These should never appear in the loader tree, and\n      // should instead be the corresponding segment keys (ie `__PAGE__`) or the `children` parallel route.\n      parallelSegmentKey =\n        parallelSegmentKey === PARALLEL_CHILDREN_SEGMENT\n          ? 'children'\n          : parallelSegmentKey === PAGE_SEGMENT\n            ? PAGE_SEGMENT_KEY\n            : parallelSegmentKey\n\n      const normalizedParallelKey = normalizeParallelKey(parallelKey)\n      let subtreeCode\n      // If it's root not found page, set not-found boundary as children page\n      if (isNotFoundRoute && normalizedParallelKey === 'children') {\n        const notFoundPath =\n          definedFilePaths.find(([type]) => type === 'not-found')?.[1] ??\n          defaultHTTPAccessFallbackPaths['not-found']\n\n        const varName = `notFound${nestedCollectedDeclarations.length}`\n        nestedCollectedDeclarations.push([varName, notFoundPath])\n        subtreeCode = `{\n          children: [${JSON.stringify(UNDERSCORE_NOT_FOUND_ROUTE)}, {\n            children: ['${PAGE_SEGMENT_KEY}', {}, {\n              page: [\n                ${varName},\n                ${JSON.stringify(notFoundPath)}\n              ]\n            }]\n          }, {}]\n        }`\n      }\n\n      const modulesCode = `{\n        ${definedFilePaths\n          .map(([file, filePath]) => {\n            const varName = `module${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, filePath])\n            return `'${file}': [${varName}, ${JSON.stringify(filePath)}],`\n          })\n          .join('\\n')}\n        ${createMetadataExportsCode(metadata)}\n      }`\n\n      if (!subtreeCode) {\n        const { treeCode: pageSubtreeCode } =\n          await createSubtreePropsFromSegmentPath(\n            subSegmentPath,\n            nestedCollectedDeclarations\n          )\n\n        subtreeCode = pageSubtreeCode\n      }\n\n      props[normalizedParallelKey] = `[\n        '${parallelSegmentKey}',\n        ${subtreeCode},\n        ${modulesCode}\n      ]`\n    }\n\n    const adjacentParallelSegments =\n      await resolveAdjacentParallelSegments(segmentPath)\n\n    for (const adjacentParallelSegment of adjacentParallelSegments) {\n      if (!props[normalizeParallelKey(adjacentParallelSegment)]) {\n        const actualSegment =\n          adjacentParallelSegment === 'children'\n            ? ''\n            : `/${adjacentParallelSegment}`\n\n        // if a default is found, use that. Otherwise use the fallback, which will trigger a `notFound()`\n        const defaultPath =\n          (await resolver(\n            `${appDirPrefix}${segmentPath}${actualSegment}/default`\n          )) ?? PARALLEL_ROUTE_DEFAULT_PATH\n\n        const varName = `default${nestedCollectedDeclarations.length}`\n        nestedCollectedDeclarations.push([varName, defaultPath])\n        props[normalizeParallelKey(adjacentParallelSegment)] = `[\n          '${DEFAULT_SEGMENT_KEY}',\n          {},\n          {\n            defaultPage: [${varName}, ${JSON.stringify(defaultPath)}],\n          }\n        ]`\n      }\n    }\n    return {\n      treeCode: `{\n        ${Object.entries(props)\n          .map(([key, value]) => `${key}: ${value}`)\n          .join(',\\n')}\n      }`,\n    }\n  }\n\n  const { treeCode } = await createSubtreePropsFromSegmentPath(\n    [],\n    collectedDeclarations\n  )\n\n  return {\n    treeCode: `${treeCode}.children;`,\n    pages: `${JSON.stringify(pages)};`,\n    rootLayout,\n    globalError: globalError ?? defaultGlobalErrorPath,\n  }\n}\n\nfunction createAbsolutePath(appDir: string, pathToTurnAbsolute: string) {\n  return (\n    pathToTurnAbsolute\n      // Replace all POSIX path separators with the current OS path separator\n      .replace(/\\//g, path.sep)\n      .replace(/^private-next-app-dir/, appDir)\n  )\n}\n\nconst filesInDirMapMap: WeakMap<\n  Compilation,\n  Map<string, Promise<Set<string>>>\n> = new WeakMap()\nconst nextAppLoader: AppLoader = async function nextAppLoader() {\n  const loaderOptions = this.getOptions()\n  const {\n    name,\n    appDir,\n    appPaths,\n    pagePath,\n    pageExtensions,\n    rootDir,\n    tsconfigPath,\n    isDev,\n    nextConfigOutput,\n    preferredRegion,\n    basePath,\n    middlewareConfig: middlewareConfigBase64,\n    nextConfigExperimentalUseEarlyImport,\n  } = loaderOptions\n\n  const buildInfo = getModuleBuildInfo((this as any)._module)\n  const collectedDeclarations: [string, string][] = []\n  const page = name.replace(/^app/, '')\n  const middlewareConfig: MiddlewareConfig = JSON.parse(\n    Buffer.from(middlewareConfigBase64, 'base64').toString()\n  )\n  buildInfo.route = {\n    page,\n    absolutePagePath: createAbsolutePath(appDir, pagePath),\n    preferredRegion,\n    middlewareConfig,\n    relatedModules: [],\n  }\n\n  const extensions = pageExtensions.map((extension) => `.${extension}`)\n\n  const normalizedAppPaths =\n    typeof appPaths === 'string' ? [appPaths] : appPaths || []\n\n  const resolveParallelSegments = (\n    pathname: string\n  ): [string, string | string[]][] => {\n    const matched: Record<string, string | string[]> = {}\n    let existingChildrenPath: string | undefined\n    for (const appPath of normalizedAppPaths) {\n      if (appPath.startsWith(pathname + '/')) {\n        const rest = appPath.slice(pathname.length + 1).split('/')\n\n        // It is the actual page, mark it specially.\n        if (rest.length === 1 && rest[0] === 'page') {\n          existingChildrenPath = appPath\n          matched.children = PAGE_SEGMENT\n          continue\n        }\n\n        const isParallelRoute = rest[0].startsWith('@')\n        if (isParallelRoute) {\n          if (rest.length === 2 && rest[1] === 'page') {\n            // We found a parallel route at this level. We don't want to mark it explicitly as the page segment,\n            // as that should be matched to the `children` slot. Instead, we use an array, to signal to `createSubtreePropsFromSegmentPath`\n            // that it needs to recursively fill in the loader tree code for the parallel route at the appropriate levels.\n            matched[rest[0]] = [PAGE_SEGMENT]\n            continue\n          }\n          // If it was a parallel route but we weren't able to find the page segment (ie, maybe the page is nested further)\n          // we first insert a special marker to ensure that we still process layout/default/etc at the slot level prior to continuing\n          // on to the page segment.\n          matched[rest[0]] = [PARALLEL_CHILDREN_SEGMENT, ...rest.slice(1)]\n          continue\n        }\n\n        if (existingChildrenPath && matched.children !== rest[0]) {\n          // If we get here, it means we already set a `page` segment earlier in the loop,\n          // meaning we already matched a page to the `children` parallel segment.\n          const isIncomingParallelPage = appPath.includes('@')\n          const hasCurrentParallelPage = existingChildrenPath.includes('@')\n\n          if (isIncomingParallelPage) {\n            // The duplicate segment was for a parallel slot. In this case,\n            // rather than throwing an error, we can ignore it since this can happen for valid reasons.\n            // For example, when we attempt to normalize catch-all routes, we'll push potential slot matches so\n            // that they are available in the loader tree when we go to render the page.\n            // We only need to throw an error if the duplicate segment was for a regular page.\n            // For example, /app/(groupa)/page & /app/(groupb)/page is an error since it corresponds\n            // with the same path.\n            continue\n          } else if (!hasCurrentParallelPage && !isIncomingParallelPage) {\n            // Both the current `children` and the incoming `children` are regular pages.\n            throw new Error(\n              `You cannot have two parallel pages that resolve to the same path. Please check ${existingChildrenPath} and ${appPath}. Refer to the route group docs for more information: https://nextjs.org/docs/app/building-your-application/routing/route-groups`\n            )\n          }\n        }\n\n        existingChildrenPath = appPath\n        matched.children = rest[0]\n      }\n    }\n\n    return Object.entries(matched)\n  }\n\n  const resolveDir: DirResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  const resolveAppRoute: PathResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  // Cached checker to see if a file exists in a given directory.\n  // This can be more efficient than checking them with `fs.stat` one by one\n  // because all the thousands of files are likely in a few possible directories.\n  // Note that it should only be cached for this compilation, not globally.\n  const fileExistsInDirectory = async (dirname: string, fileName: string) => {\n    // I don't think we should ever hit this code path, but if we do we should handle it gracefully.\n    if (this._compilation === undefined) {\n      try {\n        return (await getFilesInDir(dirname).catch(() => new Set())).has(\n          fileName\n        )\n      } catch (e) {\n        return false\n      }\n    }\n    const map =\n      filesInDirMapMap.get(this._compilation) ||\n      new Map<string, Promise<Set<string>>>()\n    if (!filesInDirMapMap.has(this._compilation)) {\n      filesInDirMapMap.set(this._compilation, map)\n    }\n    if (!map.has(dirname)) {\n      map.set(\n        dirname,\n        getFilesInDir(dirname).catch(() => new Set())\n      )\n    }\n    return ((await map.get(dirname)) || new Set()).has(fileName)\n  }\n\n  const resolver: PathResolver = async (pathname) => {\n    const absolutePath = createAbsolutePath(appDir, pathname)\n\n    const filenameIndex = absolutePath.lastIndexOf(path.sep)\n    const dirname = absolutePath.slice(0, filenameIndex)\n    const filename = absolutePath.slice(filenameIndex + 1)\n\n    let result: string | undefined\n\n    for (const ext of extensions) {\n      const absolutePathWithExtension = `${absolutePath}${ext}`\n      if (\n        !result &&\n        (await fileExistsInDirectory(dirname, `${filename}${ext}`))\n      ) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  const metadataResolver: MetadataResolver = async (\n    dirname,\n    filename,\n    exts\n  ) => {\n    const absoluteDir = createAbsolutePath(appDir, dirname)\n\n    let result: string | undefined\n\n    for (const ext of exts) {\n      // Compared to `resolver` above the exts do not have the `.` included already, so it's added here.\n      const filenameWithExt = `${filename}.${ext}`\n      const absolutePathWithExtension = `${absoluteDir}${path.sep}${filenameWithExt}`\n      if (!result && (await fileExistsInDirectory(dirname, filenameWithExt))) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  if (isAppRouteRoute(name)) {\n    return createAppRouteCode({\n      appDir,\n      // TODO: investigate if the local `page` is the same as the loaderOptions.page\n      page: loaderOptions.page,\n      name,\n      pagePath,\n      resolveAppRoute,\n      pageExtensions,\n      nextConfigOutput,\n    })\n  }\n\n  let treeCodeResult = await createTreeCodeFromPath(pagePath, {\n    page,\n    resolveDir,\n    resolver,\n    metadataResolver,\n    resolveParallelSegments,\n    loaderContext: this,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n  })\n\n  if (!treeCodeResult.rootLayout) {\n    if (!isDev) {\n      // If we're building and missing a root layout, exit the build\n      Log.error(\n        `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. To fix this error, make sure every page has a root layout.`\n      )\n      process.exit(1)\n    } else {\n      // In dev we'll try to create a root layout\n      const [createdRootLayout, rootLayoutPath] = await verifyRootLayout({\n        appDir: appDir,\n        dir: rootDir!,\n        tsconfigPath: tsconfigPath!,\n        pagePath,\n        pageExtensions,\n      })\n      if (!createdRootLayout) {\n        let message = `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. `\n\n        if (rootLayoutPath) {\n          message += `We tried to create ${bold(\n            path.relative(this._compiler?.context ?? '', rootLayoutPath)\n          )} for you but something went wrong.`\n        } else {\n          message +=\n            'To fix this error, make sure every page has a root layout.'\n        }\n\n        throw new Error(message)\n      }\n\n      // Clear fs cache, get the new result with the created root layout.\n      if (this._compilation) filesInDirMapMap.get(this._compilation)?.clear()\n      treeCodeResult = await createTreeCodeFromPath(pagePath, {\n        page,\n        resolveDir,\n        resolver,\n        metadataResolver,\n        resolveParallelSegments,\n        loaderContext: this,\n        pageExtensions,\n        basePath,\n        collectedDeclarations,\n      })\n    }\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n\n  // Prefer to modify next/src/server/app-render/entry-base.ts since this is shared with Turbopack.\n  // Any changes to this code should be reflected in Turbopack's app_source.rs and/or app-renderer.tsx as well.\n  const code = await loadEntrypoint(\n    'app-page',\n    {\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_MODULE_GLOBAL_ERROR: treeCodeResult.globalError,\n    },\n    {\n      tree: treeCodeResult.treeCode,\n      pages: treeCodeResult.pages,\n      __next_app_require__: '__webpack_require__',\n      // all modules are in the entry chunk, so we never actually need to load chunks in webpack\n      __next_app_load_chunk__: '() => Promise.resolve()',\n    }\n  )\n\n  const header =\n    nextConfigExperimentalUseEarlyImport &&\n    process.env.NODE_ENV === 'production'\n      ? // Evaluate the imported modules early in the generated code\n        collectedDeclarations\n          .map(([varName, modulePath]) => {\n            return `import * as ${varName}_ from ${JSON.stringify(\n              modulePath\n            )};\\nconst ${varName} = () => ${varName}_;\\n`\n          })\n          .join('')\n      : // Lazily evaluate the imported modules in the generated code\n        collectedDeclarations\n          .map(([varName, modulePath]) => {\n            return `const ${varName} = () => import(/* webpackMode: \"eager\" */ ${JSON.stringify(\n              modulePath\n            )});\\n`\n          })\n          .join('')\n\n  return header + code\n}\n\nexport default nextAppLoader\n"], "names": ["UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "path", "bold", "getModuleBuildInfo", "verifyRootLayout", "Log", "APP_DIR_ALIAS", "createMetadataExportsCode", "createStaticMetadataFromRoute", "promises", "fs", "isAppRouteRoute", "AppPathnameNormalizer", "isAppBuiltinNotFoundPage", "loadEntrypoint", "isGroupSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "getFilesInDir", "PARALLEL_ROUTE_DEFAULT_PATH", "createAppRouteCode", "HTTP_ACCESS_FALLBACKS", "forbidden", "unauthorized", "defaultHTTPAccessFallbackPaths", "FILE_TYPES", "layout", "template", "error", "loading", "GLOBAL_ERROR_FILE_TYPE", "PAGE_SEGMENT", "PARALLEL_CHILDREN_SEGMENT", "defaultGlobalErrorPath", "defaultLayoutPath", "normalizeP<PERSON><PERSON><PERSON><PERSON>ey", "key", "startsWith", "slice", "isDirectory", "pathname", "stat", "err", "createTreeCodeFromPath", "pagePath", "page", "resolveDir", "resolver", "resolveParallelSegments", "metadataResolver", "pageExtensions", "basePath", "collectedDeclarations", "splittedPath", "split", "isNotFoundRoute", "isDefaultNotFound", "appDirPrefix", "pages", "rootLayout", "globalError", "resolveAdjacentParallelSegments", "segmentPath", "absoluteSegmentPath", "segmentIsDirectory", "files", "opendir", "parallelSegments", "dirent", "name", "charCodeAt", "push", "createSubtreePropsFromSegmentPath", "segments", "nestedCollectedDeclarations", "join", "props", "isRootLayer", "length", "isRootLayoutOrRootPage", "metadata", "routerDirPath", "resolvedRouteDir", "segment", "parallel<PERSON>ey", "parallelSegment", "matchedPagePath", "resolvedPagePath", "varName", "JSON", "stringify", "subSegmentPath", "normalizedParallelSegment", "Array", "isArray", "parallelSegmentPath", "filePaths", "Promise", "all", "Object", "values", "map", "file", "endsWith", "definedFilePaths", "filter", "filePath", "undefined", "existedConventionNames", "Set", "type", "isFirstLayerGroupRoute", "seg", "accessFallbackTypes", "keys", "hasRootFallbackFile", "hasLayerFallbackFile", "has", "defaultFallbackPath", "<PERSON><PERSON><PERSON>", "find", "resolvedGlobalErrorPath", "parallelSegmentKey", "normalizedParallel<PERSON>ey", "subtreeCode", "notFoundPath", "modulesCode", "treeCode", "pageSubtreeCode", "adjacentParallelSegments", "adjacentParallelSegment", "actualSegment", "defaultPath", "entries", "value", "createAbsolutePath", "appDir", "pathToTurnAbsolute", "replace", "sep", "filesInDirMapMap", "WeakMap", "nextApp<PERSON><PERSON>der", "loaderOptions", "getOptions", "appPaths", "rootDir", "tsconfigPath", "isDev", "nextConfigOutput", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "nextConfigExperimentalUseEarlyImport", "buildInfo", "_module", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "route", "absolutePagePath", "relatedModules", "extensions", "extension", "normalizedAppPaths", "matched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appPath", "rest", "children", "isParallelRoute", "isIncomingParallelPage", "includes", "hasCurrentParallelPage", "Error", "pathToResolve", "resolveAppRoute", "fileExistsInDirectory", "dirname", "fileName", "_compilation", "catch", "e", "get", "Map", "set", "absolutePath", "filenameIndex", "lastIndexOf", "filename", "result", "ext", "absolutePathWithExtension", "addMissingDependency", "exts", "absoluteDir", "filenameWithExt", "treeCodeResult", "loaderContext", "process", "exit", "createdRootLayout", "rootLayoutPath", "dir", "message", "relative", "_compiler", "context", "clear", "normalize", "code", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_MODULE_GLOBAL_ERROR", "tree", "__next_app_require__", "__next_app_load_chunk__", "header", "env", "NODE_ENV", "modulePath"], "mappings": "AACA,SACEA,0BAA0B,EAC1BC,gCAAgC,QAE3B,mCAAkC;AAGzC,OAAOC,UAAU,OAAM;AACvB,SAASC,IAAI,QAAQ,6BAA4B;AACjD,SAASC,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,aAAa,QAAQ,4BAA2B;AACzD,SACEC,yBAAyB,EACzBC,6BAA6B,QACxB,uBAAsB;AAC7B,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,eAAe,QAAQ,qCAAoC;AAEpE,SAASC,qBAAqB,QAAQ,mEAAkE;AAExG,SAASC,wBAAwB,QAAQ,iBAAgB;AACzD,SAASC,cAAc,QAAQ,2BAA0B;AACzD,SACEC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,iCAAgC;AACvC,SAASC,aAAa,QAAQ,mCAAkC;AAEhE,SAASC,2BAA2B,QAAQ,uDAAsD;AAElG,SAASC,kBAAkB,QAAQ,0BAAyB;AAqB5D,MAAMC,wBAAwB;IAC5B,aAAa;IACbC,WAAW;IACXC,cAAc;AAChB;AACA,MAAMC,iCAAiC;IACrC,aAAa;IACbF,WAAW;IACXC,cAAc;AAChB;AAEA,MAAME,aAAa;IACjBC,QAAQ;IACRC,UAAU;IACVC,OAAO;IACPC,SAAS;IACT,gBAAgB;IAChB,GAAGR,qBAAqB;AAC1B;AAEA,MAAMS,yBAAyB;AAC/B,MAAMC,eAAe;AACrB,MAAMC,4BAA4B;AAElC,MAAMC,yBAAyB;AAC/B,MAAMC,oBAAoB;AAsB1B,MAAMC,uBAAuB,CAACC,MAC5BA,IAAIC,UAAU,CAAC,OAAOD,IAAIE,KAAK,CAAC,KAAKF;AAEvC,MAAMG,cAAc,OAAOC;IACzB,IAAI;QACF,MAAMC,OAAO,MAAM/B,GAAG+B,IAAI,CAACD;QAC3B,OAAOC,KAAKF,WAAW;IACzB,EAAE,OAAOG,KAAK;QACZ,OAAO;IACT;AACF;AAEA,eAAeC,uBACbC,QAAgB,EAChB,EACEC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,EACdC,QAAQ,EACRC,qBAAqB,EAatB;IAOD,MAAMC,eAAeT,SAASU,KAAK,CAAC,SAAS;IAC7C,MAAMC,kBAAkBV,SAAS7C;IAEjC,MAAMwD,oBAAoB3C,yBAAyB+B;IACnD,MAAMa,eAAeD,oBAAoBlD,gBAAgB+C,YAAY,CAAC,EAAE;IACxE,MAAMK,QAAkB,EAAE;IAE1B,IAAIC;IACJ,IAAIC;IAEJ,eAAeC,gCACbC,WAAmB;QAEnB,MAAMC,sBAAsB,MAAMjB,WAChC,GAAGW,eAAeK,aAAa;QAGjC,IAAI,CAACC,qBAAqB;YACxB,OAAO,EAAE;QACX;QAEA,MAAMC,qBAAqB,MAAMzB,YAAYwB;QAE7C,IAAI,CAACC,oBAAoB;YACvB,OAAO,EAAE;QACX;QAEA,wDAAwD;QACxD,MAAMC,QAAQ,MAAMvD,GAAGwD,OAAO,CAACH;QAE/B,MAAMI,mBAA6B;YAAC;SAAW;QAE/C,WAAW,MAAMC,UAAUH,MAAO;YAChC,qDAAqD;YACrD,IAAIG,OAAO7B,WAAW,MAAM6B,OAAOC,IAAI,CAACC,UAAU,CAAC,OAAO,IAAI;gBAC5DH,iBAAiBI,IAAI,CAACH,OAAOC,IAAI;YACnC;QACF;QAEA,OAAOF;IACT;IAEA,eAAeK,kCACbC,QAAkB,EAClBC,2BAA+C;QAI/C,MAAMZ,cAAcW,SAASE,IAAI,CAAC;QAElC,wDAAwD;QACxD,MAAMC,QAAgC,CAAC;QACvC,iDAAiD;QACjD,MAAMC,cAAcJ,SAASK,MAAM,KAAK;QACxC,MAAMC,yBAAyBN,SAASK,MAAM,IAAI;QAElD,wDAAwD;QACxD,MAAMX,mBAAgE,EAAE;QACxE,IAAIU,aAAa;YACfV,iBAAiBI,IAAI,CAAC;gBAAC;gBAAY;aAAG;QACxC,OAAO;YACLJ,iBAAiBI,IAAI,IAAIvB,wBAAwBc;QACnD;QAEA,IAAIkB,WACF;QACF,MAAMC,gBAAgB,GAAGxB,eAAeK,aAAa;QACrD,wEAAwE;QACxE,MAAMoB,mBAAmB1B,oBACrB,KACA,MAAMV,WAAWmC;QAErB,IAAIC,kBAAkB;YACpBF,WAAW,MAAMxE,8BAA8B0E,kBAAkB;gBAC/D/B;gBACAgC,SAASrB;gBACTb;gBACA8B;gBACA7B;YACF;QACF;QAEA,KAAK,MAAM,CAACkC,aAAaC,gBAAgB,IAAIlB,iBAAkB;YAC7D,gHAAgH;YAChH,0CAA0C;YAC1C,IAAIkB,oBAAoBtD,cAAc;gBACpC,MAAMuD,kBAAkB,GAAG7B,eAAeK,cACxCsB,gBAAgB,aAAa,KAAK,CAAC,CAAC,EAAEA,aAAa,CACpD,KAAK,CAAC;gBAEP,MAAMG,mBAAmB,MAAMxC,SAASuC;gBACxC,IAAIC,kBAAkB;oBACpB7B,MAAMa,IAAI,CAACgB;oBAEX,MAAMC,UAAU,CAAC,IAAI,EAAEd,4BAA4BI,MAAM,EAAE;oBAC3DJ,4BAA4BH,IAAI,CAAC;wBAACiB;wBAASD;qBAAiB;oBAE5D,+GAA+G;oBAC/GX,KAAK,CAACzC,qBAAqBiD,aAAa,GACtC,CAAC,EAAE,EAAEnE,iBAAiB;iBACjB,EAAEuE,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAACH,kBAAkB;UACtD,EAAEhF,0BAA0ByE,UAAU;UACtC,CAAC;oBACD;gBACF;YACF;YAEA,yGAAyG;YACzG,+HAA+H;YAC/H,6FAA6F;YAE7F,MAAMW,iBAAiB;mBAAIlB;aAAS;YACpC,IAAIW,gBAAgB,YAAY;gBAC9B,oFAAoF;gBACpF,0FAA0F;gBAC1FO,eAAepB,IAAI,CAACa;YACtB;YAEA,MAAMQ,4BAA4BC,MAAMC,OAAO,CAACT,mBAC5CA,eAAe,CAAC,EAAE,GAClBA;YAEJ,IACEO,8BAA8B7D,gBAC9B6D,8BAA8B5D,2BAC9B;gBACA,mHAAmH;gBACnH,sHAAsH;gBACtH,4FAA4F;gBAC5F2D,eAAepB,IAAI,CAACqB;YACtB;YAEA,MAAMG,sBAAsBJ,eAAehB,IAAI,CAAC;YAEhD,kGAAkG;YAClG,mDAAmD;YACnD,MAAMqB,YAAY,MAAMC,QAAQC,GAAG,CACjCC,OAAOC,MAAM,CAAC3E,YAAY4E,GAAG,CAAC,OAAOC;gBACnC,OAAO;oBACLA;oBACA,MAAMvD,SACJ,GAAGU,eACD,2GAA2G;oBAC3GsC,oBAAoBQ,QAAQ,CAAC,OACzBR,sBACAA,sBAAsB,MACzBO,MAAM;iBAEZ;YACH;YAGF,MAAME,mBAAmBR,UAAUS,MAAM,CACvC,CAAC,GAAGC,SAAS,GAAKA,aAAaC;YAGjC,8DAA8D;YAC9D,MAAMC,yBAAyB,IAAIC,IACjCL,iBAAiBH,GAAG,CAAC,CAAC,CAACS,KAAK,GAAKA;YAEnC,iEAAiE;YACjE,MAAMC,yBACJtC,SAASK,MAAM,KAAK,KACpBa,eAAec,MAAM,CAAC,CAACO,MAAQjG,eAAeiG,MAAMlC,MAAM,KAAK;YAEjE,IAAID,eAAekC,wBAAwB;gBACzC,MAAME,sBAAsBd,OAAOe,IAAI,CACrC1F;gBAEF,KAAK,MAAMsF,QAAQG,oBAAqB;oBACtC,MAAME,sBAAsB,MAAMpE,SAChC,GAAGU,aAAa,CAAC,EAAEhC,UAAU,CAACqF,KAAK,EAAE;oBAEvC,MAAMM,uBAAuBR,uBAAuBS,GAAG,CAACP;oBAExD,oHAAoH;oBACpH,IACE,iDAAiD;oBACjD,CAAEK,CAAAA,uBAAuBJ,sBAAqB,KAC9C,yCAAyC;oBACzC,CAACK,sBACD;wBACA,MAAME,sBAAsB9F,8BAA8B,CAACsF,KAAK;wBAChEN,iBAAiBjC,IAAI,CAAC;4BAACuC;4BAAMQ;yBAAoB;oBACnD;gBACF;YACF;YAEA,IAAI,CAAC3D,YAAY;oBACI6C;gBAAnB,MAAMe,cAAaf,yBAAAA,iBAAiBgB,IAAI,CACtC,CAAC,CAACV,KAAK,GAAKA,SAAS,8BADJN,sBAEhB,CAAC,EAAE;gBACN7C,aAAa4D;gBAEb,IAAI/D,qBAAqB,CAAC+D,cAAc,CAAC5D,YAAY;oBACnDA,aAAazB;oBACbsE,iBAAiBjC,IAAI,CAAC;wBAAC;wBAAUZ;qBAAW;gBAC9C;YACF;YAEA,IAAI,CAACC,aAAa;gBAChB,MAAM6D,0BAA0B,MAAM1E,SACpC,GAAGU,aAAa,CAAC,EAAE3B,wBAAwB;gBAE7C,IAAI2F,yBAAyB;oBAC3B7D,cAAc6D;gBAChB;YACF;YAEA,IAAIC,qBAAqB7B,MAAMC,OAAO,CAACT,mBACnCA,eAAe,CAAC,EAAE,GAClBA;YAEJ,2FAA2F;YAC3F,iGAAiG;YACjG,qGAAqG;YACrGqC,qBACEA,uBAAuB1F,4BACnB,aACA0F,uBAAuB3F,eACrBd,mBACAyG;YAER,MAAMC,wBAAwBxF,qBAAqBiD;YACnD,IAAIwC;YACJ,uEAAuE;YACvE,IAAIrE,mBAAmBoE,0BAA0B,YAAY;oBAEzDnB;gBADF,MAAMqB,eACJrB,EAAAA,0BAAAA,iBAAiBgB,IAAI,CAAC,CAAC,CAACV,KAAK,GAAKA,SAAS,iCAA3CN,uBAAyD,CAAC,EAAE,KAC5DhF,8BAA8B,CAAC,YAAY;gBAE7C,MAAMgE,UAAU,CAAC,QAAQ,EAAEd,4BAA4BI,MAAM,EAAE;gBAC/DJ,4BAA4BH,IAAI,CAAC;oBAACiB;oBAASqC;iBAAa;gBACxDD,cAAc,CAAC;qBACF,EAAEnC,KAAKC,SAAS,CAAC3F,4BAA4B;wBAC1C,EAAEkB,iBAAiB;;gBAE3B,EAAEuE,QAAQ;gBACV,EAAEC,KAAKC,SAAS,CAACmC,cAAc;;;;SAItC,CAAC;YACJ;YAEA,MAAMC,cAAc,CAAC;QACnB,EAAEtB,iBACCH,GAAG,CAAC,CAAC,CAACC,MAAMI,SAAS;gBACpB,MAAMlB,UAAU,CAAC,MAAM,EAAEd,4BAA4BI,MAAM,EAAE;gBAC7DJ,4BAA4BH,IAAI,CAAC;oBAACiB;oBAASkB;iBAAS;gBACpD,OAAO,CAAC,CAAC,EAAEJ,KAAK,IAAI,EAAEd,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAACgB,UAAU,EAAE,CAAC;YAChE,GACC/B,IAAI,CAAC,MAAM;QACd,EAAEpE,0BAA0ByE,UAAU;OACvC,CAAC;YAEF,IAAI,CAAC4C,aAAa;gBAChB,MAAM,EAAEG,UAAUC,eAAe,EAAE,GACjC,MAAMxD,kCACJmB,gBACAjB;gBAGJkD,cAAcI;YAChB;YAEApD,KAAK,CAAC+C,sBAAsB,GAAG,CAAC;SAC7B,EAAED,mBAAmB;QACtB,EAAEE,YAAY;QACd,EAAEE,YAAY;OACf,CAAC;QACJ;QAEA,MAAMG,2BACJ,MAAMpE,gCAAgCC;QAExC,KAAK,MAAMoE,2BAA2BD,yBAA0B;YAC9D,IAAI,CAACrD,KAAK,CAACzC,qBAAqB+F,yBAAyB,EAAE;gBACzD,MAAMC,gBACJD,4BAA4B,aACxB,KACA,CAAC,CAAC,EAAEA,yBAAyB;gBAEnC,iGAAiG;gBACjG,MAAME,cACJ,AAAC,MAAMrF,SACL,GAAGU,eAAeK,cAAcqE,cAAc,QAAQ,CAAC,KACnDhH;gBAER,MAAMqE,UAAU,CAAC,OAAO,EAAEd,4BAA4BI,MAAM,EAAE;gBAC9DJ,4BAA4BH,IAAI,CAAC;oBAACiB;oBAAS4C;iBAAY;gBACvDxD,KAAK,CAACzC,qBAAqB+F,yBAAyB,GAAG,CAAC;WACrD,EAAElH,oBAAoB;;;0BAGP,EAAEwE,QAAQ,EAAE,EAAEC,KAAKC,SAAS,CAAC0C,aAAa;;SAE3D,CAAC;YACJ;QACF;QACA,OAAO;YACLL,UAAU,CAAC;QACT,EAAE5B,OAAOkC,OAAO,CAACzD,OACdyB,GAAG,CAAC,CAAC,CAACjE,KAAKkG,MAAM,GAAK,GAAGlG,IAAI,EAAE,EAAEkG,OAAO,EACxC3D,IAAI,CAAC,OAAO;OAChB,CAAC;QACJ;IACF;IAEA,MAAM,EAAEoD,QAAQ,EAAE,GAAG,MAAMvD,kCACzB,EAAE,EACFpB;IAGF,OAAO;QACL2E,UAAU,GAAGA,SAAS,UAAU,CAAC;QACjCrE,OAAO,GAAG+B,KAAKC,SAAS,CAAChC,OAAO,CAAC,CAAC;QAClCC;QACAC,aAAaA,eAAe3B;IAC9B;AACF;AAEA,SAASsG,mBAAmBC,MAAc,EAAEC,kBAA0B;IACpE,OACEA,kBACE,uEAAuE;KACtEC,OAAO,CAAC,OAAOzI,KAAK0I,GAAG,EACvBD,OAAO,CAAC,yBAAyBF;AAExC;AAEA,MAAMI,mBAGF,IAAIC;AACR,MAAMC,gBAA2B,eAAeA;IAC9C,MAAMC,gBAAgB,IAAI,CAACC,UAAU;IACrC,MAAM,EACJ3E,IAAI,EACJmE,MAAM,EACNS,QAAQ,EACRrG,QAAQ,EACRM,cAAc,EACdgG,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,eAAe,EACfnG,QAAQ,EACRoG,kBAAkBC,sBAAsB,EACxCC,oCAAoC,EACrC,GAAGV;IAEJ,MAAMW,YAAYvJ,mBAAmB,AAAC,IAAI,CAASwJ,OAAO;IAC1D,MAAMvG,wBAA4C,EAAE;IACpD,MAAMP,OAAOwB,KAAKqE,OAAO,CAAC,QAAQ;IAClC,MAAMa,mBAAqC9D,KAAKmE,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAExDL,UAAUM,KAAK,GAAG;QAChBnH;QACAoH,kBAAkB1B,mBAAmBC,QAAQ5F;QAC7C0G;QACAC;QACAW,gBAAgB,EAAE;IACpB;IAEA,MAAMC,aAAajH,eAAemD,GAAG,CAAC,CAAC+D,YAAc,CAAC,CAAC,EAAEA,WAAW;IAEpE,MAAMC,qBACJ,OAAOpB,aAAa,WAAW;QAACA;KAAS,GAAGA,YAAY,EAAE;IAE5D,MAAMjG,0BAA0B,CAC9BR;QAEA,MAAM8H,UAA6C,CAAC;QACpD,IAAIC;QACJ,KAAK,MAAMC,WAAWH,mBAAoB;YACxC,IAAIG,QAAQnI,UAAU,CAACG,WAAW,MAAM;gBACtC,MAAMiI,OAAOD,QAAQlI,KAAK,CAACE,SAASsC,MAAM,GAAG,GAAGxB,KAAK,CAAC;gBAEtD,4CAA4C;gBAC5C,IAAImH,KAAK3F,MAAM,KAAK,KAAK2F,IAAI,CAAC,EAAE,KAAK,QAAQ;oBAC3CF,uBAAuBC;oBACvBF,QAAQI,QAAQ,GAAG3I;oBACnB;gBACF;gBAEA,MAAM4I,kBAAkBF,IAAI,CAAC,EAAE,CAACpI,UAAU,CAAC;gBAC3C,IAAIsI,iBAAiB;oBACnB,IAAIF,KAAK3F,MAAM,KAAK,KAAK2F,IAAI,CAAC,EAAE,KAAK,QAAQ;wBAC3C,oGAAoG;wBACpG,+HAA+H;wBAC/H,8GAA8G;wBAC9GH,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;4BAAC1I;yBAAa;wBACjC;oBACF;oBACA,iHAAiH;oBACjH,4HAA4H;oBAC5H,0BAA0B;oBAC1BuI,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;wBAACzI;2BAA8ByI,KAAKnI,KAAK,CAAC;qBAAG;oBAChE;gBACF;gBAEA,IAAIiI,wBAAwBD,QAAQI,QAAQ,KAAKD,IAAI,CAAC,EAAE,EAAE;oBACxD,gFAAgF;oBAChF,wEAAwE;oBACxE,MAAMG,yBAAyBJ,QAAQK,QAAQ,CAAC;oBAChD,MAAMC,yBAAyBP,qBAAqBM,QAAQ,CAAC;oBAE7D,IAAID,wBAAwB;wBAQ1B;oBACF,OAAO,IAAI,CAACE,0BAA0B,CAACF,wBAAwB;wBAC7D,6EAA6E;wBAC7E,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,+EAA+E,EAAER,qBAAqB,KAAK,EAAEC,QAAQ,gIAAgI,CAAC,GADnP,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAD,uBAAuBC;gBACvBF,QAAQI,QAAQ,GAAGD,IAAI,CAAC,EAAE;YAC5B;QACF;QAEA,OAAOtE,OAAOkC,OAAO,CAACiC;IACxB;IAEA,MAAMxH,aAA0B,CAACkI;QAC/B,OAAOzC,mBAAmBC,QAAQwC;IACpC;IAEA,MAAMC,kBAAgC,CAACD;QACrC,OAAOzC,mBAAmBC,QAAQwC;IACpC;IAEA,+DAA+D;IAC/D,0EAA0E;IAC1E,+EAA+E;IAC/E,yEAAyE;IACzE,MAAME,wBAAwB,OAAOC,SAAiBC;QACpD,gGAAgG;QAChG,IAAI,IAAI,CAACC,YAAY,KAAK1E,WAAW;YACnC,IAAI;gBACF,OAAO,AAAC,CAAA,MAAMzF,cAAciK,SAASG,KAAK,CAAC,IAAM,IAAIzE,MAAK,EAAGQ,GAAG,CAC9D+D;YAEJ,EAAE,OAAOG,GAAG;gBACV,OAAO;YACT;QACF;QACA,MAAMlF,MACJuC,iBAAiB4C,GAAG,CAAC,IAAI,CAACH,YAAY,KACtC,IAAII;QACN,IAAI,CAAC7C,iBAAiBvB,GAAG,CAAC,IAAI,CAACgE,YAAY,GAAG;YAC5CzC,iBAAiB8C,GAAG,CAAC,IAAI,CAACL,YAAY,EAAEhF;QAC1C;QACA,IAAI,CAACA,IAAIgB,GAAG,CAAC8D,UAAU;YACrB9E,IAAIqF,GAAG,CACLP,SACAjK,cAAciK,SAASG,KAAK,CAAC,IAAM,IAAIzE;QAE3C;QACA,OAAO,AAAC,CAAA,AAAC,MAAMR,IAAImF,GAAG,CAACL,YAAa,IAAItE,KAAI,EAAGQ,GAAG,CAAC+D;IACrD;IAEA,MAAMrI,WAAyB,OAAOP;QACpC,MAAMmJ,eAAepD,mBAAmBC,QAAQhG;QAEhD,MAAMoJ,gBAAgBD,aAAaE,WAAW,CAAC5L,KAAK0I,GAAG;QACvD,MAAMwC,UAAUQ,aAAarJ,KAAK,CAAC,GAAGsJ;QACtC,MAAME,WAAWH,aAAarJ,KAAK,CAACsJ,gBAAgB;QAEpD,IAAIG;QAEJ,KAAK,MAAMC,OAAO7B,WAAY;YAC5B,MAAM8B,4BAA4B,GAAGN,eAAeK,KAAK;YACzD,IACE,CAACD,UACA,MAAMb,sBAAsBC,SAAS,GAAGW,WAAWE,KAAK,GACzD;gBACAD,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,MAAM9I,mBAAqC,OACzCkI,SACAW,UACAK;QAEA,MAAMC,cAAc7D,mBAAmBC,QAAQ2C;QAE/C,IAAIY;QAEJ,KAAK,MAAMC,OAAOG,KAAM;YACtB,kGAAkG;YAClG,MAAME,kBAAkB,GAAGP,SAAS,CAAC,EAAEE,KAAK;YAC5C,MAAMC,4BAA4B,GAAGG,cAAcnM,KAAK0I,GAAG,GAAG0D,iBAAiB;YAC/E,IAAI,CAACN,UAAW,MAAMb,sBAAsBC,SAASkB,kBAAmB;gBACtEN,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,IAAIpL,gBAAgB0D,OAAO;QACzB,OAAOjD,mBAAmB;YACxBoH;YACA,8EAA8E;YAC9E3F,MAAMkG,cAAclG,IAAI;YACxBwB;YACAzB;YACAqI;YACA/H;YACAmG;QACF;IACF;IAEA,IAAIiD,iBAAiB,MAAM3J,uBAAuBC,UAAU;QAC1DC;QACAC;QACAC;QACAE;QACAD;QACAuJ,eAAe,IAAI;QACnBrJ;QACAC;QACAC;IACF;IAEA,IAAI,CAACkJ,eAAe3I,UAAU,EAAE;QAC9B,IAAI,CAACyF,OAAO;YACV,8DAA8D;YAC9D/I,IAAIuB,KAAK,CACP,GAAG1B,KACD0C,SAAS8F,OAAO,CAAC,GAAGpI,cAAc,CAAC,CAAC,EAAE,KACtC,uFAAuF,CAAC;YAE5FkM,QAAQC,IAAI,CAAC;QACf,OAAO;gBA2BkB7D;YA1BvB,2CAA2C;YAC3C,MAAM,CAAC8D,mBAAmBC,eAAe,GAAG,MAAMvM,iBAAiB;gBACjEoI,QAAQA;gBACRoE,KAAK1D;gBACLC,cAAcA;gBACdvG;gBACAM;YACF;YACA,IAAI,CAACwJ,mBAAmB;gBACtB,IAAIG,UAAU,GAAG3M,KACf0C,SAAS8F,OAAO,CAAC,GAAGpI,cAAc,CAAC,CAAC,EAAE,KACtC,6BAA6B,CAAC;gBAEhC,IAAIqM,gBAAgB;wBAEF;oBADhBE,WAAW,CAAC,mBAAmB,EAAE3M,KAC/BD,KAAK6M,QAAQ,CAAC,EAAA,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBC,OAAO,KAAI,IAAIL,iBAC7C,kCAAkC,CAAC;gBACvC,OAAO;oBACLE,WACE;gBACJ;gBAEA,MAAM,qBAAkB,CAAlB,IAAI9B,MAAM8B,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;YACzB;YAEA,mEAAmE;YACnE,IAAI,IAAI,CAACxB,YAAY,GAAEzC,wBAAAA,iBAAiB4C,GAAG,CAAC,IAAI,CAACH,YAAY,sBAAtCzC,sBAAyCqE,KAAK;YACrEX,iBAAiB,MAAM3J,uBAAuBC,UAAU;gBACtDC;gBACAC;gBACAC;gBACAE;gBACAD;gBACAuJ,eAAe,IAAI;gBACnBrJ;gBACAC;gBACAC;YACF;QACF;IACF;IAEA,MAAMZ,WAAW,IAAI5B,wBAAwBsM,SAAS,CAACrK;IAEvD,iGAAiG;IACjG,6GAA6G;IAC7G,MAAMsK,OAAO,MAAMrM,eACjB,YACA;QACEsM,qBAAqBvK;QACrBwK,yBAAyB7K;QACzB8K,yBAAyBhB,eAAe1I,WAAW;IACrD,GACA;QACE2J,MAAMjB,eAAevE,QAAQ;QAC7BrE,OAAO4I,eAAe5I,KAAK;QAC3B8J,sBAAsB;QACtB,0FAA0F;QAC1FC,yBAAyB;IAC3B;IAGF,MAAMC,SACJjE,wCACA+C,QAAQmB,GAAG,CAACC,QAAQ,KAAK,eAErBxK,sBACGiD,GAAG,CAAC,CAAC,CAACb,SAASqI,WAAW;QACzB,OAAO,CAAC,YAAY,EAAErI,QAAQ,OAAO,EAAEC,KAAKC,SAAS,CACnDmI,YACA,SAAS,EAAErI,QAAQ,SAAS,EAAEA,QAAQ,IAAI,CAAC;IAC/C,GACCb,IAAI,CAAC,MAERvB,sBACGiD,GAAG,CAAC,CAAC,CAACb,SAASqI,WAAW;QACzB,OAAO,CAAC,MAAM,EAAErI,QAAQ,2CAA2C,EAAEC,KAAKC,SAAS,CACjFmI,YACA,IAAI,CAAC;IACT,GACClJ,IAAI,CAAC;IAEd,OAAO+I,SAASP;AAClB;AAEA,eAAerE,cAAa"}