{"version": 3, "sources": ["../../../src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": "AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;AAEzE,OAAO,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,IACxBL,kBAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,OAC5CR,kBAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAI,QAAQP,KAAK,CACV,qEACDA;QAEFC,OAAOI,QAAQ,CAACC,IAAI,GAAGL,OAAOC,IAAI,CAACC,YAAY,CAACK,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEA,OAAO,SAASC;IACd,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QAC5C,8DAA8D;QAC9D,sDAAsD;QACtDf,UAAU;YACR,MAAMgB,2BAA2B,CAC/BC;gBAEA,MAAMd,QAAQ,YAAYc,MAAMA,IAAIC,MAAM,GAAGD,IAAId,KAAK;gBACtD,qDAAqD;gBACrD,oDAAoD;gBACpD,qCAAqC;gBACrCD,mBAAmBC;YACrB;YACAC,OAAOe,gBAAgB,CAAC,sBAAsBH;YAC9CZ,OAAOe,gBAAgB,CAAC,SAASH;YACjC,OAAO;gBACLZ,OAAOgB,mBAAmB,CAAC,SAASJ;gBACpCZ,OAAOgB,mBAAmB,CACxB,sBACAJ;YAEJ;QACF,GAAG,EAAE;IACP;AACF"}