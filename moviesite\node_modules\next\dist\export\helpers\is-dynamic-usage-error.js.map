{"version": 3, "sources": ["../../../src/export/helpers/is-dynamic-usage-error.ts"], "sourcesContent": ["import { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\n\nexport const isDynamicUsageError = (err: unknown) =>\n  isDynamicServerError(err) ||\n  isBailoutToCSRError(err) ||\n  isNextRouterError(err) ||\n  isDynamicPostpone(err)\n"], "names": ["isDynamicUsageError", "err", "isDynamicServerError", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone"], "mappings": ";;;;+BAKaA;;;eAAAA;;;oCALwB;8BACD;mCACF;kCACA;AAE3B,MAAMA,sBAAsB,CAACC,MAClCC,IAAAA,wCAAoB,EAACD,QACrBE,IAAAA,iCAAmB,EAACF,QACpBG,IAAAA,oCAAiB,EAACH,QAClBI,IAAAA,mCAAiB,EAACJ"}