{"version": 3, "sources": ["../../../../../src/server/route-matcher-providers/helpers/manifest-loaders/node-manifest-loader.ts"], "sourcesContent": ["import { SERVER_DIRECTORY } from '../../../../shared/lib/constants'\nimport path from '../../../../shared/lib/isomorphic/path'\nimport type { <PERSON>ife<PERSON>, ManifestLoader } from './manifest-loader'\n\nexport class NodeManifestLoader implements ManifestLoader {\n  constructor(private readonly distDir: string) {}\n\n  static require(id: string) {\n    try {\n      return require(id)\n    } catch {\n      return null\n    }\n  }\n\n  public load(name: string): Manifest | null {\n    return NodeManifestLoader.require(\n      path.join(this.distDir, SERVER_DIRECTORY, name)\n    )\n  }\n}\n"], "names": ["NodeManifestLoader", "constructor", "distDir", "require", "id", "load", "name", "path", "join", "SERVER_DIRECTORY"], "mappings": ";;;;+BAIaA;;;eAAAA;;;2BAJoB;6DAChB;;;;;;AAGV,MAAMA;IACXC,YAAY,AAAiBC,OAAe,CAAE;aAAjBA,UAAAA;IAAkB;IAE/C,OAAOC,QAAQC,EAAU,EAAE;QACzB,IAAI;YACF,OAAOD,QAAQC;QACjB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEOC,KAAKC,IAAY,EAAmB;QACzC,OAAON,mBAAmBG,OAAO,CAC/BI,aAAI,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO,EAAEO,2BAAgB,EAAEH;IAE9C;AACF"}