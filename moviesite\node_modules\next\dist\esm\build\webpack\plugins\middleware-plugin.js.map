{"version": 3, "sources": ["../../../../src/build/webpack/plugins/middleware-plugin.ts"], "sourcesContent": ["import type {\n  AssetBinding,\n  EdgeMiddlewareMeta,\n} from '../loaders/get-module-build-info'\nimport type { EdgeSSRMeta } from '../loaders/get-module-build-info'\nimport type { MiddlewareMatcher } from '../../analysis/get-page-static-info'\nimport { getNamedMiddlewareRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport path from 'path'\nimport {\n  EDGE_RUNTIME_WEBPACK,\n  EDGE_UNSUPPORTED_NODE_APIS,\n  MIDDLEWARE_BUILD_MANIFEST,\n  CLIENT_REFERENCE_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  NEXT_FONT_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  INTERCEPTION_ROUTE_REWRITE_MANIFEST,\n  DYNAMIC_CSS_MANIFEST,\n} from '../../../shared/lib/constants'\nimport type { MiddlewareConfig } from '../../analysis/get-page-static-info'\nimport type { Telemetry } from '../../../telemetry/storage'\nimport { traceGlobals } from '../../../trace/shared'\nimport { EVENT_BUILD_FEATURE_USAGE } from '../../../telemetry/events'\nimport { normalizeAppPath } from '../../../shared/lib/router/utils/app-paths'\nimport {\n  INSTRUMENTATION_HOOK_FILENAME,\n  WEBPACK_LAYERS,\n} from '../../../lib/constants'\nimport type { CustomRoutes } from '../../../lib/load-custom-routes'\nimport { isInterceptionRouteRewrite } from '../../../lib/generate-interception-routes-rewrites'\nimport { getDynamicCodeEvaluationError } from './wellknown-errors-plugin/parse-dynamic-code-evaluation-error'\nimport { getModuleReferencesInOrder } from '../utils'\n\nconst KNOWN_SAFE_DYNAMIC_PACKAGES =\n  require('../../../lib/known-edge-safe-packages.json') as string[]\n\nexport interface EdgeFunctionDefinition {\n  files: string[]\n  name: string\n  page: string\n  matchers: MiddlewareMatcher[]\n  env: Record<string, string>\n  wasm?: AssetBinding[]\n  assets?: AssetBinding[]\n  regions?: string[] | string\n}\n\nexport interface MiddlewareManifest {\n  version: 3\n  sortedMiddleware: string[]\n  middleware: { [page: string]: EdgeFunctionDefinition }\n  functions: { [page: string]: EdgeFunctionDefinition }\n}\n\ninterface EntryMetadata {\n  edgeMiddleware?: EdgeMiddlewareMeta\n  edgeApiFunction?: EdgeMiddlewareMeta\n  edgeSSR?: EdgeSSRMeta\n  wasmBindings: Map<string, string>\n  assetBindings: Map<string, string>\n  regions?: string[] | string\n}\n\nconst NAME = 'MiddlewarePlugin'\nconst MANIFEST_VERSION = 3\n\n/**\n * Checks the value of usingIndirectEval and when it is a set of modules it\n * check if any of the modules is actually being used. If the value is\n * simply truthy it will return true.\n */\nfunction isUsingIndirectEvalAndUsedByExports(args: {\n  module: webpack.Module\n  moduleGraph: webpack.ModuleGraph\n  runtime: any\n  usingIndirectEval: true | Set<string>\n  wp: typeof webpack\n}): boolean {\n  const { moduleGraph, runtime, module, usingIndirectEval, wp } = args\n  if (typeof usingIndirectEval === 'boolean') {\n    return usingIndirectEval\n  }\n\n  const exportsInfo = moduleGraph.getExportsInfo(module)\n  for (const exportName of usingIndirectEval) {\n    if (exportsInfo.getUsed(exportName, runtime) !== wp.UsageState.Unused) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction getEntryFiles(\n  entryFiles: string[],\n  meta: EntryMetadata,\n  hasInstrumentationHook: boolean,\n  opts: Options\n) {\n  const files: string[] = []\n  if (meta.edgeSSR) {\n    if (meta.edgeSSR.isServerComponent) {\n      files.push(`server/${SERVER_REFERENCE_MANIFEST}.js`)\n      if (opts.sriEnabled) {\n        files.push(`server/${SUBRESOURCE_INTEGRITY_MANIFEST}.js`)\n      }\n      files.push(\n        ...entryFiles\n          .filter(\n            (file) =>\n              file.startsWith('app/') && !file.endsWith('.hot-update.js')\n          )\n          .map(\n            (file) =>\n              'server/' +\n              file.replace(/\\.js$/, '_' + CLIENT_REFERENCE_MANIFEST + '.js')\n          )\n      )\n    }\n    if (!opts.dev && !meta.edgeSSR.isAppDir) {\n      files.push(`server/${DYNAMIC_CSS_MANIFEST}.js`)\n    }\n\n    files.push(\n      `server/${MIDDLEWARE_BUILD_MANIFEST}.js`,\n      `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n      `server/${NEXT_FONT_MANIFEST}.js`,\n      `server/${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`\n    )\n  }\n\n  if (hasInstrumentationHook) {\n    files.push(`server/edge-${INSTRUMENTATION_HOOK_FILENAME}.js`)\n  }\n\n  files.push(\n    ...entryFiles\n      .filter((file) => !file.endsWith('.hot-update.js'))\n      .map((file) => 'server/' + file)\n  )\n\n  return files\n}\n\nfunction getCreateAssets(params: {\n  compilation: webpack.Compilation\n  metadataByEntry: Map<string, EntryMetadata>\n  opts: Options\n}) {\n  const { compilation, metadataByEntry, opts } = params\n  return () => {\n    const middlewareManifest: MiddlewareManifest = {\n      version: MANIFEST_VERSION,\n      middleware: {},\n      functions: {},\n      sortedMiddleware: [],\n    }\n\n    const hasInstrumentationHook = compilation.entrypoints.has(\n      INSTRUMENTATION_HOOK_FILENAME\n    )\n\n    // we only emit this entry for the edge runtime since it doesn't have access to a routes manifest\n    // and we don't need to provide the entire route manifest, just the interception routes.\n    const interceptionRewrites = JSON.stringify(\n      opts.rewrites.beforeFiles.filter(isInterceptionRouteRewrite)\n    )\n    compilation.emitAsset(\n      `${INTERCEPTION_ROUTE_REWRITE_MANIFEST}.js`,\n      new sources.RawSource(\n        `self.__INTERCEPTION_ROUTE_REWRITE_MANIFEST=${JSON.stringify(\n          interceptionRewrites\n        )}`\n      ) as unknown as webpack.sources.RawSource\n    )\n\n    for (const entrypoint of compilation.entrypoints.values()) {\n      if (!entrypoint.name) {\n        continue\n      }\n\n      // There should always be metadata for the entrypoint.\n      const metadata = metadataByEntry.get(entrypoint.name)\n      const page =\n        metadata?.edgeMiddleware?.page ||\n        metadata?.edgeSSR?.page ||\n        metadata?.edgeApiFunction?.page\n      if (!page) {\n        continue\n      }\n\n      const matcherSource = metadata.edgeSSR?.isAppDir\n        ? normalizeAppPath(page)\n        : page\n\n      const catchAll = !metadata.edgeSSR && !metadata.edgeApiFunction\n\n      const { namedRegex } = getNamedMiddlewareRegex(matcherSource, {\n        catchAll,\n      })\n      const matchers = metadata?.edgeMiddleware?.matchers ?? [\n        {\n          regexp: namedRegex,\n          originalSource: page === '/' && catchAll ? '/:path*' : matcherSource,\n        },\n      ]\n\n      const isEdgeFunction = !!(metadata.edgeApiFunction || metadata.edgeSSR)\n      const edgeFunctionDefinition: EdgeFunctionDefinition = {\n        files: getEntryFiles(\n          entrypoint.getFiles(),\n          metadata,\n          hasInstrumentationHook,\n          opts\n        ),\n        name: entrypoint.name,\n        page: page,\n        matchers,\n        wasm: Array.from(metadata.wasmBindings, ([name, filePath]) => ({\n          name,\n          filePath,\n        })),\n        assets: Array.from(metadata.assetBindings, ([name, filePath]) => ({\n          name,\n          filePath,\n        })),\n        env: opts.edgeEnvironments,\n        ...(metadata.regions && { regions: metadata.regions }),\n      }\n\n      if (isEdgeFunction) {\n        middlewareManifest.functions[page] = edgeFunctionDefinition\n      } else {\n        middlewareManifest.middleware[page] = edgeFunctionDefinition\n      }\n    }\n\n    middlewareManifest.sortedMiddleware = getSortedRoutes(\n      Object.keys(middlewareManifest.middleware)\n    )\n\n    compilation.emitAsset(\n      MIDDLEWARE_MANIFEST,\n      new sources.RawSource(\n        JSON.stringify(middlewareManifest, null, 2)\n      ) as unknown as webpack.sources.RawSource\n    )\n  }\n}\n\nfunction buildWebpackError({\n  message,\n  loc,\n  compilation,\n  entryModule,\n  parser,\n}: {\n  message: string\n  loc?: any\n  compilation: webpack.Compilation\n  entryModule?: webpack.Module\n  parser?: webpack.javascript.JavascriptParser\n}) {\n  const error = new compilation.compiler.webpack.WebpackError(message)\n  error.name = NAME\n  const module = entryModule ?? parser?.state.current\n  if (module) {\n    error.module = module\n  }\n  error.loc = loc\n  return error\n}\n\nfunction isInMiddlewareLayer(parser: webpack.javascript.JavascriptParser) {\n  const layer = parser.state.module?.layer\n  return layer === WEBPACK_LAYERS.middleware || layer === WEBPACK_LAYERS.apiEdge\n}\n\nfunction isNodeJsModule(moduleName: string) {\n  return require('module').builtinModules.includes(moduleName)\n}\n\nfunction isDynamicCodeEvaluationAllowed(\n  fileName: string,\n  middlewareConfig?: MiddlewareConfig,\n  rootDir?: string\n) {\n  // Some packages are known to use `eval` but are safe to use in the Edge\n  // Runtime because the dynamic code will never be executed.\n  if (\n    KNOWN_SAFE_DYNAMIC_PACKAGES.some((pkg) =>\n      fileName.includes(`/node_modules/${pkg}/`.replace(/\\//g, path.sep))\n    )\n  ) {\n    return true\n  }\n\n  const name = fileName.replace(rootDir ?? '', '')\n\n  return picomatch(middlewareConfig?.unstable_allowDynamic ?? [], {\n    dot: true,\n  })(name)\n}\n\nfunction buildUnsupportedApiError({\n  apiName,\n  loc,\n  ...rest\n}: {\n  apiName: string\n  loc: any\n  compilation: webpack.Compilation\n  parser: webpack.javascript.JavascriptParser\n}) {\n  return buildWebpackError({\n    message: `A Node.js API is used (${apiName} at line: ${loc.start.line}) which is not supported in the Edge Runtime.\nLearn more: https://nextjs.org/docs/api-reference/edge-runtime`,\n    loc,\n    ...rest,\n  })\n}\n\nfunction registerUnsupportedApiHooks(\n  parser: webpack.javascript.JavascriptParser,\n  compilation: webpack.Compilation\n) {\n  for (const expression of EDGE_UNSUPPORTED_NODE_APIS) {\n    const warnForUnsupportedApi = (node: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n      compilation.warnings.push(\n        buildUnsupportedApiError({\n          compilation,\n          parser,\n          apiName: expression,\n          ...node,\n        })\n      )\n      return true\n    }\n    parser.hooks.call.for(expression).tap(NAME, warnForUnsupportedApi)\n    parser.hooks.expression.for(expression).tap(NAME, warnForUnsupportedApi)\n    parser.hooks.callMemberChain\n      .for(expression)\n      .tap(NAME, warnForUnsupportedApi)\n    parser.hooks.expressionMemberChain\n      .for(expression)\n      .tap(NAME, warnForUnsupportedApi)\n  }\n\n  const warnForUnsupportedProcessApi = (node: any, [callee]: string[]) => {\n    if (!isInMiddlewareLayer(parser) || callee === 'env') {\n      return\n    }\n    compilation.warnings.push(\n      buildUnsupportedApiError({\n        compilation,\n        parser,\n        apiName: `process.${callee}`,\n        ...node,\n      })\n    )\n    return true\n  }\n\n  parser.hooks.callMemberChain\n    .for('process')\n    .tap(NAME, warnForUnsupportedProcessApi)\n  parser.hooks.expressionMemberChain\n    .for('process')\n    .tap(NAME, warnForUnsupportedProcessApi)\n}\n\nfunction getCodeAnalyzer(params: {\n  dev: boolean\n  compiler: webpack.Compiler\n  compilation: webpack.Compilation\n}) {\n  return (parser: webpack.javascript.JavascriptParser) => {\n    const {\n      dev,\n      compiler: { webpack: wp },\n      compilation,\n    } = params\n    const { hooks } = parser\n\n    /**\n     * For an expression this will check the graph to ensure it is being used\n     * by exports. Then it will store in the module buildInfo a boolean to\n     * express that it contains dynamic code and, if it is available, the\n     * module path that is using it.\n     */\n    const handleExpression = () => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      wp.optimize.InnerGraph.onUsage(parser.state, (used = true) => {\n        const buildInfo = getModuleBuildInfo(parser.state.module)\n        if (buildInfo.usingIndirectEval === true || used === false) {\n          return\n        }\n\n        if (!buildInfo.usingIndirectEval || used === true) {\n          buildInfo.usingIndirectEval = used\n          return\n        }\n\n        buildInfo.usingIndirectEval = new Set([\n          ...Array.from(buildInfo.usingIndirectEval),\n          ...Array.from(used),\n        ])\n      })\n    }\n\n    /**\n     * This expression handler allows to wrap a dynamic code expression with a\n     * function call where we can warn about dynamic code not being allowed\n     * but actually execute the expression.\n     */\n    const handleWrapExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      const { ConstDependency } = wp.dependencies\n      const dep1 = new ConstDependency(\n        '__next_eval__(function() { return ',\n        expr.range[0]\n      )\n      dep1.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep1)\n      const dep2 = new ConstDependency('})', expr.range[1])\n      dep2.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep2)\n\n      handleExpression()\n      return true\n    }\n\n    /**\n     * This expression handler allows to wrap a WebAssembly.compile invocation with a\n     * function call where we can warn about WASM code generation not being allowed\n     * but actually execute the expression.\n     */\n    const handleWrapWasmCompileExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      const { ConstDependency } = wp.dependencies\n      const dep1 = new ConstDependency(\n        '__next_webassembly_compile__(function() { return ',\n        expr.range[0]\n      )\n      dep1.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep1)\n      const dep2 = new ConstDependency('})', expr.range[1])\n      dep2.loc = expr.loc\n      parser.state.module.addPresentationalDependency(dep2)\n\n      handleExpression()\n    }\n\n    /**\n     * This expression handler allows to wrap a WebAssembly.instatiate invocation with a\n     * function call where we can warn about WASM code generation not being allowed\n     * but actually execute the expression.\n     *\n     * Note that we don't update `usingIndirectEval`, i.e. we don't abort a production build\n     * since we can't determine statically if the first parameter is a module (legit use) or\n     * a buffer (dynamic code generation).\n     */\n    const handleWrapWasmInstantiateExpression = (expr: any) => {\n      if (!isInMiddlewareLayer(parser)) {\n        return\n      }\n\n      if (dev) {\n        const { ConstDependency } = wp.dependencies\n        const dep1 = new ConstDependency(\n          '__next_webassembly_instantiate__(function() { return ',\n          expr.range[0]\n        )\n        dep1.loc = expr.loc\n        parser.state.module.addPresentationalDependency(dep1)\n        const dep2 = new ConstDependency('})', expr.range[1])\n        dep2.loc = expr.loc\n        parser.state.module.addPresentationalDependency(dep2)\n      }\n    }\n\n    /**\n     * Handler to store original source location of static and dynamic imports into module's buildInfo.\n     */\n    const handleImport = (node: any) => {\n      if (isInMiddlewareLayer(parser) && node.source?.value && node?.loc) {\n        const { module, source } = parser.state\n        const buildInfo = getModuleBuildInfo(module)\n        if (!buildInfo.importLocByPath) {\n          buildInfo.importLocByPath = new Map()\n        }\n\n        const importedModule = node.source.value?.toString()\n        buildInfo.importLocByPath.set(importedModule, {\n          sourcePosition: {\n            ...node.loc.start,\n            source: module.identifier(),\n          },\n          sourceContent: source.toString(),\n        })\n\n        if (\n          !dev &&\n          isNodeJsModule(importedModule) &&\n          !SUPPORTED_NATIVE_MODULES.includes(importedModule)\n        ) {\n          compilation.warnings.push(\n            buildWebpackError({\n              message: `A Node.js module is loaded ('${importedModule}' at line ${node.loc.start.line}) which is not supported in the Edge Runtime.\nLearn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`,\n              compilation,\n              parser,\n              ...node,\n            })\n          )\n        }\n      }\n    }\n\n    /**\n     * A noop handler to skip analyzing some cases.\n     * Order matters: for it to work, it must be registered first\n     */\n    const skip = () => (isInMiddlewareLayer(parser) ? true : undefined)\n\n    for (const prefix of ['', 'global.']) {\n      hooks.expression.for(`${prefix}Function.prototype`).tap(NAME, skip)\n      hooks.expression.for(`${prefix}Function.bind`).tap(NAME, skip)\n      hooks.call.for(`${prefix}eval`).tap(NAME, handleWrapExpression)\n      hooks.call.for(`${prefix}Function`).tap(NAME, handleWrapExpression)\n      hooks.new.for(`${prefix}Function`).tap(NAME, handleWrapExpression)\n      hooks.call\n        .for(`${prefix}WebAssembly.compile`)\n        .tap(NAME, handleWrapWasmCompileExpression)\n      hooks.call\n        .for(`${prefix}WebAssembly.instantiate`)\n        .tap(NAME, handleWrapWasmInstantiateExpression)\n    }\n\n    hooks.importCall.tap(NAME, handleImport)\n    hooks.import.tap(NAME, handleImport)\n\n    if (!dev) {\n      // do not issue compilation warning on dev: invoking code will provide details\n      registerUnsupportedApiHooks(parser, compilation)\n    }\n  }\n}\n\nfunction getExtractMetadata(params: {\n  compilation: webpack.Compilation\n  compiler: webpack.Compiler\n  dev: boolean\n  metadataByEntry: Map<string, EntryMetadata>\n}): () => Promise<void> {\n  const { dev, compilation, metadataByEntry, compiler } = params\n  const { webpack: wp } = compiler\n  return async () => {\n    metadataByEntry.clear()\n    const telemetry: Telemetry | undefined = traceGlobals.get('telemetry')\n\n    for (const [entryName, entry] of compilation.entries) {\n      if (entry.options.runtime !== EDGE_RUNTIME_WEBPACK) {\n        // Only process edge runtime entries\n        continue\n      }\n      const entryDependency = entry.dependencies?.[0]\n      const resolvedModule =\n        compilation.moduleGraph.getResolvedModule(entryDependency)\n      if (!resolvedModule) {\n        continue\n      }\n      const { rootDir, route } = getModuleBuildInfo(resolvedModule)\n\n      const { moduleGraph } = compilation\n      const modules = new Set<webpack.NormalModule>()\n      const addEntriesFromDependency = (dependency: any) => {\n        const module = moduleGraph.getModule(dependency)\n        if (module) {\n          modules.add(module as webpack.NormalModule)\n        }\n      }\n\n      entry.dependencies.forEach(addEntriesFromDependency)\n      entry.includeDependencies.forEach(addEntriesFromDependency)\n\n      const entryMetadata: EntryMetadata = {\n        wasmBindings: new Map(),\n        assetBindings: new Map(),\n      }\n\n      if (route?.middlewareConfig?.regions) {\n        entryMetadata.regions = route.middlewareConfig.regions\n      }\n\n      if (route?.preferredRegion) {\n        const preferredRegion = route.preferredRegion\n        entryMetadata.regions =\n          // Ensures preferredRegion is always an array in the manifest.\n          typeof preferredRegion === 'string'\n            ? [preferredRegion]\n            : preferredRegion\n      }\n\n      let ogImageGenerationCount = 0\n\n      for (const module of modules) {\n        const buildInfo = getModuleBuildInfo(module)\n\n        /**\n         * Check if it uses the image generation feature.\n         */\n        if (!dev) {\n          const resource = module.resource\n          const hasOGImageGeneration =\n            resource &&\n            /[\\\\/]node_modules[\\\\/]@vercel[\\\\/]og[\\\\/]dist[\\\\/]index\\.(edge|node)\\.js$|[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js$/.test(\n              resource\n            )\n\n          if (hasOGImageGeneration) {\n            ogImageGenerationCount++\n          }\n        }\n\n        /**\n         * When building for production checks if the module is using `eval`\n         * and in such case produces a compilation error. The module has to\n         * be in use.\n         */\n        if (\n          !dev &&\n          buildInfo.usingIndirectEval &&\n          isUsingIndirectEvalAndUsedByExports({\n            module,\n            moduleGraph,\n            runtime: wp.util.runtime.getEntryRuntime(compilation, entryName),\n            usingIndirectEval: buildInfo.usingIndirectEval,\n            wp,\n          })\n        ) {\n          const id = module.identifier()\n          if (/node_modules[\\\\/]regenerator-runtime[\\\\/]runtime\\.js/.test(id)) {\n            continue\n          }\n          if (route?.middlewareConfig?.unstable_allowDynamic) {\n            telemetry?.record({\n              eventName: 'NEXT_EDGE_ALLOW_DYNAMIC_USED',\n              payload: {\n                file: route?.absolutePagePath.replace(rootDir ?? '', ''),\n                config: route?.middlewareConfig,\n                fileWithDynamicCode: module.userRequest.replace(\n                  rootDir ?? '',\n                  ''\n                ),\n              },\n            })\n          }\n          if (\n            !isDynamicCodeEvaluationAllowed(\n              module.userRequest,\n              route?.middlewareConfig,\n              rootDir\n            )\n          ) {\n            const message = `Dynamic Code Evaluation (e. g. 'eval', 'new Function', 'WebAssembly.compile') not allowed in Edge Runtime ${\n              typeof buildInfo.usingIndirectEval !== 'boolean'\n                ? `\\nUsed by ${Array.from(buildInfo.usingIndirectEval).join(\n                    ', '\n                  )}`\n                : ''\n            }\\nLearn More: https://nextjs.org/docs/messages/edge-dynamic-code-evaluation`\n            compilation.errors.push(\n              getDynamicCodeEvaluationError(\n                message,\n                module,\n                compilation,\n                compiler\n              )\n            )\n          }\n        }\n\n        /**\n         * The entry module has to be either a page or a middleware and hold\n         * the corresponding metadata.\n         */\n        if (buildInfo?.nextEdgeSSR) {\n          entryMetadata.edgeSSR = buildInfo.nextEdgeSSR\n        } else if (buildInfo?.nextEdgeMiddleware) {\n          entryMetadata.edgeMiddleware = buildInfo.nextEdgeMiddleware\n        } else if (buildInfo?.nextEdgeApiFunction) {\n          entryMetadata.edgeApiFunction = buildInfo.nextEdgeApiFunction\n        }\n\n        /**\n         * If the module is a WASM module we read the binding information and\n         * append it to the entry wasm bindings.\n         */\n        if (buildInfo?.nextWasmMiddlewareBinding) {\n          entryMetadata.wasmBindings.set(\n            buildInfo.nextWasmMiddlewareBinding.name,\n            buildInfo.nextWasmMiddlewareBinding.filePath\n          )\n        }\n\n        if (buildInfo?.nextAssetMiddlewareBinding) {\n          entryMetadata.assetBindings.set(\n            buildInfo.nextAssetMiddlewareBinding.name,\n            buildInfo.nextAssetMiddlewareBinding.filePath\n          )\n        }\n\n        /**\n         * Append to the list of modules to process outgoingConnections from\n         * the module that is being processed.\n         */\n        for (const conn of getModuleReferencesInOrder(module, moduleGraph)) {\n          if (conn.module) {\n            modules.add(conn.module as webpack.NormalModule)\n          }\n        }\n      }\n\n      telemetry?.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: {\n          featureName: 'vercelImageGeneration',\n          invocationCount: ogImageGenerationCount,\n        },\n      })\n      metadataByEntry.set(entryName, entryMetadata)\n    }\n  }\n}\n\n// These values will be replaced again in edge runtime deployment build.\n// `buildId` represents BUILD_ID to be externalized in env vars.\n// `encryptionKey` represents server action encryption key to be externalized in env vars.\ntype EdgeRuntimeEnvironments = Record<string, string> & {\n  __NEXT_BUILD_ID: string\n  NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: string\n}\n\ninterface Options {\n  dev: boolean\n  sriEnabled: boolean\n  rewrites: CustomRoutes['rewrites']\n  edgeEnvironments: EdgeRuntimeEnvironments\n}\n\nexport default class MiddlewarePlugin {\n  private readonly dev: Options['dev']\n  private readonly sriEnabled: Options['sriEnabled']\n  private readonly rewrites: Options['rewrites']\n  private readonly edgeEnvironments: EdgeRuntimeEnvironments\n\n  constructor({ dev, sriEnabled, rewrites, edgeEnvironments }: Options) {\n    this.dev = dev\n    this.sriEnabled = sriEnabled\n    this.rewrites = rewrites\n    this.edgeEnvironments = edgeEnvironments\n  }\n\n  public apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(NAME, (compilation, params) => {\n      const { hooks } = params.normalModuleFactory\n      /**\n       * This is the static code analysis phase.\n       */\n      const codeAnalyzer = getCodeAnalyzer({\n        dev: this.dev,\n        compiler,\n        compilation,\n      })\n\n      // parser hooks aren't available in rspack\n      if (!process.env.NEXT_RSPACK) {\n        hooks.parser.for('javascript/auto').tap(NAME, codeAnalyzer)\n        hooks.parser.for('javascript/dynamic').tap(NAME, codeAnalyzer)\n        hooks.parser.for('javascript/esm').tap(NAME, codeAnalyzer)\n      }\n\n      /**\n       * Extract all metadata for the entry points in a Map object.\n       */\n      const metadataByEntry = new Map<string, EntryMetadata>()\n      compilation.hooks.finishModules.tapPromise(\n        NAME,\n        getExtractMetadata({\n          compilation,\n          compiler,\n          dev: this.dev,\n          metadataByEntry,\n        })\n      )\n\n      /**\n       * Emit the middleware manifest.\n       */\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'NextJsMiddlewareManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        getCreateAssets({\n          compilation,\n          metadataByEntry,\n          opts: {\n            sriEnabled: this.sriEnabled,\n            rewrites: this.rewrites,\n            edgeEnvironments: this.edgeEnvironments,\n            dev: this.dev,\n          },\n        })\n      )\n    })\n  }\n}\n\nexport const SUPPORTED_NATIVE_MODULES = [\n  'buffer',\n  'events',\n  'assert',\n  'util',\n  'async_hooks',\n] as const\n\nconst supportedEdgePolyfills = new Set<string>(SUPPORTED_NATIVE_MODULES)\n\nexport function getEdgePolyfilledModules() {\n  const records: Record<string, string> = {}\n  for (const mod of SUPPORTED_NATIVE_MODULES) {\n    records[mod] = `commonjs node:${mod}`\n    records[`node:${mod}`] = `commonjs node:${mod}`\n  }\n  return records\n}\n\nexport async function handleWebpackExternalForEdgeRuntime({\n  request,\n  context,\n  contextInfo,\n  getResolve,\n}: {\n  request: string\n  context: string\n  contextInfo: any\n  getResolve: () => any\n}) {\n  if (\n    (contextInfo.issuerLayer === WEBPACK_LAYERS.middleware ||\n      contextInfo.issuerLayer === WEBPACK_LAYERS.apiEdge) &&\n    isNodeJsModule(request) &&\n    !supportedEdgePolyfills.has(request)\n  ) {\n    // allows user to provide and use their polyfills, as we do with buffer.\n    try {\n      await getResolve()(context, request)\n    } catch {\n      return `root globalThis.__import_unsupported('${request}')`\n    }\n  }\n}\n"], "names": ["getNamedMiddlewareRegex", "getModuleBuildInfo", "getSortedRoutes", "webpack", "sources", "picomatch", "path", "EDGE_RUNTIME_WEBPACK", "EDGE_UNSUPPORTED_NODE_APIS", "MIDDLEWARE_BUILD_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "MIDDLEWARE_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "NEXT_FONT_MANIFEST", "SERVER_REFERENCE_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "DYNAMIC_CSS_MANIFEST", "traceGlobals", "EVENT_BUILD_FEATURE_USAGE", "normalizeAppPath", "INSTRUMENTATION_HOOK_FILENAME", "WEBPACK_LAYERS", "isInterceptionRouteRewrite", "getDynamicCodeEvaluationError", "getModuleReferencesInOrder", "KNOWN_SAFE_DYNAMIC_PACKAGES", "require", "NAME", "MANIFEST_VERSION", "isUsingIndirectEvalAndUsedByExports", "args", "moduleGraph", "runtime", "module", "usingIndirectEval", "wp", "exportsInfo", "getExportsInfo", "exportName", "getUsed", "UsageState", "Unused", "getEntryFiles", "entryFiles", "meta", "hasInstrumentationHook", "opts", "files", "edgeSSR", "isServerComponent", "push", "sriEnabled", "filter", "file", "startsWith", "endsWith", "map", "replace", "dev", "isAppDir", "getCreateAssets", "params", "compilation", "metadataByEntry", "middlewareManifest", "version", "middleware", "functions", "sortedMiddleware", "entrypoints", "has", "interceptionRewrites", "JSON", "stringify", "rewrites", "beforeFiles", "emitAsset", "RawSource", "entrypoint", "values", "metadata", "name", "get", "page", "edgeMiddleware", "edgeApiFunction", "matcherSource", "catchAll", "namedRegex", "matchers", "regexp", "originalSource", "isEdgeFunction", "edgeFunctionDefinition", "getFiles", "wasm", "Array", "from", "wasmBindings", "filePath", "assets", "assetBindings", "env", "edgeEnvironments", "regions", "Object", "keys", "buildWebpackError", "message", "loc", "entryModule", "parser", "error", "compiler", "WebpackError", "state", "current", "isInMiddlewareLayer", "layer", "apiEdge", "isNodeJsModule", "moduleName", "builtinModules", "includes", "isDynamicCodeEvaluationAllowed", "fileName", "middlewareConfig", "rootDir", "some", "pkg", "sep", "unstable_allowDynamic", "dot", "buildUnsupportedApiError", "apiName", "rest", "start", "line", "registerUnsupportedApiHooks", "expression", "warnForUnsupportedApi", "node", "warnings", "hooks", "call", "for", "tap", "callMemberChain", "expression<PERSON>ember<PERSON>hain", "warnForUnsupportedProcessApi", "callee", "getCodeAnalyzer", "handleExpression", "optimize", "InnerGraph", "onUsage", "used", "buildInfo", "Set", "handleWrapExpression", "expr", "ConstDependency", "dependencies", "dep1", "range", "addPresentationalDependency", "dep2", "handleWrapWasmCompileExpression", "handleWrapWasmInstantiateExpression", "handleImport", "source", "value", "importLocByPath", "Map", "importedModule", "toString", "set", "sourcePosition", "identifier", "sourceContent", "SUPPORTED_NATIVE_MODULES", "skip", "undefined", "prefix", "new", "importCall", "import", "getExtractMetadata", "clear", "telemetry", "entryName", "entry", "entries", "route", "options", "entryDependency", "resolvedModule", "getResolvedModule", "modules", "addEntriesFromDependency", "dependency", "getModule", "add", "for<PERSON>ach", "includeDependencies", "entryMetadata", "preferredRegion", "ogImageGenerationCount", "resource", "hasOGImageGeneration", "test", "util", "getEntryRuntime", "id", "record", "eventName", "payload", "absolutePagePath", "config", "fileWithDynamicCode", "userRequest", "join", "errors", "nextEdgeSSR", "nextEdgeMiddleware", "nextEdgeApiFunction", "nextWasmMiddlewareBinding", "nextAssetMiddlewareBinding", "conn", "featureName", "invocationCount", "MiddlewarePlugin", "constructor", "apply", "normalModuleFactory", "codeAnalyzer", "process", "NEXT_RSPACK", "finishModules", "tapPromise", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS", "supportedEdgePolyfills", "getEdgePolyfilledModules", "records", "mod", "handleWebpackExternalForEdgeRuntime", "request", "context", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er"], "mappings": "AAMA,SAASA,uBAAuB,QAAQ,+CAA8C;AACtF,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,eAAe,QAAQ,mCAAkC;AAClE,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,OAAOC,eAAe,+BAA8B;AACpD,OAAOC,UAAU,OAAM;AACvB,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,yBAAyB,EACzBC,mBAAmB,EACnBC,kCAAkC,EAClCC,8BAA8B,EAC9BC,kBAAkB,EAClBC,yBAAyB,EACzBC,mCAAmC,EACnCC,oBAAoB,QACf,gCAA+B;AAGtC,SAASC,YAAY,QAAQ,wBAAuB;AACpD,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,SACEC,6BAA6B,EAC7BC,cAAc,QACT,yBAAwB;AAE/B,SAASC,0BAA0B,QAAQ,qDAAoD;AAC/F,SAASC,6BAA6B,QAAQ,gEAA+D;AAC7G,SAASC,0BAA0B,QAAQ,WAAU;AAErD,MAAMC,8BACJC,QAAQ;AA6BV,MAAMC,OAAO;AACb,MAAMC,mBAAmB;AAEzB;;;;CAIC,GACD,SAASC,oCAAoCC,IAM5C;IACC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,EAAE,EAAE,GAAGL;IAChE,IAAI,OAAOI,sBAAsB,WAAW;QAC1C,OAAOA;IACT;IAEA,MAAME,cAAcL,YAAYM,cAAc,CAACJ;IAC/C,KAAK,MAAMK,cAAcJ,kBAAmB;QAC1C,IAAIE,YAAYG,OAAO,CAACD,YAAYN,aAAaG,GAAGK,UAAU,CAACC,MAAM,EAAE;YACrE,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAASC,cACPC,UAAoB,EACpBC,IAAmB,EACnBC,sBAA+B,EAC/BC,IAAa;IAEb,MAAMC,QAAkB,EAAE;IAC1B,IAAIH,KAAKI,OAAO,EAAE;QAChB,IAAIJ,KAAKI,OAAO,CAACC,iBAAiB,EAAE;YAClCF,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEpC,0BAA0B,GAAG,CAAC;YACnD,IAAIgC,KAAKK,UAAU,EAAE;gBACnBJ,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAEtC,+BAA+B,GAAG,CAAC;YAC1D;YACAmC,MAAMG,IAAI,IACLP,WACAS,MAAM,CACL,CAACC,OACCA,KAAKC,UAAU,CAAC,WAAW,CAACD,KAAKE,QAAQ,CAAC,mBAE7CC,GAAG,CACF,CAACH,OACC,YACAA,KAAKI,OAAO,CAAC,SAAS,MAAMhD,4BAA4B;QAGlE;QACA,IAAI,CAACqC,KAAKY,GAAG,IAAI,CAACd,KAAKI,OAAO,CAACW,QAAQ,EAAE;YACvCZ,MAAMG,IAAI,CAAC,CAAC,OAAO,EAAElC,qBAAqB,GAAG,CAAC;QAChD;QAEA+B,MAAMG,IAAI,CACR,CAAC,OAAO,EAAE1C,0BAA0B,GAAG,CAAC,EACxC,CAAC,OAAO,EAAEG,mCAAmC,GAAG,CAAC,EACjD,CAAC,OAAO,EAAEE,mBAAmB,GAAG,CAAC,EACjC,CAAC,OAAO,EAAEE,oCAAoC,GAAG,CAAC;IAEtD;IAEA,IAAI8B,wBAAwB;QAC1BE,MAAMG,IAAI,CAAC,CAAC,YAAY,EAAE9B,8BAA8B,GAAG,CAAC;IAC9D;IAEA2B,MAAMG,IAAI,IACLP,WACAS,MAAM,CAAC,CAACC,OAAS,CAACA,KAAKE,QAAQ,CAAC,mBAChCC,GAAG,CAAC,CAACH,OAAS,YAAYA;IAG/B,OAAON;AACT;AAEA,SAASa,gBAAgBC,MAIxB;IACC,MAAM,EAAEC,WAAW,EAAEC,eAAe,EAAEjB,IAAI,EAAE,GAAGe;IAC/C,OAAO;QACL,MAAMG,qBAAyC;YAC7CC,SAASrC;YACTsC,YAAY,CAAC;YACbC,WAAW,CAAC;YACZC,kBAAkB,EAAE;QACtB;QAEA,MAAMvB,yBAAyBiB,YAAYO,WAAW,CAACC,GAAG,CACxDlD;QAGF,iGAAiG;QACjG,wFAAwF;QACxF,MAAMmD,uBAAuBC,KAAKC,SAAS,CACzC3B,KAAK4B,QAAQ,CAACC,WAAW,CAACvB,MAAM,CAAC9B;QAEnCwC,YAAYc,SAAS,CACnB,GAAG7D,oCAAoC,GAAG,CAAC,EAC3C,IAAIZ,QAAQ0E,SAAS,CACnB,CAAC,2CAA2C,EAAEL,KAAKC,SAAS,CAC1DF,uBACC;QAIP,KAAK,MAAMO,cAAchB,YAAYO,WAAW,CAACU,MAAM,GAAI;gBAQvDC,0BACAA,mBACAA,2BAKoBA,oBASLA;YAvBjB,IAAI,CAACF,WAAWG,IAAI,EAAE;gBACpB;YACF;YAEA,sDAAsD;YACtD,MAAMD,WAAWjB,gBAAgBmB,GAAG,CAACJ,WAAWG,IAAI;YACpD,MAAME,OACJH,CAAAA,6BAAAA,2BAAAA,SAAUI,cAAc,qBAAxBJ,yBAA0BG,IAAI,MAC9BH,6BAAAA,oBAAAA,SAAUhC,OAAO,qBAAjBgC,kBAAmBG,IAAI,MACvBH,6BAAAA,4BAAAA,SAAUK,eAAe,qBAAzBL,0BAA2BG,IAAI;YACjC,IAAI,CAACA,MAAM;gBACT;YACF;YAEA,MAAMG,gBAAgBN,EAAAA,qBAAAA,SAAShC,OAAO,qBAAhBgC,mBAAkBrB,QAAQ,IAC5CxC,iBAAiBgE,QACjBA;YAEJ,MAAMI,WAAW,CAACP,SAAShC,OAAO,IAAI,CAACgC,SAASK,eAAe;YAE/D,MAAM,EAAEG,UAAU,EAAE,GAAGzF,wBAAwBuF,eAAe;gBAC5DC;YACF;YACA,MAAME,WAAWT,CAAAA,6BAAAA,4BAAAA,SAAUI,cAAc,qBAAxBJ,0BAA0BS,QAAQ,KAAI;gBACrD;oBACEC,QAAQF;oBACRG,gBAAgBR,SAAS,OAAOI,WAAW,YAAYD;gBACzD;aACD;YAED,MAAMM,iBAAiB,CAAC,CAAEZ,CAAAA,SAASK,eAAe,IAAIL,SAAShC,OAAO,AAAD;YACrE,MAAM6C,yBAAiD;gBACrD9C,OAAOL,cACLoC,WAAWgB,QAAQ,IACnBd,UACAnC,wBACAC;gBAEFmC,MAAMH,WAAWG,IAAI;gBACrBE,MAAMA;gBACNM;gBACAM,MAAMC,MAAMC,IAAI,CAACjB,SAASkB,YAAY,EAAE,CAAC,CAACjB,MAAMkB,SAAS,GAAM,CAAA;wBAC7DlB;wBACAkB;oBACF,CAAA;gBACAC,QAAQJ,MAAMC,IAAI,CAACjB,SAASqB,aAAa,EAAE,CAAC,CAACpB,MAAMkB,SAAS,GAAM,CAAA;wBAChElB;wBACAkB;oBACF,CAAA;gBACAG,KAAKxD,KAAKyD,gBAAgB;gBAC1B,GAAIvB,SAASwB,OAAO,IAAI;oBAAEA,SAASxB,SAASwB,OAAO;gBAAC,CAAC;YACvD;YAEA,IAAIZ,gBAAgB;gBAClB5B,mBAAmBG,SAAS,CAACgB,KAAK,GAAGU;YACvC,OAAO;gBACL7B,mBAAmBE,UAAU,CAACiB,KAAK,GAAGU;YACxC;QACF;QAEA7B,mBAAmBI,gBAAgB,GAAGnE,gBACpCwG,OAAOC,IAAI,CAAC1C,mBAAmBE,UAAU;QAG3CJ,YAAYc,SAAS,CACnBlE,qBACA,IAAIP,QAAQ0E,SAAS,CACnBL,KAAKC,SAAS,CAACT,oBAAoB,MAAM;IAG/C;AACF;AAEA,SAAS2C,kBAAkB,EACzBC,OAAO,EACPC,GAAG,EACH/C,WAAW,EACXgD,WAAW,EACXC,MAAM,EAOP;IACC,MAAMC,QAAQ,IAAIlD,YAAYmD,QAAQ,CAAC/G,OAAO,CAACgH,YAAY,CAACN;IAC5DI,MAAM/B,IAAI,GAAGtD;IACb,MAAMM,SAAS6E,gBAAeC,0BAAAA,OAAQI,KAAK,CAACC,OAAO;IACnD,IAAInF,QAAQ;QACV+E,MAAM/E,MAAM,GAAGA;IACjB;IACA+E,MAAMH,GAAG,GAAGA;IACZ,OAAOG;AACT;AAEA,SAASK,oBAAoBN,MAA2C;QACxDA;IAAd,MAAMO,SAAQP,uBAAAA,OAAOI,KAAK,CAAClF,MAAM,qBAAnB8E,qBAAqBO,KAAK;IACxC,OAAOA,UAAUjG,eAAe6C,UAAU,IAAIoD,UAAUjG,eAAekG,OAAO;AAChF;AAEA,SAASC,eAAeC,UAAkB;IACxC,OAAO/F,QAAQ,UAAUgG,cAAc,CAACC,QAAQ,CAACF;AACnD;AAEA,SAASG,+BACPC,QAAgB,EAChBC,gBAAmC,EACnCC,OAAgB;IAEhB,wEAAwE;IACxE,2DAA2D;IAC3D,IACEtG,4BAA4BuG,IAAI,CAAC,CAACC,MAChCJ,SAASF,QAAQ,CAAC,CAAC,cAAc,EAAEM,IAAI,CAAC,CAAC,CAACxE,OAAO,CAAC,OAAOpD,KAAK6H,GAAG,KAEnE;QACA,OAAO;IACT;IAEA,MAAMjD,OAAO4C,SAASpE,OAAO,CAACsE,WAAW,IAAI;IAE7C,OAAO3H,UAAU0H,CAAAA,oCAAAA,iBAAkBK,qBAAqB,KAAI,EAAE,EAAE;QAC9DC,KAAK;IACP,GAAGnD;AACL;AAEA,SAASoD,yBAAyB,EAChCC,OAAO,EACPzB,GAAG,EACH,GAAG0B,MAMJ;IACC,OAAO5B,kBAAkB;QACvBC,SAAS,CAAC,uBAAuB,EAAE0B,QAAQ,UAAU,EAAEzB,IAAI2B,KAAK,CAACC,IAAI,CAAC;8DACZ,CAAC;QAC3D5B;QACA,GAAG0B,IAAI;IACT;AACF;AAEA,SAASG,4BACP3B,MAA2C,EAC3CjD,WAAgC;IAEhC,KAAK,MAAM6E,cAAcpI,2BAA4B;QACnD,MAAMqI,wBAAwB,CAACC;YAC7B,IAAI,CAACxB,oBAAoBN,SAAS;gBAChC;YACF;YACAjD,YAAYgF,QAAQ,CAAC5F,IAAI,CACvBmF,yBAAyB;gBACvBvE;gBACAiD;gBACAuB,SAASK;gBACT,GAAGE,IAAI;YACT;YAEF,OAAO;QACT;QACA9B,OAAOgC,KAAK,CAACC,IAAI,CAACC,GAAG,CAACN,YAAYO,GAAG,CAACvH,MAAMiH;QAC5C7B,OAAOgC,KAAK,CAACJ,UAAU,CAACM,GAAG,CAACN,YAAYO,GAAG,CAACvH,MAAMiH;QAClD7B,OAAOgC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAACN,YACJO,GAAG,CAACvH,MAAMiH;QACb7B,OAAOgC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAACN,YACJO,GAAG,CAACvH,MAAMiH;IACf;IAEA,MAAMS,+BAA+B,CAACR,MAAW,CAACS,OAAiB;QACjE,IAAI,CAACjC,oBAAoBN,WAAWuC,WAAW,OAAO;YACpD;QACF;QACAxF,YAAYgF,QAAQ,CAAC5F,IAAI,CACvBmF,yBAAyB;YACvBvE;YACAiD;YACAuB,SAAS,CAAC,QAAQ,EAAEgB,QAAQ;YAC5B,GAAGT,IAAI;QACT;QAEF,OAAO;IACT;IAEA9B,OAAOgC,KAAK,CAACI,eAAe,CACzBF,GAAG,CAAC,WACJC,GAAG,CAACvH,MAAM0H;IACbtC,OAAOgC,KAAK,CAACK,qBAAqB,CAC/BH,GAAG,CAAC,WACJC,GAAG,CAACvH,MAAM0H;AACf;AAEA,SAASE,gBAAgB1F,MAIxB;IACC,OAAO,CAACkD;QACN,MAAM,EACJrD,GAAG,EACHuD,UAAU,EAAE/G,SAASiC,EAAE,EAAE,EACzB2B,WAAW,EACZ,GAAGD;QACJ,MAAM,EAAEkF,KAAK,EAAE,GAAGhC;QAElB;;;;;KAKC,GACD,MAAMyC,mBAAmB;YACvB,IAAI,CAACnC,oBAAoBN,SAAS;gBAChC;YACF;YAEA5E,GAAGsH,QAAQ,CAACC,UAAU,CAACC,OAAO,CAAC5C,OAAOI,KAAK,EAAE,CAACyC,OAAO,IAAI;gBACvD,MAAMC,YAAY7J,mBAAmB+G,OAAOI,KAAK,CAAClF,MAAM;gBACxD,IAAI4H,UAAU3H,iBAAiB,KAAK,QAAQ0H,SAAS,OAAO;oBAC1D;gBACF;gBAEA,IAAI,CAACC,UAAU3H,iBAAiB,IAAI0H,SAAS,MAAM;oBACjDC,UAAU3H,iBAAiB,GAAG0H;oBAC9B;gBACF;gBAEAC,UAAU3H,iBAAiB,GAAG,IAAI4H,IAAI;uBACjC9D,MAAMC,IAAI,CAAC4D,UAAU3H,iBAAiB;uBACtC8D,MAAMC,IAAI,CAAC2D;iBACf;YACH;QACF;QAEA;;;;KAIC,GACD,MAAMG,uBAAuB,CAACC;YAC5B,IAAI,CAAC3C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEkD,eAAe,EAAE,GAAG9H,GAAG+H,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,sCACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAKtD,GAAG,GAAGmD,KAAKnD,GAAG;YACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAKzD,GAAG,GAAGmD,KAAKnD,GAAG;YACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACC;YAEhDd;YACA,OAAO;QACT;QAEA;;;;KAIC,GACD,MAAMe,kCAAkC,CAACP;YACvC,IAAI,CAAC3C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,MAAM,EAAEkD,eAAe,EAAE,GAAG9H,GAAG+H,YAAY;YAC3C,MAAMC,OAAO,IAAIF,gBACf,qDACAD,KAAKI,KAAK,CAAC,EAAE;YAEfD,KAAKtD,GAAG,GAAGmD,KAAKnD,GAAG;YACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACF;YAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;YACpDE,KAAKzD,GAAG,GAAGmD,KAAKnD,GAAG;YACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACC;YAEhDd;QACF;QAEA;;;;;;;;KAQC,GACD,MAAMgB,sCAAsC,CAACR;YAC3C,IAAI,CAAC3C,oBAAoBN,SAAS;gBAChC;YACF;YAEA,IAAIrD,KAAK;gBACP,MAAM,EAAEuG,eAAe,EAAE,GAAG9H,GAAG+H,YAAY;gBAC3C,MAAMC,OAAO,IAAIF,gBACf,yDACAD,KAAKI,KAAK,CAAC,EAAE;gBAEfD,KAAKtD,GAAG,GAAGmD,KAAKnD,GAAG;gBACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACF;gBAChD,MAAMG,OAAO,IAAIL,gBAAgB,MAAMD,KAAKI,KAAK,CAAC,EAAE;gBACpDE,KAAKzD,GAAG,GAAGmD,KAAKnD,GAAG;gBACnBE,OAAOI,KAAK,CAAClF,MAAM,CAACoI,2BAA2B,CAACC;YAClD;QACF;QAEA;;KAEC,GACD,MAAMG,eAAe,CAAC5B;gBACeA;YAAnC,IAAIxB,oBAAoBN,aAAW8B,eAAAA,KAAK6B,MAAM,qBAAX7B,aAAa8B,KAAK,MAAI9B,wBAAAA,KAAMhC,GAAG,GAAE;oBAO3CgC;gBANvB,MAAM,EAAE5G,MAAM,EAAEyI,MAAM,EAAE,GAAG3D,OAAOI,KAAK;gBACvC,MAAM0C,YAAY7J,mBAAmBiC;gBACrC,IAAI,CAAC4H,UAAUe,eAAe,EAAE;oBAC9Bf,UAAUe,eAAe,GAAG,IAAIC;gBAClC;gBAEA,MAAMC,kBAAiBjC,qBAAAA,KAAK6B,MAAM,CAACC,KAAK,qBAAjB9B,mBAAmBkC,QAAQ;gBAClDlB,UAAUe,eAAe,CAACI,GAAG,CAACF,gBAAgB;oBAC5CG,gBAAgB;wBACd,GAAGpC,KAAKhC,GAAG,CAAC2B,KAAK;wBACjBkC,QAAQzI,OAAOiJ,UAAU;oBAC3B;oBACAC,eAAeT,OAAOK,QAAQ;gBAChC;gBAEA,IACE,CAACrH,OACD8D,eAAesD,mBACf,CAACM,yBAAyBzD,QAAQ,CAACmD,iBACnC;oBACAhH,YAAYgF,QAAQ,CAAC5F,IAAI,CACvByD,kBAAkB;wBAChBC,SAAS,CAAC,6BAA6B,EAAEkE,eAAe,UAAU,EAAEjC,KAAKhC,GAAG,CAAC2B,KAAK,CAACC,IAAI,CAAC;wEAC9B,CAAC;wBAC3D3E;wBACAiD;wBACA,GAAG8B,IAAI;oBACT;gBAEJ;YACF;QACF;QAEA;;;KAGC,GACD,MAAMwC,OAAO,IAAOhE,oBAAoBN,UAAU,OAAOuE;QAEzD,KAAK,MAAMC,UAAU;YAAC;YAAI;SAAU,CAAE;YACpCxC,MAAMJ,UAAU,CAACM,GAAG,CAAC,GAAGsC,OAAO,kBAAkB,CAAC,EAAErC,GAAG,CAACvH,MAAM0J;YAC9DtC,MAAMJ,UAAU,CAACM,GAAG,CAAC,GAAGsC,OAAO,aAAa,CAAC,EAAErC,GAAG,CAACvH,MAAM0J;YACzDtC,MAAMC,IAAI,CAACC,GAAG,CAAC,GAAGsC,OAAO,IAAI,CAAC,EAAErC,GAAG,CAACvH,MAAMoI;YAC1ChB,MAAMC,IAAI,CAACC,GAAG,CAAC,GAAGsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAACvH,MAAMoI;YAC9ChB,MAAMyC,GAAG,CAACvC,GAAG,CAAC,GAAGsC,OAAO,QAAQ,CAAC,EAAErC,GAAG,CAACvH,MAAMoI;YAC7ChB,MAAMC,IAAI,CACPC,GAAG,CAAC,GAAGsC,OAAO,mBAAmB,CAAC,EAClCrC,GAAG,CAACvH,MAAM4I;YACbxB,MAAMC,IAAI,CACPC,GAAG,CAAC,GAAGsC,OAAO,uBAAuB,CAAC,EACtCrC,GAAG,CAACvH,MAAM6I;QACf;QAEAzB,MAAM0C,UAAU,CAACvC,GAAG,CAACvH,MAAM8I;QAC3B1B,MAAM2C,MAAM,CAACxC,GAAG,CAACvH,MAAM8I;QAEvB,IAAI,CAAC/G,KAAK;YACR,8EAA8E;YAC9EgF,4BAA4B3B,QAAQjD;QACtC;IACF;AACF;AAEA,SAAS6H,mBAAmB9H,MAK3B;IACC,MAAM,EAAEH,GAAG,EAAEI,WAAW,EAAEC,eAAe,EAAEkD,QAAQ,EAAE,GAAGpD;IACxD,MAAM,EAAE3D,SAASiC,EAAE,EAAE,GAAG8E;IACxB,OAAO;QACLlD,gBAAgB6H,KAAK;QACrB,MAAMC,YAAmC5K,aAAaiE,GAAG,CAAC;QAE1D,KAAK,MAAM,CAAC4G,WAAWC,MAAM,IAAIjI,YAAYkI,OAAO,CAAE;gBAK5BD,qBAyBpBE;YA7BJ,IAAIF,MAAMG,OAAO,CAAClK,OAAO,KAAK1B,sBAAsB;gBAElD;YACF;YACA,MAAM6L,mBAAkBJ,sBAAAA,MAAM7B,YAAY,qBAAlB6B,mBAAoB,CAAC,EAAE;YAC/C,MAAMK,iBACJtI,YAAY/B,WAAW,CAACsK,iBAAiB,CAACF;YAC5C,IAAI,CAACC,gBAAgB;gBACnB;YACF;YACA,MAAM,EAAErE,OAAO,EAAEkE,KAAK,EAAE,GAAGjM,mBAAmBoM;YAE9C,MAAM,EAAErK,WAAW,EAAE,GAAG+B;YACxB,MAAMwI,UAAU,IAAIxC;YACpB,MAAMyC,2BAA2B,CAACC;gBAChC,MAAMvK,SAASF,YAAY0K,SAAS,CAACD;gBACrC,IAAIvK,QAAQ;oBACVqK,QAAQI,GAAG,CAACzK;gBACd;YACF;YAEA8J,MAAM7B,YAAY,CAACyC,OAAO,CAACJ;YAC3BR,MAAMa,mBAAmB,CAACD,OAAO,CAACJ;YAElC,MAAMM,gBAA+B;gBACnC3G,cAAc,IAAI2E;gBAClBxE,eAAe,IAAIwE;YACrB;YAEA,IAAIoB,0BAAAA,0BAAAA,MAAOnE,gBAAgB,qBAAvBmE,wBAAyBzF,OAAO,EAAE;gBACpCqG,cAAcrG,OAAO,GAAGyF,MAAMnE,gBAAgB,CAACtB,OAAO;YACxD;YAEA,IAAIyF,yBAAAA,MAAOa,eAAe,EAAE;gBAC1B,MAAMA,kBAAkBb,MAAMa,eAAe;gBAC7CD,cAAcrG,OAAO,GACnB,8DAA8D;gBAC9D,OAAOsG,oBAAoB,WACvB;oBAACA;iBAAgB,GACjBA;YACR;YAEA,IAAIC,yBAAyB;YAE7B,KAAK,MAAM9K,UAAUqK,QAAS;gBAC5B,MAAMzC,YAAY7J,mBAAmBiC;gBAErC;;SAEC,GACD,IAAI,CAACyB,KAAK;oBACR,MAAMsJ,WAAW/K,OAAO+K,QAAQ;oBAChC,MAAMC,uBACJD,YACA,oJAAoJE,IAAI,CACtJF;oBAGJ,IAAIC,sBAAsB;wBACxBF;oBACF;gBACF;gBAEA;;;;SAIC,GACD,IACE,CAACrJ,OACDmG,UAAU3H,iBAAiB,IAC3BL,oCAAoC;oBAClCI;oBACAF;oBACAC,SAASG,GAAGgL,IAAI,CAACnL,OAAO,CAACoL,eAAe,CAACtJ,aAAagI;oBACtD5J,mBAAmB2H,UAAU3H,iBAAiB;oBAC9CC;gBACF,IACA;wBAKI8J;oBAJJ,MAAMoB,KAAKpL,OAAOiJ,UAAU;oBAC5B,IAAI,uDAAuDgC,IAAI,CAACG,KAAK;wBACnE;oBACF;oBACA,IAAIpB,0BAAAA,2BAAAA,MAAOnE,gBAAgB,qBAAvBmE,yBAAyB9D,qBAAqB,EAAE;wBAClD0D,6BAAAA,UAAWyB,MAAM,CAAC;4BAChBC,WAAW;4BACXC,SAAS;gCACPnK,IAAI,EAAE4I,yBAAAA,MAAOwB,gBAAgB,CAAChK,OAAO,CAACsE,WAAW,IAAI;gCACrD2F,MAAM,EAAEzB,yBAAAA,MAAOnE,gBAAgB;gCAC/B6F,qBAAqB1L,OAAO2L,WAAW,CAACnK,OAAO,CAC7CsE,WAAW,IACX;4BAEJ;wBACF;oBACF;oBACA,IACE,CAACH,+BACC3F,OAAO2L,WAAW,EAClB3B,yBAAAA,MAAOnE,gBAAgB,EACvBC,UAEF;wBACA,MAAMnB,UAAU,CAAC,0GAA0G,EACzH,OAAOiD,UAAU3H,iBAAiB,KAAK,YACnC,CAAC,UAAU,EAAE8D,MAAMC,IAAI,CAAC4D,UAAU3H,iBAAiB,EAAE2L,IAAI,CACvD,OACC,GACH,GACL,2EAA2E,CAAC;wBAC7E/J,YAAYgK,MAAM,CAAC5K,IAAI,CACrB3B,8BACEqF,SACA3E,QACA6B,aACAmD;oBAGN;gBACF;gBAEA;;;SAGC,GACD,IAAI4C,6BAAAA,UAAWkE,WAAW,EAAE;oBAC1BlB,cAAc7J,OAAO,GAAG6G,UAAUkE,WAAW;gBAC/C,OAAO,IAAIlE,6BAAAA,UAAWmE,kBAAkB,EAAE;oBACxCnB,cAAczH,cAAc,GAAGyE,UAAUmE,kBAAkB;gBAC7D,OAAO,IAAInE,6BAAAA,UAAWoE,mBAAmB,EAAE;oBACzCpB,cAAcxH,eAAe,GAAGwE,UAAUoE,mBAAmB;gBAC/D;gBAEA;;;SAGC,GACD,IAAIpE,6BAAAA,UAAWqE,yBAAyB,EAAE;oBACxCrB,cAAc3G,YAAY,CAAC8E,GAAG,CAC5BnB,UAAUqE,yBAAyB,CAACjJ,IAAI,EACxC4E,UAAUqE,yBAAyB,CAAC/H,QAAQ;gBAEhD;gBAEA,IAAI0D,6BAAAA,UAAWsE,0BAA0B,EAAE;oBACzCtB,cAAcxG,aAAa,CAAC2E,GAAG,CAC7BnB,UAAUsE,0BAA0B,CAAClJ,IAAI,EACzC4E,UAAUsE,0BAA0B,CAAChI,QAAQ;gBAEjD;gBAEA;;;SAGC,GACD,KAAK,MAAMiI,QAAQ5M,2BAA2BS,QAAQF,aAAc;oBAClE,IAAIqM,KAAKnM,MAAM,EAAE;wBACfqK,QAAQI,GAAG,CAAC0B,KAAKnM,MAAM;oBACzB;gBACF;YACF;YAEA4J,6BAAAA,UAAWyB,MAAM,CAAC;gBAChBC,WAAWrM;gBACXsM,SAAS;oBACPa,aAAa;oBACbC,iBAAiBvB;gBACnB;YACF;YACAhJ,gBAAgBiH,GAAG,CAACc,WAAWe;QACjC;IACF;AACF;AAiBA,eAAe,MAAM0B;IAMnBC,YAAY,EAAE9K,GAAG,EAAEP,UAAU,EAAEuB,QAAQ,EAAE6B,gBAAgB,EAAW,CAAE;QACpE,IAAI,CAAC7C,GAAG,GAAGA;QACX,IAAI,CAACP,UAAU,GAAGA;QAClB,IAAI,CAACuB,QAAQ,GAAGA;QAChB,IAAI,CAAC6B,gBAAgB,GAAGA;IAC1B;IAEOkI,MAAMxH,QAA0B,EAAE;QACvCA,SAAS8B,KAAK,CAACjF,WAAW,CAACoF,GAAG,CAACvH,MAAM,CAACmC,aAAaD;YACjD,MAAM,EAAEkF,KAAK,EAAE,GAAGlF,OAAO6K,mBAAmB;YAC5C;;OAEC,GACD,MAAMC,eAAepF,gBAAgB;gBACnC7F,KAAK,IAAI,CAACA,GAAG;gBACbuD;gBACAnD;YACF;YAEA,0CAA0C;YAC1C,IAAI,CAAC8K,QAAQtI,GAAG,CAACuI,WAAW,EAAE;gBAC5B9F,MAAMhC,MAAM,CAACkC,GAAG,CAAC,mBAAmBC,GAAG,CAACvH,MAAMgN;gBAC9C5F,MAAMhC,MAAM,CAACkC,GAAG,CAAC,sBAAsBC,GAAG,CAACvH,MAAMgN;gBACjD5F,MAAMhC,MAAM,CAACkC,GAAG,CAAC,kBAAkBC,GAAG,CAACvH,MAAMgN;YAC/C;YAEA;;OAEC,GACD,MAAM5K,kBAAkB,IAAI8G;YAC5B/G,YAAYiF,KAAK,CAAC+F,aAAa,CAACC,UAAU,CACxCpN,MACAgK,mBAAmB;gBACjB7H;gBACAmD;gBACAvD,KAAK,IAAI,CAACA,GAAG;gBACbK;YACF;YAGF;;OAEC,GACDD,YAAYiF,KAAK,CAACiG,aAAa,CAAC9F,GAAG,CACjC;gBACEjE,MAAM;gBACNgK,OAAO/O,QAAQgP,WAAW,CAACC,8BAA8B;YAC3D,GACAvL,gBAAgB;gBACdE;gBACAC;gBACAjB,MAAM;oBACJK,YAAY,IAAI,CAACA,UAAU;oBAC3BuB,UAAU,IAAI,CAACA,QAAQ;oBACvB6B,kBAAkB,IAAI,CAACA,gBAAgB;oBACvC7C,KAAK,IAAI,CAACA,GAAG;gBACf;YACF;QAEJ;IACF;AACF;AAEA,OAAO,MAAM0H,2BAA2B;IACtC;IACA;IACA;IACA;IACA;CACD,CAAS;AAEV,MAAMgE,yBAAyB,IAAItF,IAAYsB;AAE/C,OAAO,SAASiE;IACd,MAAMC,UAAkC,CAAC;IACzC,KAAK,MAAMC,OAAOnE,yBAA0B;QAC1CkE,OAAO,CAACC,IAAI,GAAG,CAAC,cAAc,EAAEA,KAAK;QACrCD,OAAO,CAAC,CAAC,KAAK,EAAEC,KAAK,CAAC,GAAG,CAAC,cAAc,EAAEA,KAAK;IACjD;IACA,OAAOD;AACT;AAEA,OAAO,eAAeE,oCAAoC,EACxDC,OAAO,EACPC,OAAO,EACPC,WAAW,EACXC,UAAU,EAMX;IACC,IACE,AAACD,CAAAA,YAAYE,WAAW,KAAKxO,eAAe6C,UAAU,IACpDyL,YAAYE,WAAW,KAAKxO,eAAekG,OAAO,AAAD,KACnDC,eAAeiI,YACf,CAACL,uBAAuB9K,GAAG,CAACmL,UAC5B;QACA,wEAAwE;QACxE,IAAI;YACF,MAAMG,aAAaF,SAASD;QAC9B,EAAE,OAAM;YACN,OAAO,CAAC,sCAAsC,EAAEA,QAAQ,EAAE,CAAC;QAC7D;IACF;AACF"}