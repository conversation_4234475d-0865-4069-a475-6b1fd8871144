{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "sourcesContent": ["import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../shared/lib/errors/constants'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: ('html' | 'body')[] = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<html id=\"__next_error__\">\n            <template\n              data-next-error-message=\"Missing ${missingTags\n                .map((c) => `<${c}>`)\n                .join(\n                  missingTags.length > 1 ? ' and ' : ''\n                )} tags in the root layout.\\nRead more at https://nextjs.org/docs/messages/missing-root-layout-tags\"\"\n              data-next-error-digest=\"${MISSING_ROOT_TAGS_ERROR}\"\n              data-next-error-stack=\"\"\n            ></template>\n          `\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n"], "names": ["chainStreams", "continueDynamicHTMLResume", "continueDynamicPrerender", "continueFizzStream", "continueStaticP<PERSON><PERSON>", "createBufferedTransformStream", "createDocumentClosingStream", "createRootLayoutValidatorStream", "renderToInitialFizzStream", "streamFromBuffer", "streamFromString", "streamToBuffer", "streamToString", "voidCatch", "encoder", "TextEncoder", "streams", "length", "Error", "readable", "writable", "TransformStream", "promise", "pipeTo", "preventClose", "i", "nextStream", "then", "lastStream", "catch", "str", "ReadableStream", "start", "controller", "enqueue", "encode", "close", "chunk", "stream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "done", "value", "read", "push", "<PERSON><PERSON><PERSON>", "concat", "signal", "decoder", "TextDecoder", "fatal", "string", "aborted", "decode", "bufferedChunks", "bufferByteLength", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "Uint8Array", "copiedBytes", "bufferedChunk", "set", "byteLength", "undefined", "resolve", "transform", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "hasBytes", "insertion", "encodedInsertion", "index", "indexOfUint8Array", "ENCODED_TAGS", "CLOSED", "HEAD", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "pull", "donePulling", "startPulling", "atLeastOneTask", "err", "error", "CLOSE_TAG", "createMoveSuffixStream", "foundSuffix", "BODY_AND_HTML", "before", "after", "createStripDocumentClosingTagsTransform", "isEquivalentUint8Arrays", "BODY", "HTML", "removeFromUint8Array", "foundHtml", "foundBody", "OPENING", "missingTags", "map", "c", "join", "MISSING_ROOT_TAGS_ERROR", "chainTransformers", "transformers", "transformer", "pipeThrough", "renderStream", "inlinedDataStream", "isStaticGeneration", "getServerInsertedHTML", "getServerInsertedMetadata", "validateRootLayout", "suffixUnclosed", "split", "allReady", "prerenderStream"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IA2BgBA,YAAY;eAAZA;;IAknBMC,yBAAyB;eAAzBA;;IA1DAC,wBAAwB;eAAxBA;;IArDAC,kBAAkB;eAAlBA;;IAgFAC,uBAAuB;eAAvBA;;IAnfNC,6BAA6B;eAA7BA;;IA2iBAC,2BAA2B;eAA3BA;;IApNAC,+BAA+B;eAA/BA;;IA7RAC,yBAAyB;eAAzBA;;IAzGAC,gBAAgB;eAAhBA;;IATAC,gBAAgB;eAAhBA;;IAkBMC,cAAc;eAAdA;;IAkBAC,cAAc;eAAdA;;;wBAvGI;2BACI;iCACE;2BACkB;6BACrB;mCAKtB;4BACiC;AAExC,SAASC;AACP,iFAAiF;AACjF,uFAAuF;AACvF,mBAAmB;AACrB;AAMA,oDAAoD;AACpD,uEAAuE;AACvE,+BAA+B;AAC/B,MAAMC,UAAU,IAAIC;AAEb,SAASf,aACd,GAAGgB,OAA4B;IAE/B,yFAAyF;IACzF,sCAAsC;IACtC,IAAIA,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,qBAAiE,CAAjE,IAAIC,MAAM,yDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgE;IACxE;IAEA,yEAAyE;IACzE,IAAIF,QAAQC,MAAM,KAAK,GAAG;QACxB,OAAOD,OAAO,CAAC,EAAE;IACnB;IAEA,MAAM,EAAEG,QAAQ,EAAEC,QAAQ,EAAE,GAAG,IAAIC;IAEnC,4EAA4E;IAC5E,mEAAmE;IACnE,IAAIC,UAAUN,OAAO,CAAC,EAAE,CAACO,MAAM,CAACH,UAAU;QAAEI,cAAc;IAAK;IAE/D,IAAIC,IAAI;IACR,MAAOA,IAAIT,QAAQC,MAAM,GAAG,GAAGQ,IAAK;QAClC,MAAMC,aAAaV,OAAO,CAACS,EAAE;QAC7BH,UAAUA,QAAQK,IAAI,CAAC,IACrBD,WAAWH,MAAM,CAACH,UAAU;gBAAEI,cAAc;YAAK;IAErD;IAEA,kFAAkF;IAClF,wEAAwE;IACxE,MAAMI,aAAaZ,OAAO,CAACS,EAAE;IAC7BH,UAAUA,QAAQK,IAAI,CAAC,IAAMC,WAAWL,MAAM,CAACH;IAE/C,0EAA0E;IAC1E,gDAAgD;IAChDE,QAAQO,KAAK,CAAChB;IAEd,OAAOM;AACT;AAEO,SAAST,iBAAiBoB,GAAW;IAC1C,OAAO,IAAIC,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACL;YAClCG,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,SAAS3B,iBAAiB4B,KAAa;IAC5C,OAAO,IAAIN,eAAe;QACxBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACG;YACnBJ,WAAWG,KAAK;QAClB;IACF;AACF;AAEO,eAAezB,eACpB2B,MAAkC;IAElC,MAAMC,SAASD,OAAOE,SAAS;IAC/B,MAAMC,SAAuB,EAAE;IAE/B,MAAO,KAAM;QACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;QACzC,IAAIF,MAAM;YACR;QACF;QAEAD,OAAOI,IAAI,CAACF;IACd;IAEA,OAAOG,OAAOC,MAAM,CAACN;AACvB;AAEO,eAAe7B,eACpB0B,MAAkC,EAClCU,MAAoB;IAEpB,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IACvD,IAAIC,SAAS;IAEb,WAAW,MAAMf,SAASC,OAAQ;QAChC,IAAIU,0BAAAA,OAAQK,OAAO,EAAE;YACnB,OAAOD;QACT;QAEAA,UAAUH,QAAQK,MAAM,CAACjB,OAAO;YAAEC,QAAQ;QAAK;IACjD;IAEAc,UAAUH,QAAQK,MAAM;IAExB,OAAOF;AACT;AAEO,SAAS/C;IAId,IAAIkD,iBAAoC,EAAE;IAC1C,IAAIC,mBAA2B;IAC/B,IAAIC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,yDAAyD;QACzD,IAAIwB,SAAS;QAEb,MAAME,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACF,MAAMxB,QAAQ,IAAIyB,WAAWN;gBAC7B,IAAIO,cAAc;gBAElB,IAAK,IAAItC,IAAI,GAAGA,IAAI8B,eAAetC,MAAM,EAAEQ,IAAK;oBAC9C,MAAMuC,gBAAgBT,cAAc,CAAC9B,EAAE;oBACvCY,MAAM4B,GAAG,CAACD,eAAeD;oBACzBA,eAAeC,cAAcE,UAAU;gBACzC;gBACA,qFAAqF;gBACrF,4EAA4E;gBAC5EX,eAAetC,MAAM,GAAG;gBACxBuC,mBAAmB;gBACnBvB,WAAWC,OAAO,CAACG;YACrB,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRoB,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,kDAAkD;YAClDsB,eAAeV,IAAI,CAACR;YACpBmB,oBAAoBnB,MAAM6B,UAAU;YAEpC,sCAAsC;YACtCR,MAAMzB;QACR;QACAyB;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQnC,OAAO;QACxB;IACF;AACF;AAEO,SAASd,0BAA0B,EACxC8D,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,wBAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IAEf,wEAAwE;IACxE,iDAAiD;IACjD,IAAIC,WAAW;IAEf,OAAO,IAAI3D,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B+C,WAAW;YAEX,MAAMC,YAAY,MAAMH;YACxB,IAAIC,UAAU;gBACZ,IAAIE,WAAW;oBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;oBACxChD,WAAWC,OAAO,CAACgD;gBACrB;gBACAjD,WAAWC,OAAO,CAACG;YACrB,OAAO;gBACL,0JAA0J;gBAC1J,MAAM8C,QAAQC,IAAAA,oCAAiB,EAAC/C,OAAOgD,yBAAY,CAACC,MAAM,CAACC,IAAI;gBAC/D,wDAAwD;gBACxD,uEAAuE;gBACvE,IAAIJ,UAAU,CAAC,GAAG;oBAChB,IAAIF,WAAW;wBACb,MAAMC,mBAAmBpE,QAAQqB,MAAM,CAAC8C;wBACxC,kEAAkE;wBAClE,OAAO;wBACP,8CAA8C;wBAC9C,mCAAmC;wBACnC,yEAAyE;wBACzE,MAAMO,sBAAsB,IAAI1B,WAC9BzB,MAAMpB,MAAM,GAAGiE,iBAAiBjE,MAAM;wBAExC,0DAA0D;wBAC1DuE,oBAAoBvB,GAAG,CAAC5B,MAAMoD,KAAK,CAAC,GAAGN;wBACvC,qCAAqC;wBACrCK,oBAAoBvB,GAAG,CAACiB,kBAAkBC;wBAC1C,+BAA+B;wBAC/BK,oBAAoBvB,GAAG,CACrB5B,MAAMoD,KAAK,CAACN,QACZA,QAAQD,iBAAiBjE,MAAM;wBAEjCgB,WAAWC,OAAO,CAACsD;oBACrB,OAAO;wBACLvD,WAAWC,OAAO,CAACG;oBACrB;oBACA0C,WAAW;gBACb,OAAO;oBACL,6FAA6F;oBAC7F,gFAAgF;oBAChF,8EAA8E;oBAC9E,OAAO;oBACP,gEAAgE;oBAChE,6CAA6C;oBAC7C,IAAIE,WAAW;wBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;oBACpC;oBACAhD,WAAWC,OAAO,CAACG;oBACnB0C,WAAW;gBACb;YACF;QACF;QACA,MAAMrB,OAAMzB,UAAU;YACpB,gEAAgE;YAChE,IAAI+C,UAAU;gBACZ,MAAMC,YAAY,MAAMH;gBACxB,IAAIG,WAAW;oBACbhD,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAAC8C;gBACpC;YACF;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASS,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAInC;IAEJ,MAAMC,QAAQ,CAACzB;QACb,MAAM0B,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACF5B,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRlC,UAAUU;gBACVR,SAASS,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAI/C,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,wCAAwC;YACxC,IAAIuD,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVlC,MAAMzB;QACR;QACAyB,OAAMzB,UAAU;YACd,IAAIwB,SAAS,OAAOA,QAAQnC,OAAO;YACnC,IAAIsE,SAAS;YAEb,aAAa;YACb3D,WAAWC,OAAO,CAACpB,QAAQqB,MAAM,CAACwD;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACPvD,MAAkC;IAElC,IAAIwD,OAA6B;IACjC,IAAIC,cAAc;IAElB,eAAeC,aAAa/D,UAA4C;QACtE,IAAI6D,MAAM;YACR;QACF;QAEA,MAAMvD,SAASD,OAAOE,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QAEb,qEAAqE;QACrE,6EAA6E;QAC7E,gEAAgE;QAChE,MAAMyD,IAAAA,yBAAc;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAEvD,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMJ,OAAOK,IAAI;gBACzC,IAAIF,MAAM;oBACRqD,cAAc;oBACd;gBACF;gBAEA9D,WAAWC,OAAO,CAACS;YACrB;QACF,EAAE,OAAOuD,KAAK;YACZjE,WAAWkE,KAAK,CAACD;QACnB;IACF;IAEA,OAAO,IAAI7E,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzBA,WAAWC,OAAO,CAACG;YAEnB,6DAA6D;YAC7D,IAAI,CAACyD,MAAM;gBACTA,OAAOE,aAAa/D;YACtB;QACF;QACAyB,OAAMzB,UAAU;YACd,IAAI8D,aAAa;gBACf;YACF;YACA,OAAOD,QAAQE,aAAa/D;QAC9B;IACF;AACF;AAEA,MAAMmE,YAAY;AAElB;;;;CAIC,GACD,SAASC;IACP,IAAIC,cAAc;IAElB,OAAO,IAAIjF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,IAAIqE,aAAa;gBACf,OAAOrE,WAAWC,OAAO,CAACG;YAC5B;YAEA,MAAM8C,QAAQC,IAAAA,oCAAiB,EAAC/C,OAAOgD,yBAAY,CAACC,MAAM,CAACiB,aAAa;YACxE,IAAIpB,QAAQ,CAAC,GAAG;gBACdmB,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIjE,MAAMpB,MAAM,KAAKoE,yBAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,EAAE;oBAC7D;gBACF;gBAEA,wCAAwC;gBACxC,MAAMuF,SAASnE,MAAMoD,KAAK,CAAC,GAAGN;gBAC9BlD,WAAWC,OAAO,CAACsE;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAInE,MAAMpB,MAAM,GAAGoE,yBAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM,GAAGkE,OAAO;oBACnE,uCAAuC;oBACvC,MAAMsB,QAAQpE,MAAMoD,KAAK,CACvBN,QAAQE,yBAAY,CAACC,MAAM,CAACiB,aAAa,CAACtF,MAAM;oBAElDgB,WAAWC,OAAO,CAACuE;gBACrB;YACF,OAAO;gBACLxE,WAAWC,OAAO,CAACG;YACrB;QACF;QACAqB,OAAMzB,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWC,OAAO,CAACmD,yBAAY,CAACC,MAAM,CAACiB,aAAa;QACtD;IACF;AACF;AAEA,SAASG;IAIP,OAAO,IAAIrF,gBAAgB;QACzBgD,WAAUhC,KAAK,EAAEJ,UAAU;YACzB,6EAA6E;YAC7E,qFAAqF;YACrF,wFAAwF;YACxF,2FAA2F;YAC3F,sCAAsC;YACtC,IACE0E,IAAAA,0CAAuB,EAACtE,OAAOgD,yBAAY,CAACC,MAAM,CAACiB,aAAa,KAChEI,IAAAA,0CAAuB,EAACtE,OAAOgD,yBAAY,CAACC,MAAM,CAACsB,IAAI,KACvDD,IAAAA,0CAAuB,EAACtE,OAAOgD,yBAAY,CAACC,MAAM,CAACuB,IAAI,GACvD;gBACA,4EAA4E;gBAC5E;YACF;YAEA,+EAA+E;YAC/E,wFAAwF;YACxF,sFAAsF;YACtFxE,QAAQyE,IAAAA,uCAAoB,EAACzE,OAAOgD,yBAAY,CAACC,MAAM,CAACsB,IAAI;YAC5DvE,QAAQyE,IAAAA,uCAAoB,EAACzE,OAAOgD,yBAAY,CAACC,MAAM,CAACuB,IAAI;YAE5D5E,WAAWC,OAAO,CAACG;QACrB;IACF;AACF;AAOO,SAAS9B;IAId,IAAIwG,YAAY;IAChB,IAAIC,YAAY;IAChB,OAAO,IAAI3F,gBAAgB;QACzB,MAAMgD,WAAUhC,KAAK,EAAEJ,UAAU;YAC/B,+DAA+D;YAC/D,IACE,CAAC8E,aACD3B,IAAAA,oCAAiB,EAAC/C,OAAOgD,yBAAY,CAAC4B,OAAO,CAACJ,IAAI,IAAI,CAAC,GACvD;gBACAE,YAAY;YACd;YAEA,IACE,CAACC,aACD5B,IAAAA,oCAAiB,EAAC/C,OAAOgD,yBAAY,CAAC4B,OAAO,CAACL,IAAI,IAAI,CAAC,GACvD;gBACAI,YAAY;YACd;YAEA/E,WAAWC,OAAO,CAACG;QACrB;QACAqB,OAAMzB,UAAU;YACd,MAAMiF,cAAmC,EAAE;YAC3C,IAAI,CAACH,WAAWG,YAAYrE,IAAI,CAAC;YACjC,IAAI,CAACmE,WAAWE,YAAYrE,IAAI,CAAC;YAEjC,IAAI,CAACqE,YAAYjG,MAAM,EAAE;YAEzBgB,WAAWC,OAAO,CAChBpB,QAAQqB,MAAM,CACZ,CAAC;;+CAEoC,EAAE+E,YAChCC,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnBC,IAAI,CACHH,YAAYjG,MAAM,GAAG,IAAI,UAAU,IACnC;sCACoB,EAAEqG,mCAAuB,CAAC;;;UAGtD,CAAC;QAGP;IACF;AACF;AAEA,SAASC,kBACPpG,QAA2B,EAC3BqG,YAAyD;IAEzD,IAAIlF,SAASnB;IACb,KAAK,MAAMsG,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBnF,SAASA,OAAOoF,WAAW,CAACD;IAC9B;IACA,OAAOnF;AACT;AAcO,eAAenC,mBACpBwH,YAAiC,EACjC,EACEhC,MAAM,EACNiC,iBAAiB,EACjBC,kBAAkB,EAClBC,qBAAqB,EACrBC,yBAAyB,EACzBC,kBAAkB,EACI;IAExB,6EAA6E;IAC7E,MAAMC,iBAAiBtC,SAASA,OAAOuC,KAAK,CAAC9B,WAAW,EAAE,CAAC,EAAE,GAAG;IAEhE,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIyB,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOZ,kBAAkBI,cAAc;QACrC,qDAAqD;QACrDtH;QAEA,4BAA4B;QAC5BwE,mCAAmCkD;QAEnC,wBAAwB;QACxBE,kBAAkB,QAAQA,eAAehH,MAAM,GAAG,IAC9CyE,2BAA2BuC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoB/B,4BAA4B+B,qBAAqB;QAErE,yDAAyD;QACzDI,qBAAqBzH,oCAAoC;QAEzD,kDAAkD;QAClD8F;QAEA,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ExB,mCAAmCiD;KACpC;AACH;AAOO,eAAe5H,yBACpBkI,eAA2C,EAC3C,EACEN,qBAAqB,EACrBC,yBAAyB,EACO;IAElC,OACEK,eACE,qDAAqD;KACpDV,WAAW,CAACrH,iCACZqH,WAAW,CAAChB,0CACb,gCAAgC;KAC/BgB,WAAW,CAAC7C,mCAAmCiD,uBAChD,4BAA4B;KAC3BJ,WAAW,CACV7C,mCAAmCkD;AAG3C;AAQO,eAAe3H,wBACpBgI,eAA2C,EAC3C,EACER,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACM;IAEjC,OACEK,eACE,qDAAqD;KACpDV,WAAW,CAACrH,gCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,uBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,2BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,mBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAQO,eAAepG,0BACpB0H,YAAwC,EACxC,EACEC,iBAAiB,EACjBE,qBAAqB,EACrBC,yBAAyB,EACH;IAExB,OACEJ,YACE,qDAAqD;KACpDD,WAAW,CAACrH,gCACb,gCAAgC;KAC/BqH,WAAW,CAAC7C,mCAAmCiD,uBAChD,oCAAoC;KACnCJ,WAAW,CACV7C,mCAAmCkD,2BAErC,+EAA+E;KAC9EL,WAAW,CAAC7B,4BAA4B+B,mBACzC,kDAAkD;KACjDF,WAAW,CAACrB;AAEnB;AAEO,SAAS/F;IACd,OAAOI,iBAAiB0F;AAC1B"}