{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/index.ts"], "sourcesContent": ["import type { CompilerNameValues } from '../../../../shared/lib/constants'\n\nimport path from 'path'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { getImageSize } from '../../../../server/image-optimizer'\nimport { getBlurImage } from './blur'\n\ninterface Options {\n  compilerType: CompilerNameValues\n  isDev: boolean\n  assetPrefix: string\n  basePath: string\n}\n\nfunction nextImageLoader(this: any, content: Buffer) {\n  const imageLoaderSpan = this.currentTraceSpan.traceChild('next-image-loader')\n  return imageLoaderSpan.traceAsyncFn(async () => {\n    const options: Options = this.getOptions()\n    const { compilerType, isDev, assetPrefix, basePath } = options\n    const context = this.rootContext\n\n    const opts = { context, content }\n    const interpolatedName = loaderUtils.interpolateName(\n      this,\n      '/static/media/[name].[hash:8].[ext]',\n      opts\n    )\n    const outputPath = assetPrefix + '/_next' + interpolatedName\n    let extension = loaderUtils.interpolateName(this, '[ext]', opts)\n    if (extension === 'jpg') {\n      extension = 'jpeg'\n    }\n\n    const imageSizeSpan = imageLoaderSpan.traceChild('image-size-calculation')\n    const imageSize = await imageSizeSpan.traceAsyncFn(() =>\n      getImageSize(content).catch((err) => err)\n    )\n\n    if (imageSize instanceof Error) {\n      const err = imageSize\n      err.name = 'InvalidImageFormatError'\n      throw err\n    }\n\n    const {\n      dataURL: blurDataURL,\n      width: blurWidth,\n      height: blurHeight,\n    } = await getBlurImage(content, extension, imageSize, {\n      basePath,\n      outputPath,\n      isDev,\n      tracing: imageLoaderSpan.traceChild.bind(imageLoaderSpan),\n    })\n\n    const stringifiedData = imageLoaderSpan\n      .traceChild('image-data-stringify')\n      .traceFn(() =>\n        JSON.stringify({\n          src: outputPath,\n          height: imageSize.height,\n          width: imageSize.width,\n          blurDataURL,\n          blurWidth,\n          blurHeight,\n        })\n      )\n\n    if (compilerType === 'client') {\n      this.emitFile(interpolatedName, content, null)\n    } else {\n      this.emitFile(\n        path.join(\n          '..',\n          isDev || compilerType === 'edge-server' ? '' : '..',\n          interpolatedName\n        ),\n        content,\n        null\n      )\n    }\n\n    return `export default ${stringifiedData};`\n  })\n}\nexport const raw = true\nexport default nextImageLoader\n"], "names": ["path", "loaderUtils", "getImageSize", "getBlurImage", "nextImage<PERSON><PERSON><PERSON>", "content", "imageLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "getOptions", "compilerType", "isDev", "assetPrefix", "basePath", "context", "rootContext", "opts", "interpolatedName", "interpolateName", "outputPath", "extension", "imageSizeSpan", "imageSize", "catch", "err", "Error", "name", "dataURL", "blurDataURL", "width", "blur<PERSON>idth", "height", "blurHeight", "tracing", "bind", "stringifiedData", "traceFn", "JSON", "stringify", "src", "emitFile", "join", "raw"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,OAAOC,iBAAiB,mCAAkC;AAC1D,SAASC,YAAY,QAAQ,qCAAoC;AACjE,SAASC,YAAY,QAAQ,SAAQ;AASrC,SAASC,gBAA2BC,OAAe;IACjD,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACzD,OAAOF,gBAAgBG,YAAY,CAAC;QAClC,MAAMC,UAAmB,IAAI,CAACC,UAAU;QACxC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE,GAAGL;QACvD,MAAMM,UAAU,IAAI,CAACC,WAAW;QAEhC,MAAMC,OAAO;YAAEF;YAASX;QAAQ;QAChC,MAAMc,mBAAmBlB,YAAYmB,eAAe,CAClD,IAAI,EACJ,uCACAF;QAEF,MAAMG,aAAaP,cAAc,WAAWK;QAC5C,IAAIG,YAAYrB,YAAYmB,eAAe,CAAC,IAAI,EAAE,SAASF;QAC3D,IAAII,cAAc,OAAO;YACvBA,YAAY;QACd;QAEA,MAAMC,gBAAgBjB,gBAAgBE,UAAU,CAAC;QACjD,MAAMgB,YAAY,MAAMD,cAAcd,YAAY,CAAC,IACjDP,aAAaG,SAASoB,KAAK,CAAC,CAACC,MAAQA;QAGvC,IAAIF,qBAAqBG,OAAO;YAC9B,MAAMD,MAAMF;YACZE,IAAIE,IAAI,GAAG;YACX,MAAMF;QACR;QAEA,MAAM,EACJG,SAASC,WAAW,EACpBC,OAAOC,SAAS,EAChBC,QAAQC,UAAU,EACnB,GAAG,MAAM/B,aAAaE,SAASiB,WAAWE,WAAW;YACpDT;YACAM;YACAR;YACAsB,SAAS7B,gBAAgBE,UAAU,CAAC4B,IAAI,CAAC9B;QAC3C;QAEA,MAAM+B,kBAAkB/B,gBACrBE,UAAU,CAAC,wBACX8B,OAAO,CAAC,IACPC,KAAKC,SAAS,CAAC;gBACbC,KAAKpB;gBACLY,QAAQT,UAAUS,MAAM;gBACxBF,OAAOP,UAAUO,KAAK;gBACtBD;gBACAE;gBACAE;YACF;QAGJ,IAAItB,iBAAiB,UAAU;YAC7B,IAAI,CAAC8B,QAAQ,CAACvB,kBAAkBd,SAAS;QAC3C,OAAO;YACL,IAAI,CAACqC,QAAQ,CACX1C,KAAK2C,IAAI,CACP,MACA9B,SAASD,iBAAiB,gBAAgB,KAAK,MAC/CO,mBAEFd,SACA;QAEJ;QAEA,OAAO,CAAC,eAAe,EAAEgC,gBAAgB,CAAC,CAAC;IAC7C;AACF;AACA,OAAO,MAAMO,MAAM,KAAI;AACvB,eAAexC,gBAAe"}