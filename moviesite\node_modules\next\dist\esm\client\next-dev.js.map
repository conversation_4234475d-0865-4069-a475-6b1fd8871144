{"version": 3, "sources": ["../../src/client/next-dev.ts"], "sourcesContent": ["// TODO: Remove use of `any` type.\nimport './webpack'\nimport { initialize, version, router, emitter } from './'\nimport initHMR from './dev/hot-middleware-client'\nimport { pageBootstrap } from './page-bootstrap'\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n\nconst devClient = initHMR()\ninitialize({ devClient })\n  .then(({ assetPrefix }) => {\n    return pageBootstrap(assetPrefix)\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["initialize", "version", "router", "emitter", "initHMR", "pageBootstrap", "window", "next", "devClient", "then", "assetPrefix", "catch", "err", "console", "error"], "mappings": "AAAA,kCAAkC;AAClC,OAAO,YAAW;AAClB,SAASA,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,QAAQ,KAAI;AACzD,OAAOC,aAAa,8BAA6B;AACjD,SAASC,aAAa,QAAQ,mBAAkB;AAEhDC,OAAOC,IAAI,GAAG;IACZN;IACA,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AAEA,MAAMK,YAAYJ;AAClBJ,WAAW;IAAEQ;AAAU,GACpBC,IAAI,CAAC;QAAC,EAAEC,WAAW,EAAE;IACpB,OAAOL,cAAcK;AACvB,GACCC,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}