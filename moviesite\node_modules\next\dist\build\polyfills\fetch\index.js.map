{"version": 3, "sources": ["../../../../src/build/polyfills/fetch/index.ts"], "sourcesContent": ["/* globals self */\nconst fetchModule = self.fetch.bind(self)\nmodule.exports = fetchModule\nmodule.exports.default = module.exports\n"], "names": ["fetchModule", "self", "fetch", "bind", "module", "exports", "default"], "mappings": "AAAA,gBAAgB;AAChB,MAAMA,cAAcC,KAAKC,KAAK,CAACC,IAAI,CAACF;AACpCG,OAAOC,OAAO,GAAGL;AACjBI,OAAOC,OAAO,CAACC,OAAO,GAAGF,OAAOC,OAAO"}