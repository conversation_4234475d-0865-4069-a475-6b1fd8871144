{"version": 3, "sources": ["../../src/server/next-server.ts"], "sourcesContent": ["import './node-environment'\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { CacheFs } from '../shared/lib/utils'\nimport {\n  DecodeError,\n  PageNotFoundError,\n  MiddlewareNotFoundError,\n} from '../shared/lib/utils'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport type RenderResult from './render-result'\nimport type { FetchEventResult } from './web/types'\nimport type { PrerenderManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { PagesAPIRouteModule } from './route-modules/pages-api/module'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { ParsedUrl } from '../shared/lib/router/utils/parse-url'\nimport type { CacheControl } from './lib/cache-control'\nimport type { WaitUntil } from './after/builtin-request-context'\n\nimport fs from 'fs'\nimport { join, resolve } from 'path'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport {\n  PAGES_MANIFEST,\n  BUILD_ID_FILE,\n  MIDDLEWARE_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n  CLIENT_PUBLIC_FILES_PATH,\n  APP_PATHS_MANIFEST,\n  SERVER_DIRECTORY,\n  NEXT_FONT_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport { findDir } from '../lib/find-pages-dir'\nimport { NodeNextRequest, NodeNextResponse } from './base-http/node'\nimport { sendRenderResult } from './send-payload'\nimport { parseUrl } from '../shared/lib/router/utils/parse-url'\nimport * as Log from '../build/output/log'\n\nimport type {\n  Options,\n  FindComponentsResult,\n  MiddlewareRoutingItem,\n  RequestContext,\n  NormalizedRouteManifest,\n  LoadedRenderOpts,\n  RouteHandler,\n  NextEnabledDirectories,\n  BaseRequestHandler,\n} from './base-server'\nimport BaseServer, { NoFallbackError } from './base-server'\nimport { getMaybePagePath, getPagePath } from './require'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { loadComponents } from './load-components'\nimport type { LoadComponentsReturnType } from './load-components'\nimport isError, { getProperError } from '../lib/is-error'\nimport { splitCookiesString, toNodeOutgoingHttpHeaders } from './web/utils'\nimport { getMiddlewareRouteMatcher } from '../shared/lib/router/utils/middleware-route-matcher'\nimport { loadEnvConfig } from '@next/env'\nimport { urlQueryToSearchParams } from '../shared/lib/router/utils/querystring'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport { getCloneableBody } from './body-streams'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport ResponseCache, {\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\n\nimport { isPagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { MatchOptions } from './route-matcher-managers/route-matcher-manager'\nimport { INSTRUMENTATION_HOOK_FILENAME } from '../lib/constants'\nimport { BubbledError, getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { nodeFs } from './lib/node-fs-methods'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport { signalFromNodeResponse } from './web/spec-extension/adapters/next-request'\nimport { RouteModuleLoader } from './lib/module-loader/route-module-loader'\nimport { loadManifest } from './load-manifest'\nimport { lazyRenderAppPage } from './route-modules/app-page/module.render'\nimport { lazyRenderPagesPage } from './route-modules/pages/module.render'\nimport { interopDefault } from '../lib/interop-default'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { RouteKind } from './route-kind'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { AwaiterOnce } from './after/awaiter'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\nimport { initializeCacheHandlers, setCacheHandler } from './use-cache/handlers'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport { populateStaticEnv } from '../lib/static-env'\n\nexport * from './base-server'\n\ndeclare const __non_webpack_require__: NodeRequire\n\n// For module that can be both CJS or ESM\nconst dynamicImportEsmDefault = process.env.NEXT_MINIMAL\n  ? (id: string) =>\n      import(/* webpackIgnore: true */ id).then((mod) => mod.default || mod)\n  : (id: string) => import(id).then((mod) => mod.default || mod)\n\n// For module that will be compiled to CJS, e.g. instrument\nconst dynamicRequire = process.env.NEXT_MINIMAL\n  ? __non_webpack_require__\n  : require\n\nexport type NodeRequestHandler = BaseRequestHandler<\n  IncomingMessage | NodeNextRequest,\n  ServerResponse | NodeNextResponse\n>\n\ntype NodeRouteHandler = RouteHandler<NodeNextRequest, NodeNextResponse>\n\nconst MiddlewareMatcherCache = new WeakMap<\n  MiddlewareManifest['middleware'][string],\n  MiddlewareRouteMatch\n>()\n\nfunction getMiddlewareMatcher(\n  info: MiddlewareManifest['middleware'][string]\n): MiddlewareRouteMatch {\n  const stored = MiddlewareMatcherCache.get(info)\n  if (stored) {\n    return stored\n  }\n\n  if (!Array.isArray(info.matchers)) {\n    throw new Error(\n      `Invariant: invalid matchers for middleware ${JSON.stringify(info)}`\n    )\n  }\n\n  const matcher = getMiddlewareRouteMatcher(info.matchers)\n  MiddlewareMatcherCache.set(info, matcher)\n  return matcher\n}\n\nexport default class NextNodeServer extends BaseServer<\n  Options,\n  NodeNextRequest,\n  NodeNextResponse\n> {\n  protected middlewareManifestPath: string\n  private _serverDistDir: string | undefined\n  private imageResponseCache?: ResponseCache\n  private registeredInstrumentation: boolean = false\n  protected renderWorkersPromises?: Promise<void>\n  protected dynamicRoutes?: {\n    match: import('../shared/lib/router/utils/route-matcher').RouteMatchFn\n    page: string\n    re: RegExp\n  }[]\n  private routerServerHandler?: (\n    req: IncomingMessage,\n    res: ServerResponse\n  ) => void\n\n  protected cleanupListeners = new AsyncCallbackSet()\n  protected internalWaitUntil: WaitUntil | undefined\n  private isDev: boolean\n  private sriEnabled: boolean\n\n  constructor(options: Options) {\n    // Initialize super class\n    super(options)\n\n    const isDev = options.dev ?? false\n    this.isDev = isDev\n    this.sriEnabled = Boolean(options.conf.experimental?.sri?.algorithm)\n\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (this.renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    if (this.renderOpts.nextScriptWorkers) {\n      process.env.__NEXT_SCRIPT_WORKERS = JSON.stringify(true)\n    }\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n\n    if (!this.minimalMode) {\n      this.imageResponseCache = new ResponseCache(this.minimalMode)\n    }\n\n    const { appDocumentPreloading } = this.nextConfig.experimental\n    const isDefaultEnabled = typeof appDocumentPreloading === 'undefined'\n\n    if (\n      !options.dev &&\n      (appDocumentPreloading === true ||\n        !(this.minimalMode && isDefaultEnabled))\n    ) {\n      // pre-warm _document and _app as these will be\n      // needed for most requests\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_document',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_app',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    if (\n      !options.dev &&\n      !this.minimalMode &&\n      this.nextConfig.experimental.preloadEntriesOnStart\n    ) {\n      this.unstable_preloadEntries()\n    }\n\n    if (!options.dev) {\n      const { dynamicRoutes = [] } = this.getRoutesManifest() ?? {}\n      this.dynamicRoutes = dynamicRoutes.map((r) => {\n        // TODO: can we just re-use the regex from the manifest?\n        const regex = getRouteRegex(r.page)\n        const match = getRouteMatcher(regex)\n\n        return {\n          match,\n          page: r.page,\n          re: regex.re,\n        }\n      })\n    }\n\n    // ensure options are set when loadConfig isn't called\n    setHttpClientAndAgentOptions(this.nextConfig)\n\n    // Intercept fetch and other testmode apis.\n    if (this.serverOptions.experimentalTestProxy) {\n      process.env.NEXT_PRIVATE_TEST_PROXY = 'true'\n      const {\n        interceptTestApis,\n      } = require('next/dist/experimental/testmode/server')\n      interceptTestApis()\n    }\n\n    this.middlewareManifestPath = join(this.serverDistDir, MIDDLEWARE_MANIFEST)\n\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause a unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    if (!options.dev) {\n      this.prepare().catch((err) => {\n        console.error('Failed to prepare server', err)\n      })\n    }\n\n    // when using compile mode static env isn't inlined so we\n    // need to populate in normal runtime env\n    if (this.renderOpts.isExperimentalCompile) {\n      populateStaticEnv(this.nextConfig)\n    }\n  }\n\n  public async unstable_preloadEntries(): Promise<void> {\n    const appPathsManifest = this.getAppPathsManifest()\n    const pagesManifest = this.getPagesManifest()\n\n    await this.loadCustomCacheHandlers()\n\n    for (const page of Object.keys(pagesManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    for (const page of Object.keys(appPathsManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: true,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      })\n        .then(async ({ ComponentMod }) => {\n          // we need to ensure fetch is patched before we require the page,\n          // otherwise if the fetch is patched by user code, we will be patching it\n          // too late and there won't be any caching behaviors\n          ComponentMod.patchFetch()\n\n          const webpackRequire = ComponentMod.__next_app__.require\n          if (webpackRequire?.m) {\n            for (const id of Object.keys(webpackRequire.m)) {\n              await webpackRequire(id)\n            }\n          }\n        })\n        .catch(() => {})\n    }\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets, it's only used for HMR in\n    // development.\n  }\n\n  protected async loadInstrumentationModule() {\n    if (!this.serverOptions.dev) {\n      try {\n        this.instrumentation = await dynamicRequire(\n          resolve(\n            this.serverOptions.dir || '.',\n            this.serverOptions.conf.distDir!,\n            'server',\n            INSTRUMENTATION_HOOK_FILENAME\n          )\n        )\n      } catch (err: any) {\n        if (err.code !== 'MODULE_NOT_FOUND') {\n          throw new Error(\n            'An error occurred while loading the instrumentation hook',\n            { cause: err }\n          )\n        }\n      }\n    }\n    return this.instrumentation\n  }\n\n  protected async prepareImpl() {\n    await super.prepareImpl()\n    await this.runInstrumentationHookIfAvailable()\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    if (this.registeredInstrumentation) return\n    this.registeredInstrumentation = true\n    await this.instrumentation?.register?.()\n  }\n\n  protected loadEnvConfig({\n    dev,\n    forceReload,\n    silent,\n  }: {\n    dev: boolean\n    forceReload?: boolean\n    silent?: boolean\n  }) {\n    loadEnvConfig(\n      this.dir,\n      dev,\n      silent ? { info: () => {}, error: () => {} } : Log,\n      forceReload\n    )\n  }\n\n  private async loadCustomCacheHandlers() {\n    const { cacheHandlers } = this.nextConfig.experimental\n    if (!cacheHandlers) return\n\n    // If we've already initialized the cache handlers interface, don't do it\n    // again.\n    if (!initializeCacheHandlers()) return\n\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, handler)\n          )\n        )\n      )\n    }\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n    requestProtocol,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n    requestProtocol: 'http' | 'https'\n  }) {\n    const dev = !!this.renderOpts.dev\n    let CacheHandler: any\n    const { cacheHandler } = this.nextConfig\n\n    if (cacheHandler) {\n      CacheHandler = interopDefault(\n        await dynamicImportEsmDefault(\n          formatDynamicImportPath(this.distDir, cacheHandler)\n        )\n      )\n    }\n\n    await this.loadCustomCacheHandlers()\n\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      fs: this.getCacheFilesystem(),\n      dev,\n      requestHeaders,\n      requestProtocol,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      serverDistDir: this.serverDistDir,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk:\n        !this.minimalMode && this.nextConfig.experimental.isrFlushToDisk,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n      CurCacheHandler: CacheHandler,\n    })\n  }\n\n  protected getResponseCache() {\n    return new ResponseCache(this.minimalMode)\n  }\n\n  protected getPublicDir(): string {\n    return join(this.dir, CLIENT_PUBLIC_FILES_PATH)\n  }\n\n  protected getHasStaticDir(): boolean {\n    return fs.existsSync(join(this.dir, 'static'))\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return loadManifest(\n      join(this.serverDistDir, PAGES_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return loadManifest(\n      join(this.serverDistDir, APP_PATHS_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    if (!this.enabledDirectories.app) return []\n\n    const routesManifest = this.getRoutesManifest()\n    return (\n      routesManifest?.rewrites.beforeFiles\n        .filter(isInterceptionRouteRewrite)\n        .map((rewrite) => new RegExp(rewrite.regex)) ?? []\n    )\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    return !!getMaybePagePath(\n      pathname,\n      this.distDir,\n      this.nextConfig.i18n?.locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected getBuildId(): string {\n    const buildIdFile = join(this.distDir, BUILD_ID_FILE)\n    try {\n      return fs.readFileSync(buildIdFile, 'utf8').trim()\n    } catch (err: any) {\n      if (err.code === 'ENOENT') {\n        throw new Error(\n          `Could not find a production build in the '${this.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n        )\n      }\n\n      throw err\n    }\n  }\n\n  protected getEnabledDirectories(dev: boolean): NextEnabledDirectories {\n    const dir = dev ? this.dir : this.serverDistDir\n\n    return {\n      app: findDir(dir, 'app') ? true : false,\n      pages: findDir(dir, 'pages') ? true : false,\n    }\n  }\n\n  protected sendRenderResult(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    return sendRenderResult({\n      req: req.originalRequest,\n      res: res.originalResponse,\n      result: options.result,\n      type: options.type,\n      generateEtags: options.generateEtags,\n      poweredByHeader: options.poweredByHeader,\n      cacheControl: options.cacheControl,\n    })\n  }\n\n  protected async runApi(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages()\n\n    for (const edgeFunctionsPage of edgeFunctionsPages) {\n      if (edgeFunctionsPage === match.definition.pathname) {\n        const handledAsEdgeFunction = await this.runEdgeFunction({\n          req,\n          res,\n          query,\n          params: match.params,\n          page: match.definition.pathname,\n          appPaths: null,\n        })\n\n        if (handledAsEdgeFunction) {\n          return true\n        }\n      }\n    }\n\n    // The module supports minimal mode, load the minimal module.\n    const module = await RouteModuleLoader.load<PagesAPIRouteModule>(\n      match.definition.filename\n    )\n\n    query = { ...query, ...match.params }\n\n    await module.render(req.originalRequest, res.originalResponse, {\n      previewProps: this.renderOpts.previewProps,\n      revalidate: this.revalidate.bind(this),\n      trustHostHeader: this.nextConfig.experimental.trustHostHeader,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      hostname: this.fetchHostname,\n      minimalMode: this.minimalMode,\n      dev: this.renderOpts.dev === true,\n      query,\n      params: match.params,\n      page: match.definition.pathname,\n      onError: this.instrumentationOnRequestError.bind(this),\n      multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n    })\n\n    return true\n  }\n\n  protected async renderHTML(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    return getTracer().trace(NextNodeServerSpan.renderHTML, async () =>\n      this.renderHTMLImpl(req, res, pathname, query, renderOpts)\n    )\n  }\n\n  private async renderHTMLImpl(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Invariant: renderHTML should not be called in minimal mode'\n      )\n      // the `else` branch is needed for tree-shaking\n    } else {\n      // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n      // object here but only updating its `nextFontManifest` field.\n      // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n      renderOpts.nextFontManifest = this.nextFontManifest\n\n      if (this.enabledDirectories.app && renderOpts.isAppPath) {\n        return lazyRenderAppPage(\n          req,\n          res,\n          pathname,\n          query,\n          // This code path does not service revalidations for unknown param\n          // shells. As a result, we don't need to pass in the unknown params.\n          null,\n          renderOpts,\n          this.getServerComponentsHmrCache(),\n          false,\n          {\n            buildId: this.buildId,\n          }\n        )\n      }\n\n      // TODO: re-enable this once we've refactored to use implicit matches\n      // throw new Error('Invariant: render should have used routeModule')\n\n      return lazyRenderPagesPage(\n        req.originalRequest,\n        res.originalResponse,\n        pathname,\n        query,\n        renderOpts,\n        {\n          buildId: this.buildId,\n          deploymentId: this.nextConfig.deploymentId,\n          customServer: this.serverOptions.customServer || undefined,\n        },\n        {\n          isFallback: false,\n          isDraftMode: renderOpts.isDraftMode,\n          developmentNotFoundSourcePage: getRequestMeta(\n            req,\n            'developmentNotFoundSourcePage'\n          ),\n        }\n      )\n    }\n  }\n\n  protected async imageOptimizer(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    paramsResult: import('./image-optimizer').ImageParamsResult,\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  ): Promise<{\n    buffer: Buffer\n    contentType: string\n    maxAge: number\n    upstreamEtag: string\n    etag: string\n  }> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: imageOptimizer should not be called in minimal mode'\n      )\n    } else {\n      const { imageOptimizer, fetchExternalImage, fetchInternalImage } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const handleInternalReq = async (\n        newReq: IncomingMessage,\n        newRes: ServerResponse\n      ) => {\n        if (newReq.url === req.url) {\n          throw new Error(`Invariant attempted to optimize _next/image itself`)\n        }\n\n        if (!this.routerServerHandler) {\n          throw new Error(`Invariant missing routerServerHandler`)\n        }\n\n        await this.routerServerHandler(newReq, newRes)\n        return\n      }\n\n      const { isAbsolute, href } = paramsResult\n\n      const imageUpstream = isAbsolute\n        ? await fetchExternalImage(href)\n        : await fetchInternalImage(\n            href,\n            req.originalRequest,\n            res.originalResponse,\n            handleInternalReq\n          )\n\n      return imageOptimizer(imageUpstream, paramsResult, this.nextConfig, {\n        isDev: this.renderOpts.dev,\n        previousCacheEntry,\n      })\n    }\n  }\n\n  protected getPagePath(pathname: string, locales?: string[]): string {\n    return getPagePath(\n      pathname,\n      this.distDir,\n      locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages() || []\n    if (edgeFunctionsPages.length) {\n      const appPaths = this.getOriginalAppPaths(ctx.pathname)\n      const isAppPath = Array.isArray(appPaths)\n\n      let page = ctx.pathname\n      if (isAppPath) {\n        // When it's an array, we need to pass all parallel routes to the loader.\n        page = appPaths[0]\n      }\n\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        if (edgeFunctionsPage === page) {\n          await this.runEdgeFunction({\n            req: ctx.req,\n            res: ctx.res,\n            query: ctx.query,\n            params: ctx.renderOpts.params,\n            page,\n            appPaths,\n          })\n          return null\n        }\n      }\n    }\n\n    return super.renderPageComponent(ctx, bubbleNoFallback)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    return getTracer().trace(\n      NextNodeServerSpan.findPageComponents,\n      {\n        spanName: 'resolve page components',\n        attributes: {\n          'next.route': isAppPath ? normalizeAppPath(page) : page,\n        },\n      },\n      () =>\n        this.findPageComponentsImpl({\n          locale,\n          page,\n          query,\n          params,\n          isAppPath,\n          url,\n        })\n    )\n  }\n\n  private async findPageComponentsImpl({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url: _url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    const pagePaths: string[] = [page]\n    if (query.amp) {\n      // try serving a static AMP version first\n      pagePaths.unshift(\n        (isAppPath ? normalizeAppPath(page) : normalizePagePath(page)) + '.amp'\n      )\n    }\n\n    if (locale) {\n      pagePaths.unshift(\n        ...pagePaths.map((path) => `/${locale}${path === '/' ? '' : path}`)\n      )\n    }\n\n    for (const pagePath of pagePaths) {\n      try {\n        const components = await loadComponents({\n          distDir: this.distDir,\n          page: pagePath,\n          isAppPath,\n          isDev: this.isDev,\n          sriEnabled: this.sriEnabled,\n        })\n\n        if (\n          locale &&\n          typeof components.Component === 'string' &&\n          !pagePath.startsWith(`/${locale}/`) &&\n          pagePath !== `/${locale}`\n        ) {\n          // if loading an static HTML file the locale is required\n          // to be present since all HTML files are output under their locale\n          continue\n        }\n\n        return {\n          components,\n          query: {\n            ...(!this.renderOpts.isExperimentalCompile &&\n            components.getStaticProps\n              ? ({\n                  amp: query.amp,\n                } as NextParsedUrlQuery)\n              : query),\n            // For appDir params is excluded.\n            ...((isAppPath ? {} : params) || {}),\n          },\n        }\n      } catch (err) {\n        // we should only not throw if we failed to find the page\n        // in the pages-manifest\n        if (!(err instanceof PageNotFoundError)) {\n          throw err\n        }\n      }\n    }\n    return null\n  }\n\n  protected getNextFontManifest(): NextFontManifest | undefined {\n    return loadManifest(\n      join(this.distDir, 'server', NEXT_FONT_MANIFEST + '.json')\n    ) as NextFontManifest\n  }\n\n  protected handleNextImageRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname || !parsedUrl.pathname.startsWith('/_next/image')) {\n      return false\n    }\n    // Ignore if its a middleware request\n    if (getRequestMeta(req, 'middlewareInvoke')) {\n      return false\n    }\n\n    if (\n      this.minimalMode ||\n      this.nextConfig.output === 'export' ||\n      process.env.NEXT_MINIMAL\n    ) {\n      res.statusCode = 400\n      res.body('Bad Request').send()\n      return true\n      // the `else` branch is needed for tree-shaking\n    } else {\n      const { ImageOptimizerCache } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const imageOptimizerCache = new ImageOptimizerCache({\n        distDir: this.distDir,\n        nextConfig: this.nextConfig,\n      })\n\n      const { sendResponse, ImageError } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      if (!this.imageResponseCache) {\n        throw new Error('invariant image optimizer cache was not initialized')\n      }\n      const imagesConfig = this.nextConfig.images\n\n      if (imagesConfig.loader !== 'default' || imagesConfig.unoptimized) {\n        await this.render404(req, res)\n        return true\n      }\n\n      const paramsResult = ImageOptimizerCache.validateParams(\n        req.originalRequest,\n        parsedUrl.query,\n        this.nextConfig,\n        !!this.renderOpts.dev\n      )\n\n      if ('errorMessage' in paramsResult) {\n        res.statusCode = 400\n        res.body(paramsResult.errorMessage).send()\n        return true\n      }\n\n      const cacheKey = ImageOptimizerCache.getCacheKey(paramsResult)\n\n      try {\n        const { getExtension } =\n          require('./serve-static') as typeof import('./serve-static')\n        const cacheEntry = await this.imageResponseCache.get(\n          cacheKey,\n          async ({ previousCacheEntry }) => {\n            const { buffer, contentType, maxAge, upstreamEtag, etag } =\n              await this.imageOptimizer(\n                req,\n                res,\n                paramsResult,\n                previousCacheEntry\n              )\n\n            return {\n              value: {\n                kind: CachedRouteKind.IMAGE,\n                buffer,\n                etag,\n                extension: getExtension(contentType) as string,\n                upstreamEtag,\n              },\n              isFallback: false,\n              cacheControl: { revalidate: maxAge, expire: undefined },\n            }\n          },\n          {\n            routeKind: RouteKind.IMAGE,\n            incrementalCache: imageOptimizerCache,\n            isFallback: false,\n          }\n        )\n\n        if (cacheEntry?.value?.kind !== CachedRouteKind.IMAGE) {\n          throw new Error(\n            'invariant did not get entry from image response cache'\n          )\n        }\n\n        sendResponse(\n          req.originalRequest,\n          res.originalResponse,\n          paramsResult.href,\n          cacheEntry.value.extension,\n          cacheEntry.value.buffer,\n          cacheEntry.value.etag,\n          paramsResult.isStatic,\n          cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT',\n          imagesConfig,\n          cacheEntry.cacheControl?.revalidate || 0,\n          Boolean(this.renderOpts.dev)\n        )\n        return true\n      } catch (err) {\n        if (err instanceof ImageError) {\n          res.statusCode = err.statusCode\n          res.body(err.message).send()\n          return true\n        }\n        throw err\n      }\n    }\n  }\n\n  protected handleCatchallRenderRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('Invariant: pathname is undefined')\n    }\n\n    // This is a catch-all route, there should be no fallbacks so mark it as\n    // such.\n    addRequestMeta(req, 'bubbleNoFallback', true)\n\n    try {\n      // next.js core assumes page path without trailing slash\n      pathname = removeTrailingSlash(pathname)\n\n      const options: MatchOptions = {\n        i18n: this.i18nProvider?.fromRequest(req, pathname),\n      }\n      const match = await this.matchers.match(pathname, options)\n\n      // If we don't have a match, try to render it anyways.\n      if (!match) {\n        await this.render(req, res, pathname, query, parsedUrl, true)\n\n        return true\n      }\n\n      // Add the match to the request so we don't have to re-run the matcher\n      // for the same request.\n      addRequestMeta(req, 'match', match)\n\n      // TODO-APP: move this to a route handler\n      const edgeFunctionsPages = this.getEdgeFunctionsPages()\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        // If the page doesn't match the edge function page, skip it.\n        if (edgeFunctionsPage !== match.definition.page) continue\n\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n        delete query[NEXT_RSC_UNION_QUERY]\n\n        // If we handled the request, we can return early.\n        // For api routes edge runtime\n        try {\n          const handled = await this.runEdgeFunction({\n            req,\n            res,\n            query,\n            params: match.params,\n            page: match.definition.page,\n            match,\n            appPaths: null,\n          })\n          if (handled) return true\n        } catch (apiError) {\n          await this.instrumentationOnRequestError(apiError, req, {\n            routePath: match.definition.page,\n            routerKind: 'Pages Router',\n            routeType: 'route',\n            // Edge runtime does not support ISR\n            revalidateReason: undefined,\n          })\n          throw apiError\n        }\n      }\n\n      // If the route was detected as being a Pages API route, then handle\n      // it.\n      // TODO: move this behavior into a route handler.\n      if (isPagesAPIRouteMatch(match)) {\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n\n        const handled = await this.handleApiRequest(req, res, query, match)\n        if (handled) return true\n      }\n\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      try {\n        if (this.renderOpts.dev) {\n          const { formatServerError } =\n            require('../lib/format-server-error') as typeof import('../lib/format-server-error')\n          formatServerError(err)\n          this.logErrorWithOriginalStack(err)\n        } else {\n          this.logError(err)\n        }\n        res.statusCode = 500\n        await this.renderError(err, req, res, pathname, query)\n        return true\n      } catch {}\n\n      throw err\n    }\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected logErrorWithOriginalStack(\n    _err?: unknown,\n    _type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    throw new Error(\n      'Invariant: logErrorWithOriginalStack can only be called on the development server'\n    )\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected async ensurePage(_opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    match?: RouteMatch\n    url?: string\n  }): Promise<void> {\n    throw new Error(\n      'Invariant: ensurePage can only be called on the development server'\n    )\n  }\n\n  /**\n   * Resolves `API` request, in development builds on demand\n   * @param req http request\n   * @param res http response\n   * @param pathname path of request\n   */\n  protected async handleApiRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    return this.runApi(req, res, query, match)\n  }\n\n  protected getCacheFilesystem(): CacheFs {\n    return nodeFs\n  }\n\n  protected normalizeReq(\n    req: NodeNextRequest | IncomingMessage\n  ): NodeNextRequest {\n    return !(req instanceof NodeNextRequest) ? new NodeNextRequest(req) : req\n  }\n\n  protected normalizeRes(\n    res: NodeNextResponse | ServerResponse\n  ): NodeNextResponse {\n    return !(res instanceof NodeNextResponse) ? new NodeNextResponse(res) : res\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = this.makeRequestHandler()\n    if (this.serverOptions.experimentalTestProxy) {\n      const {\n        wrapRequestHandlerNode,\n      } = require('next/dist/experimental/testmode/server')\n      return wrapRequestHandlerNode(handler)\n    }\n    return handler\n  }\n\n  private makeRequestHandler(): NodeRequestHandler {\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause an unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    this.prepare().catch((err) => {\n      console.error('Failed to prepare server', err)\n    })\n\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) =>\n      handler(this.normalizeReq(req), this.normalizeRes(res), parsedUrl)\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts,\n  }: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    const handler = this.getRequestHandler()\n    await handler(\n      new NodeNextRequest(mocked.req),\n      new NodeNextResponse(mocked.res)\n    )\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      mocked.res.statusCode !== 200 &&\n      !(mocked.res.statusCode === 404 && opts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n  }\n\n  public async render(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    parsedUrl?: NextUrlWithParsedQuery,\n    internal = false\n  ): Promise<void> {\n    return super.render(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      parsedUrl,\n      internal\n    )\n  }\n\n  public async renderToHTML(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderToHTML(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    err: Error | null\n  ) {\n    const { req, res, query } = ctx\n    const is404 = res.statusCode === 404\n\n    if (is404 && this.enabledDirectories.app) {\n      if (this.renderOpts.dev) {\n        await this.ensurePage({\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          clientOnly: false,\n          url: req.url,\n        }).catch(() => {})\n      }\n\n      if (\n        this.getEdgeFunctionsPages().includes(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      ) {\n        await this.runEdgeFunction({\n          req,\n          res,\n          query: query || {},\n          params: {},\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          appPaths: null,\n        })\n        return null\n      }\n    }\n    return super.renderErrorToResponseImpl(ctx, err)\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.renderError(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      setHeaders\n    )\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderErrorToHTML(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  public async render404(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.render404(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      parsedUrl,\n      setHeaders\n    )\n  }\n\n  protected getMiddlewareManifest(): MiddlewareManifest | null {\n    if (this.minimalMode) {\n      return null\n    } else {\n      const manifest: MiddlewareManifest = require(this.middlewareManifestPath)\n      return manifest\n    }\n  }\n\n  /** Returns the middleware routing item if there is one. */\n  protected async getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    const manifest = this.getMiddlewareManifest()\n    const middleware = manifest?.middleware?.['/']\n    if (!middleware) {\n      const middlewareModule = await this.loadNodeMiddleware()\n\n      if (middlewareModule) {\n        return {\n          match: getMiddlewareRouteMatcher(\n            middlewareModule.config?.matchers || [\n              { regexp: '.*', originalSource: '/:path*' },\n            ]\n          ),\n          page: '/',\n        }\n      }\n\n      return\n    }\n\n    return {\n      match: getMiddlewareMatcher(middleware),\n      page: '/',\n    }\n  }\n\n  protected getEdgeFunctionsPages(): string[] {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return []\n    }\n\n    return Object.keys(manifest.functions)\n  }\n\n  /**\n   * Get information for the edge function located in the provided page\n   * folder. If the edge function info can't be found it will throw\n   * an error.\n   */\n  protected getEdgeFunctionInfo(params: {\n    page: string\n    /** Whether we should look for a middleware or not */\n    middleware: boolean\n  }): {\n    name: string\n    paths: string[]\n    wasm: { filePath: string; name: string }[]\n    env: { [key: string]: string }\n    assets?: { filePath: string; name: string }[]\n  } | null {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return null\n    }\n\n    let foundPage: string\n\n    try {\n      foundPage = denormalizePagePath(normalizePagePath(params.page))\n    } catch (err) {\n      return null\n    }\n\n    let pageInfo = params.middleware\n      ? manifest.middleware[foundPage]\n      : manifest.functions[foundPage]\n\n    if (!pageInfo) {\n      if (!params.middleware) {\n        throw new PageNotFoundError(foundPage)\n      }\n      return null\n    }\n\n    return {\n      name: pageInfo.name,\n      paths: pageInfo.files.map((file) => join(this.distDir, file)),\n      wasm: (pageInfo.wasm ?? []).map((binding) => ({\n        ...binding,\n        filePath: join(this.distDir, binding.filePath),\n      })),\n      assets:\n        pageInfo.assets &&\n        pageInfo.assets.map((binding) => {\n          return {\n            ...binding,\n            filePath: join(this.distDir, binding.filePath),\n          }\n        }),\n      env: pageInfo.env,\n    }\n  }\n\n  private async loadNodeMiddleware() {\n    if (!this.nextConfig.experimental.nodeMiddleware) {\n      return\n    }\n\n    try {\n      const functionsConfig = this.renderOpts.dev\n        ? {}\n        : require(join(this.distDir, 'server', FUNCTIONS_CONFIG_MANIFEST))\n\n      if (this.renderOpts.dev || functionsConfig?.functions?.['/_middleware']) {\n        // if used with top level await, this will be a promise\n        return require(join(this.distDir, 'server', 'middleware.js'))\n      }\n    } catch (err) {\n      if (\n        isError(err) &&\n        err.code !== 'ENOENT' &&\n        err.code !== 'MODULE_NOT_FOUND'\n      ) {\n        throw err\n      }\n    }\n  }\n\n  /**\n   * Checks if a middleware exists. This method is useful for the development\n   * server where we need to check the filesystem. Here we just check the\n   * middleware manifest.\n   */\n  protected async hasMiddleware(pathname: string): Promise<boolean> {\n    const info = this.getEdgeFunctionInfo({ page: pathname, middleware: true })\n    const nodeMiddleware = await this.loadNodeMiddleware()\n\n    if (!info && nodeMiddleware) {\n      return true\n    }\n    return Boolean(info && info.paths.length > 0)\n  }\n\n  /**\n   * A placeholder for a function to be defined in the development server.\n   * It will make sure that the root middleware or an edge function has been compiled\n   * so that we can run it.\n   */\n  protected async ensureMiddleware(_url?: string) {}\n  protected async ensureEdgeFunction(_params: {\n    page: string\n    appPaths: string[] | null\n    url?: string\n  }) {}\n\n  /**\n   * This method gets all middleware matchers and execute them when the request\n   * matches. It will make sure that each middleware exists and is compiled and\n   * ready to be invoked. The development server will decorate it to add warns\n   * and errors with rich traces.\n   */\n  protected async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    onWarning?: (warning: Error) => void\n  }) {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: runMiddleware should not be called in minimal mode'\n      )\n    }\n\n    // Middleware is skipped for on-demand revalidate requests\n    if (\n      checkIsOnDemandRevalidate(params.request, this.renderOpts.previewProps)\n        .isOnDemandRevalidate\n    ) {\n      return {\n        response: new Response(null, { headers: { 'x-middleware-next': '1' } }),\n      } as FetchEventResult\n    }\n\n    let url: string\n\n    if (this.nextConfig.skipMiddlewareUrlNormalize) {\n      url = getRequestMeta(params.request, 'initURL')!\n    } else {\n      // For middleware to \"fetch\" we must always provide an absolute URL\n      const query = urlQueryToSearchParams(params.parsed.query).toString()\n      const locale = getRequestMeta(params.request, 'locale')\n\n      url = `${getRequestMeta(params.request, 'initProtocol')}://${\n        this.fetchHostname || 'localhost'\n      }:${this.port}${locale ? `/${locale}` : ''}${params.parsed.pathname}${\n        query ? `?${query}` : ''\n      }`\n    }\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const page: {\n      name?: string\n      params?: { [key: string]: string | string[] }\n    } = {}\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return { finished: false }\n    }\n    if (!(await this.hasMiddleware(middleware.page))) {\n      return { finished: false }\n    }\n\n    await this.ensureMiddleware(params.request.url)\n    const middlewareInfo = this.getEdgeFunctionInfo({\n      page: middleware.page,\n      middleware: true,\n    })\n\n    const method = (params.request.method || 'GET').toUpperCase()\n    const requestData = {\n      headers: params.request.headers,\n      method,\n      nextConfig: {\n        basePath: this.nextConfig.basePath,\n        i18n: this.nextConfig.i18n,\n        trailingSlash: this.nextConfig.trailingSlash,\n        experimental: this.nextConfig.experimental,\n      },\n      url: url,\n      page,\n      body:\n        method !== 'GET' && method !== 'HEAD'\n          ? (getRequestMeta(params.request, 'clonableBody') as any)\n          : undefined,\n\n      signal: signalFromNodeResponse(params.response.originalResponse),\n      waitUntil: this.getWaitUntil(),\n    }\n    let result:\n      | UnwrapPromise<ReturnType<typeof import('./web/sandbox').run>>\n      | undefined\n\n    // if no middleware info check for Node.js middleware\n    // this is not in the middleware-manifest as that historically\n    // has only included edge-functions, we need to do a breaking\n    // version bump for that manifest to write this info there if\n    // we decide we want to\n    if (!middlewareInfo) {\n      let middlewareModule\n      middlewareModule = await this.loadNodeMiddleware()\n\n      if (!middlewareModule) {\n        throw new MiddlewareNotFoundError()\n      }\n      const adapterFn: typeof import('./web/adapter').adapter =\n        middlewareModule.default || middlewareModule\n\n      result = await adapterFn({\n        handler: middlewareModule.middleware || middlewareModule,\n        request: requestData,\n        page: 'middleware',\n      })\n    } else {\n      const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n\n      result = await run({\n        distDir: this.distDir,\n        name: middlewareInfo.name,\n        paths: middlewareInfo.paths,\n        edgeFunctionEntry: middlewareInfo,\n        request: requestData,\n        useCache: true,\n        onWarning: params.onWarning,\n      })\n    }\n\n    if (!this.renderOpts.dev) {\n      result.waitUntil.catch((error) => {\n        console.error(`Uncaught: middleware waitUntil errored`, error)\n      })\n    }\n\n    if (!result) {\n      this.render404(params.request, params.response, params.parsed)\n      return { finished: true }\n    }\n\n    // Split compound (comma-separated) set-cookie headers\n    if (result.response.headers.has('set-cookie')) {\n      const cookies = result.response.headers\n        .getSetCookie()\n        .flatMap((maybeCompoundCookie) =>\n          splitCookiesString(maybeCompoundCookie)\n        )\n\n      // Clear existing header(s)\n      result.response.headers.delete('set-cookie')\n\n      // Append each cookie individually.\n      for (const cookie of cookies) {\n        result.response.headers.append('set-cookie', cookie)\n      }\n\n      // Add cookies to request meta.\n      addRequestMeta(params.request, 'middlewareCookie', cookies)\n    }\n\n    return result\n  }\n\n  protected handleCatchallMiddlewareRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsed\n  ) => {\n    const isMiddlewareInvoke = getRequestMeta(req, 'middlewareInvoke')\n\n    if (!isMiddlewareInvoke) {\n      return false\n    }\n\n    const handleFinished = () => {\n      addRequestMeta(req, 'middlewareInvoke', true)\n      res.body('').send()\n      return true\n    }\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return handleFinished()\n    }\n\n    const initUrl = getRequestMeta(req, 'initURL')!\n    const parsedUrl = parseUrl(initUrl)\n    const pathnameInfo = getNextPathnameInfo(parsedUrl.pathname, {\n      nextConfig: this.nextConfig,\n      i18nProvider: this.i18nProvider,\n    })\n\n    parsedUrl.pathname = pathnameInfo.pathname\n    const normalizedPathname = removeTrailingSlash(parsed.pathname || '')\n    let maybeDecodedPathname = normalizedPathname\n\n    try {\n      maybeDecodedPathname = decodeURIComponent(normalizedPathname)\n    } catch {\n      /* non-fatal we can't decode so can't match it */\n    }\n\n    if (\n      !(\n        middleware.match(normalizedPathname, req, parsedUrl.query) ||\n        middleware.match(maybeDecodedPathname, req, parsedUrl.query)\n      )\n    ) {\n      return handleFinished()\n    }\n\n    let result: Awaited<\n      ReturnType<typeof NextNodeServer.prototype.runMiddleware>\n    >\n    let bubblingResult = false\n\n    try {\n      await this.ensureMiddleware(req.url)\n\n      result = await this.runMiddleware({\n        request: req,\n        response: res,\n        parsedUrl: parsedUrl,\n        parsed: parsed,\n      })\n\n      if ('response' in result) {\n        if (isMiddlewareInvoke) {\n          bubblingResult = true\n          throw new BubbledError(true, result)\n        }\n\n        for (const [key, value] of Object.entries(\n          toNodeOutgoingHttpHeaders(result.response.headers)\n        )) {\n          if (key !== 'content-encoding' && value !== undefined) {\n            res.setHeader(key, value as string | string[])\n          }\n        }\n        res.statusCode = result.response.status\n\n        const { originalResponse } = res\n        if (result.response.body) {\n          await pipeToNodeResponse(result.response.body, originalResponse)\n        } else {\n          originalResponse.end()\n        }\n        return true\n      }\n    } catch (err: unknown) {\n      if (bubblingResult) {\n        throw err\n      }\n\n      if (isError(err) && err.code === 'ENOENT') {\n        await this.render404(req, res, parsed)\n        return true\n      }\n\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        await this.renderError(err, req, res, parsed.pathname || '')\n        return true\n      }\n\n      const error = getProperError(err)\n      console.error(error)\n      res.statusCode = 500\n      await this.renderError(error, req, res, parsed.pathname || '')\n      return true\n    }\n\n    return result.finished\n  }\n\n  private _cachedPreviewManifest: PrerenderManifest | undefined\n  protected getPrerenderManifest(): PrerenderManifest {\n    if (this._cachedPreviewManifest) {\n      return this._cachedPreviewManifest\n    }\n    if (\n      this.renderOpts?.dev ||\n      this.serverOptions?.dev ||\n      process.env.NODE_ENV === 'development' ||\n      process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD\n    ) {\n      this._cachedPreviewManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: {\n          previewModeId: require('crypto').randomBytes(16).toString('hex'),\n          previewModeSigningKey: require('crypto')\n            .randomBytes(32)\n            .toString('hex'),\n          previewModeEncryptionKey: require('crypto')\n            .randomBytes(32)\n            .toString('hex'),\n        },\n      }\n      return this._cachedPreviewManifest\n    }\n\n    this._cachedPreviewManifest = loadManifest(\n      join(this.distDir, PRERENDER_MANIFEST)\n    ) as PrerenderManifest\n\n    return this._cachedPreviewManifest\n  }\n\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    return getTracer().trace(NextNodeServerSpan.getRoutesManifest, () => {\n      const manifest = loadManifest(join(this.distDir, ROUTES_MANIFEST)) as any\n\n      let rewrites = manifest.rewrites ?? {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      }\n\n      if (Array.isArray(rewrites)) {\n        rewrites = {\n          beforeFiles: [],\n          afterFiles: rewrites,\n          fallback: [],\n        }\n      }\n\n      return { ...manifest, rewrites }\n    })\n  }\n\n  protected attachRequestMeta(\n    req: NodeNextRequest,\n    parsedUrl: NextUrlWithParsedQuery,\n    isUpgradeReq?: boolean\n  ) {\n    // Injected in base-server.ts\n    const protocol = req.headers['x-forwarded-proto']?.includes('https')\n      ? 'https'\n      : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl =\n      this.fetchHostname && this.port\n        ? `${protocol}://${this.fetchHostname}:${this.port}${req.url}`\n        : this.nextConfig.experimental.trustHostHeader\n          ? `https://${req.headers.host || 'localhost'}${req.url}`\n          : req.url\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req.originalRequest))\n    }\n  }\n\n  protected async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    match?: RouteMatch\n    onError?: (err: unknown) => void\n    onWarning?: (warning: Error) => void\n  }): Promise<FetchEventResult | null> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Middleware is not supported in minimal mode. Please remove the `NEXT_MINIMAL` environment variable.'\n      )\n    }\n    let edgeInfo: ReturnType<typeof this.getEdgeFunctionInfo> | undefined\n\n    const { query, page, match } = params\n\n    if (!match)\n      await this.ensureEdgeFunction({\n        page,\n        appPaths: params.appPaths,\n        url: params.req.url,\n      })\n    edgeInfo = this.getEdgeFunctionInfo({\n      page,\n      middleware: false,\n    })\n\n    if (!edgeInfo) {\n      return null\n    }\n\n    // For edge to \"fetch\" we must always provide an absolute URL\n    const isNextDataRequest = getRequestMeta(params.req, 'isNextDataReq')\n    const initialUrl = new URL(\n      getRequestMeta(params.req, 'initURL') || '/',\n      'http://n'\n    )\n    const queryString = urlQueryToSearchParams({\n      ...Object.fromEntries(initialUrl.searchParams),\n      ...query,\n      ...params.params,\n    }).toString()\n\n    if (isNextDataRequest) {\n      params.req.headers['x-nextjs-data'] = '1'\n    }\n    initialUrl.search = queryString\n    const url = initialUrl.toString()\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n    const result = await run({\n      distDir: this.distDir,\n      name: edgeInfo.name,\n      paths: edgeInfo.paths,\n      edgeFunctionEntry: edgeInfo,\n      request: {\n        headers: params.req.headers,\n        method: params.req.method,\n        nextConfig: {\n          basePath: this.nextConfig.basePath,\n          i18n: this.nextConfig.i18n,\n          trailingSlash: this.nextConfig.trailingSlash,\n        },\n        url,\n        page: {\n          name: params.page,\n          ...(params.params && { params: params.params }),\n        },\n        body: getRequestMeta(params.req, 'clonableBody'),\n        signal: signalFromNodeResponse(params.res.originalResponse),\n        waitUntil: this.getWaitUntil(),\n      },\n      useCache: true,\n      onError: params.onError,\n      onWarning: params.onWarning,\n      incrementalCache:\n        (globalThis as any).__incrementalCache ||\n        getRequestMeta(params.req, 'incrementalCache'),\n      serverComponentsHmrCache: getRequestMeta(\n        params.req,\n        'serverComponentsHmrCache'\n      ),\n    })\n\n    if (result.fetchMetrics) {\n      params.req.fetchMetrics = result.fetchMetrics\n    }\n\n    if (!params.res.statusCode || params.res.statusCode < 400) {\n      params.res.statusCode = result.response.status\n      params.res.statusMessage = result.response.statusText\n    }\n\n    // TODO: (wyattjoh) investigate improving this\n\n    result.response.headers.forEach((value, key) => {\n      // The append handling is special cased for `set-cookie`.\n      if (key.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          params.res.appendHeader(key, cookie)\n        }\n      } else {\n        params.res.appendHeader(key, value)\n      }\n    })\n\n    const { originalResponse } = params.res\n    if (result.response.body) {\n      await pipeToNodeResponse(result.response.body, originalResponse)\n    } else {\n      originalResponse.end()\n    }\n\n    return result\n  }\n\n  protected get serverDistDir(): string {\n    if (this._serverDistDir) {\n      return this._serverDistDir\n    }\n    const serverDistDir = join(this.distDir, SERVER_DIRECTORY)\n    this._serverDistDir = serverDistDir\n    return serverDistDir\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // Not implemented for production use cases, this is implemented on the\n    // development server.\n    return null\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    // For Node.js runtime production logs, in dev it will be overridden by next-dev-server\n    if (!this.renderOpts.dev) {\n      this.logError(args[0] as Error)\n    }\n  }\n\n  protected onServerClose(listener: () => Promise<void>) {\n    this.cleanupListeners.add(listener)\n  }\n\n  async close(): Promise<void> {\n    await this.cleanupListeners.runAll()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil {\n    this.internalWaitUntil ??= this.createInternalWaitUntil()\n    return this.internalWaitUntil\n  }\n\n  private createInternalWaitUntil() {\n    if (this.minimalMode) {\n      throw new InvariantError(\n        'createInternalWaitUntil should never be called in minimal mode'\n      )\n    }\n\n    const awaiter = new AwaiterOnce({ onError: console.error })\n\n    // TODO(after): warn if the process exits before these are awaited\n    this.onServerClose(() => awaiter.awaiting())\n\n    return awaiter.waitUntil\n  }\n}\n"], "names": ["DecodeError", "PageNotFoundError", "MiddlewareNotFoundError", "fs", "join", "resolve", "getRouteMatcher", "addRequestMeta", "getRequestMeta", "PAGES_MANIFEST", "BUILD_ID_FILE", "MIDDLEWARE_MANIFEST", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "CLIENT_PUBLIC_FILES_PATH", "APP_PATHS_MANIFEST", "SERVER_DIRECTORY", "NEXT_FONT_MANIFEST", "PHASE_PRODUCTION_BUILD", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "FUNCTIONS_CONFIG_MANIFEST", "findDir", "NodeNextRequest", "NodeNextResponse", "sendRenderResult", "parseUrl", "Log", "BaseServer", "NoFallbackError", "getMaybePagePath", "getPagePath", "denormalizePagePath", "normalizePagePath", "loadComponents", "isError", "getProperError", "splitCookiesString", "toNodeOutgoingHttpHeaders", "getMiddlewareRouteMatcher", "loadEnvConfig", "urlQueryToSearchParams", "removeTrailingSlash", "getNextPathnameInfo", "getCloneableBody", "checkIsOnDemandRevalidate", "ResponseCache", "CachedRouteKind", "IncrementalCache", "normalizeAppPath", "setHttpClientAndAgentOptions", "isPagesAPIRouteMatch", "INSTRUMENTATION_HOOK_FILENAME", "BubbledError", "getTracer", "NextNodeServerSpan", "nodeFs", "getRouteRegex", "pipeToNodeResponse", "createRequestResponseMocks", "NEXT_RSC_UNION_QUERY", "signalFromNodeResponse", "RouteModuleLoader", "loadManifest", "lazyRenderAppPage", "lazyRenderPagesPage", "interopDefault", "formatDynamicImportPath", "isInterceptionRouteRewrite", "RouteKind", "InvariantError", "Awaiter<PERSON>nce", "AsyncCallbackSet", "initializeCacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "populateStaticEnv", "dynamicImportEsmDefault", "process", "env", "NEXT_MINIMAL", "id", "then", "mod", "default", "dynamicRequire", "__non_webpack_require__", "require", "MiddlewareMatcherCache", "WeakMap", "getMiddlewareMatcher", "info", "stored", "get", "Array", "isArray", "matchers", "Error", "JSON", "stringify", "matcher", "set", "NextNodeServer", "constructor", "options", "registeredInstrumentation", "cleanupListeners", "handleNextImageRequest", "req", "res", "parsedUrl", "pathname", "startsWith", "minimalMode", "nextConfig", "output", "statusCode", "body", "send", "ImageOptimizerCache", "imageOptimizerCache", "distDir", "sendResponse", "ImageError", "imageResponseCache", "imagesConfig", "images", "loader", "unoptimized", "render404", "paramsResult", "validateParams", "originalRequest", "query", "renderOpts", "dev", "errorMessage", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "getExtension", "previousCacheEntry", "buffer", "contentType", "maxAge", "upstreamEtag", "etag", "imageOptimizer", "value", "kind", "IMAGE", "extension", "<PERSON><PERSON><PERSON><PERSON>", "cacheControl", "revalidate", "expire", "undefined", "routeKind", "incrementalCache", "originalResponse", "href", "isStatic", "isMiss", "isStale", "Boolean", "err", "message", "handleCatchallRenderRequest", "i18n", "i18nProvider", "fromRequest", "match", "render", "edgeFunctionsPages", "getEdgeFunctionsPages", "edgeFunctionsPage", "definition", "page", "handled", "runEdgeFunction", "params", "appPaths", "apiError", "instrumentationOnRequestError", "routePath", "routerKind", "routeType", "revalidateReason", "handleApiRequest", "formatServerError", "logErrorWithOriginalStack", "logError", "renderError", "handleCatchallMiddlewareRequest", "parsed", "isMiddlewareInvoke", "handleFinished", "middleware", "getMiddleware", "initUrl", "pathnameInfo", "normalizedPathname", "maybeDecodedPathname", "decodeURIComponent", "result", "bubblingResult", "ensureMiddleware", "url", "runMiddleware", "request", "response", "key", "Object", "entries", "headers", "<PERSON><PERSON><PERSON><PERSON>", "status", "end", "code", "error", "console", "finished", "isDev", "sriEnabled", "conf", "experimental", "sri", "algorithm", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "nextScriptWorkers", "__NEXT_SCRIPT_WORKERS", "NEXT_DEPLOYMENT_ID", "deploymentId", "appDocumentPreloading", "isDefaultEnabled", "isAppPath", "catch", "preloadEntriesOnStart", "unstable_preloadEntries", "dynamicRoutes", "getRoutesManifest", "map", "r", "regex", "re", "serverOptions", "experimentalTestProxy", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "middlewareManifestPath", "serverDistDir", "prepare", "isExperimentalCompile", "appPathsManifest", "getAppPathsManifest", "pagesManifest", "getPagesManifest", "loadCustomCacheHandlers", "keys", "ComponentMod", "patchFetch", "webpackRequire", "__next_app__", "m", "handleUpgrade", "loadInstrumentationModule", "instrumentation", "dir", "cause", "prepareImpl", "runInstrumentationHookIfAvailable", "register", "forceReload", "silent", "cacheHandlers", "handler", "getIncrementalCache", "requestHeaders", "requestProtocol", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "getCacheFilesystem", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getResponseCache", "getPublicDir", "getHasStaticDir", "existsSync", "enabledDirectories", "app", "getinterceptionRoutePatterns", "routesManifest", "rewrites", "beforeFiles", "filter", "rewrite", "RegExp", "hasPage", "locales", "getBuildId", "buildIdFile", "readFileSync", "trim", "getEnabledDirectories", "pages", "type", "generateEtags", "poweredByHeader", "run<PERSON><PERSON>", "handledAsEdgeFunction", "module", "load", "filename", "previewProps", "bind", "trustHostHeader", "hostname", "fetchHostname", "onError", "multiZoneDraftMode", "renderHTML", "trace", "renderHTMLImpl", "nextFontManifest", "getServerComponentsHmrCache", "buildId", "customServer", "isDraftMode", "developmentNotFoundSourcePage", "fetchExternalImage", "fetchInternalImage", "handleInternalReq", "newReq", "newRes", "routerServerHandler", "isAbsolute", "imageUpstream", "renderPageComponent", "ctx", "bubbleNoFallback", "length", "getOriginalAppPaths", "findPageComponents", "locale", "spanName", "attributes", "findPageComponentsImpl", "_url", "pagePaths", "amp", "unshift", "path", "pagePath", "components", "Component", "getStaticProps", "getNextFontManifest", "_err", "_type", "ensurePage", "_opts", "normalizeReq", "normalizeRes", "getRequestHandler", "makeRequestHandler", "wrapRequestHandlerNode", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "mocked", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "unstable_onlyGenerated", "internal", "renderToHTML", "renderErrorToResponseImpl", "is404", "clientOnly", "includes", "setHeaders", "renderErrorToHTML", "getMiddlewareManifest", "manifest", "middlewareModule", "loadNodeMiddleware", "config", "regexp", "originalSource", "functions", "getEdgeFunctionInfo", "foundPage", "pageInfo", "name", "paths", "files", "file", "wasm", "binding", "filePath", "assets", "nodeMiddleware", "functionsConfig", "hasMiddleware", "ensureEdgeFunction", "_params", "isOnDemandRevalidate", "Response", "skipMiddlewareUrlNormalize", "toString", "port", "middlewareInfo", "method", "toUpperCase", "requestData", "basePath", "trailingSlash", "signal", "waitUntil", "getWaitUntil", "adapterFn", "run", "edgeFunctionEntry", "useCache", "onWarning", "has", "cookies", "getSetCookie", "flatMap", "maybeCompoundCookie", "delete", "cookie", "append", "_cachedPreviewManifest", "NODE_ENV", "NEXT_PHASE", "version", "routes", "notFoundRoutes", "preview", "previewModeId", "randomBytes", "previewModeSigningKey", "previewModeEncryptionKey", "afterFiles", "fallback", "attachRequestMeta", "isUpgradeReq", "protocol", "host", "edgeInfo", "isNextDataRequest", "initialUrl", "URL", "queryString", "fromEntries", "searchParams", "search", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "fetchMetrics", "statusMessage", "statusText", "for<PERSON>ach", "toLowerCase", "append<PERSON><PERSON>er", "_serverDistDir", "getFallbackErrorComponents", "args", "onServerClose", "listener", "add", "close", "runAll", "getInternalWaitUntil", "internalWaitUntil", "createInternalWaitUntil", "awaiter", "awaiting"], "mappings": "AAAA,OAAO,qBAAoB;AAC3B,OAAO,iBAAgB;AACvB,OAAO,yBAAwB;AAG/B,SACEA,WAAW,EACXC,iBAAiB,EACjBC,uBAAuB,QAClB,sBAAqB;AAkB5B,OAAOC,QAAQ,KAAI;AACnB,SAASC,IAAI,EAAEC,OAAO,QAAQ,OAAM;AACpC,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAgB;AAC/D,SACEC,cAAc,EACdC,aAAa,EACbC,mBAAmB,EACnBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,sBAAsB,EACtBC,gCAAgC,EAChCC,yBAAyB,QACpB,0BAAyB;AAChC,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,mBAAkB;AACpE,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,QAAQ,QAAQ,uCAAsC;AAC/D,YAAYC,SAAS,sBAAqB;AAa1C,OAAOC,cAAcC,eAAe,QAAQ,gBAAe;AAC3D,SAASC,gBAAgB,EAAEC,WAAW,QAAQ,YAAW;AACzD,SAASC,mBAAmB,QAAQ,gDAA+C;AACnF,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,cAAc,QAAQ,oBAAmB;AAElD,OAAOC,WAAWC,cAAc,QAAQ,kBAAiB;AACzD,SAASC,kBAAkB,EAAEC,yBAAyB,QAAQ,cAAa;AAC3E,SAASC,yBAAyB,QAAQ,sDAAqD;AAC/F,SAASC,aAAa,QAAQ,YAAW;AACzC,SAASC,sBAAsB,QAAQ,yCAAwC;AAC/E,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,mBAAmB,QAAQ,oDAAmD;AACvF,SAASC,gBAAgB,QAAQ,iBAAgB;AACjD,SAASC,yBAAyB,QAAQ,cAAa;AACvD,OAAOC,iBACLC,eAAe,QAEV,mBAAkB;AACzB,SAASC,gBAAgB,QAAQ,0BAAyB;AAC1D,SAASC,gBAAgB,QAAQ,uCAAsC;AAEvE,SAASC,4BAA4B,QAAQ,yBAAwB;AAErE,SAASC,oBAAoB,QAAQ,wCAAuC;AAG5E,SAASC,6BAA6B,QAAQ,mBAAkB;AAChE,SAASC,YAAY,EAAEC,SAAS,QAAQ,qBAAoB;AAC5D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,MAAM,QAAQ,wBAAuB;AAC9C,SAASC,aAAa,QAAQ,yCAAwC;AACtE,SAASC,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,0BAA0B,QAAQ,qBAAoB;AAC/D,SAASC,oBAAoB,QAAQ,0CAAyC;AAC9E,SAASC,sBAAsB,QAAQ,6CAA4C;AACnF,SAASC,iBAAiB,QAAQ,0CAAyC;AAC3E,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,yCAAwC;AAC1E,SAASC,mBAAmB,QAAQ,sCAAqC;AACzE,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,uBAAuB,QAAQ,oCAAmC;AAE3E,SAASC,0BAA0B,QAAQ,+CAA8C;AAEzF,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,uBAAuB,EAAEC,eAAe,QAAQ,uBAAsB;AAE/E,SAASC,iBAAiB,QAAQ,oBAAmB;AAErD,cAAc,gBAAe;AAI7B,yCAAyC;AACzC,MAAMC,0BAA0BC,QAAQC,GAAG,CAACC,YAAY,GACpD,CAACC,KACC,MAAM,CAAC,uBAAuB,GAAGA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID,OACpE,CAACF,KAAe,MAAM,CAACA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAE5D,2DAA2D;AAC3D,MAAME,iBAAiBP,QAAQC,GAAG,CAACC,YAAY,GAC3CM,0BACAC;AASJ,MAAMC,yBAAyB,IAAIC;AAKnC,SAASC,qBACPC,IAA8C;IAE9C,MAAMC,SAASJ,uBAAuBK,GAAG,CAACF;IAC1C,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,IAAI,CAACE,MAAMC,OAAO,CAACJ,KAAKK,QAAQ,GAAG;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAEC,KAAKC,SAAS,CAACR,OAAO,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMS,UAAU5D,0BAA0BmD,KAAKK,QAAQ;IACvDR,uBAAuBa,GAAG,CAACV,MAAMS;IACjC,OAAOA;AACT;AAEA,eAAe,MAAME,uBAAuBzE;IAyB1C0E,YAAYC,OAAgB,CAAE;YAMFA,gCAAAA;QAL1B,yBAAyB;QACzB,KAAK,CAACA,eAnBAC,4BAAqC,YAYnCC,mBAAmB,IAAIjC,yBAgsBvBkC,yBAA2C,OACnDC,KACAC,KACAC;YAEA,IAAI,CAACA,UAAUC,QAAQ,IAAI,CAACD,UAAUC,QAAQ,CAACC,UAAU,CAAC,iBAAiB;gBACzE,OAAO;YACT;YACA,qCAAqC;YACrC,IAAItG,eAAekG,KAAK,qBAAqB;gBAC3C,OAAO;YACT;YAEA,IACE,IAAI,CAACK,WAAW,IAChB,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,YAC3BrC,QAAQC,GAAG,CAACC,YAAY,EACxB;gBACA6B,IAAIO,UAAU,GAAG;gBACjBP,IAAIQ,IAAI,CAAC,eAAeC,IAAI;gBAC5B,OAAO;YACP,+CAA+C;YACjD,OAAO;gBACL,MAAM,EAAEC,mBAAmB,EAAE,GAC3BhC,QAAQ;gBAEV,MAAMiC,sBAAsB,IAAID,oBAAoB;oBAClDE,SAAS,IAAI,CAACA,OAAO;oBACrBP,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,MAAM,EAAEQ,YAAY,EAAEC,UAAU,EAAE,GAChCpC,QAAQ;gBAEV,IAAI,CAAC,IAAI,CAACqC,kBAAkB,EAAE;oBAC5B,MAAM,qBAAgE,CAAhE,IAAI3B,MAAM,wDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+D;gBACvE;gBACA,MAAM4B,eAAe,IAAI,CAACX,UAAU,CAACY,MAAM;gBAE3C,IAAID,aAAaE,MAAM,KAAK,aAAaF,aAAaG,WAAW,EAAE;oBACjE,MAAM,IAAI,CAACC,SAAS,CAACrB,KAAKC;oBAC1B,OAAO;gBACT;gBAEA,MAAMqB,eAAeX,oBAAoBY,cAAc,CACrDvB,IAAIwB,eAAe,EACnBtB,UAAUuB,KAAK,EACf,IAAI,CAACnB,UAAU,EACf,CAAC,CAAC,IAAI,CAACoB,UAAU,CAACC,GAAG;gBAGvB,IAAI,kBAAkBL,cAAc;oBAClCrB,IAAIO,UAAU,GAAG;oBACjBP,IAAIQ,IAAI,CAACa,aAAaM,YAAY,EAAElB,IAAI;oBACxC,OAAO;gBACT;gBAEA,MAAMmB,WAAWlB,oBAAoBmB,WAAW,CAACR;gBAEjD,IAAI;wBAiCES,mBAgBFA;oBAhDF,MAAM,EAAEC,YAAY,EAAE,GACpBrD,QAAQ;oBACV,MAAMoD,aAAa,MAAM,IAAI,CAACf,kBAAkB,CAAC/B,GAAG,CAClD4C,UACA,OAAO,EAAEI,kBAAkB,EAAE;wBAC3B,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAE,GACvD,MAAM,IAAI,CAACC,cAAc,CACvBvC,KACAC,KACAqB,cACAW;wBAGJ,OAAO;4BACLO,OAAO;gCACLC,MAAMrG,gBAAgBsG,KAAK;gCAC3BR;gCACAI;gCACAK,WAAWX,aAAaG;gCACxBE;4BACF;4BACAO,YAAY;4BACZC,cAAc;gCAAEC,YAAYV;gCAAQW,QAAQC;4BAAU;wBACxD;oBACF,GACA;wBACEC,WAAWvF,UAAUgF,KAAK;wBAC1BQ,kBAAkBtC;wBAClBgC,YAAY;oBACd;oBAGF,IAAIb,CAAAA,+BAAAA,oBAAAA,WAAYS,KAAK,qBAAjBT,kBAAmBU,IAAI,MAAKrG,gBAAgBsG,KAAK,EAAE;wBACrD,MAAM,qBAEL,CAFK,IAAIrD,MACR,0DADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEAyB,aACEd,IAAIwB,eAAe,EACnBvB,IAAIkD,gBAAgB,EACpB7B,aAAa8B,IAAI,EACjBrB,WAAWS,KAAK,CAACG,SAAS,EAC1BZ,WAAWS,KAAK,CAACN,MAAM,EACvBH,WAAWS,KAAK,CAACF,IAAI,EACrBhB,aAAa+B,QAAQ,EACrBtB,WAAWuB,MAAM,GAAG,SAASvB,WAAWwB,OAAO,GAAG,UAAU,OAC5DtC,cACAc,EAAAA,2BAAAA,WAAWc,YAAY,qBAAvBd,yBAAyBe,UAAU,KAAI,GACvCU,QAAQ,IAAI,CAAC9B,UAAU,CAACC,GAAG;oBAE7B,OAAO;gBACT,EAAE,OAAO8B,KAAK;oBACZ,IAAIA,eAAe1C,YAAY;wBAC7Bd,IAAIO,UAAU,GAAGiD,IAAIjD,UAAU;wBAC/BP,IAAIQ,IAAI,CAACgD,IAAIC,OAAO,EAAEhD,IAAI;wBAC1B,OAAO;oBACT;oBACA,MAAM+C;gBACR;YACF;QACF,QAEUE,8BAAgD,OACxD3D,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEsB,KAAK,EAAE,GAAGvB;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAA6C,CAA7C,IAAId,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,wEAAwE;YACxE,QAAQ;YACRxF,eAAemG,KAAK,oBAAoB;YAExC,IAAI;oBAKM;gBAJR,wDAAwD;gBACxDG,WAAWpE,oBAAoBoE;gBAE/B,MAAMP,UAAwB;oBAC5BgE,IAAI,GAAE,qBAAA,IAAI,CAACC,YAAY,qBAAjB,mBAAmBC,WAAW,CAAC9D,KAAKG;gBAC5C;gBACA,MAAM4D,QAAQ,MAAM,IAAI,CAAC3E,QAAQ,CAAC2E,KAAK,CAAC5D,UAAUP;gBAElD,sDAAsD;gBACtD,IAAI,CAACmE,OAAO;oBACV,MAAM,IAAI,CAACC,MAAM,CAAChE,KAAKC,KAAKE,UAAUsB,OAAOvB,WAAW;oBAExD,OAAO;gBACT;gBAEA,sEAAsE;gBACtE,wBAAwB;gBACxBrG,eAAemG,KAAK,SAAS+D;gBAE7B,yCAAyC;gBACzC,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;gBACrD,KAAK,MAAMC,qBAAqBF,mBAAoB;oBAClD,6DAA6D;oBAC7D,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACC,IAAI,EAAE;oBAEjD,IAAI,IAAI,CAAC/D,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACc,SAAS,CAACrB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBACA,OAAOuB,KAAK,CAACxE,qBAAqB;oBAElC,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI;wBACF,MAAMqH,UAAU,MAAM,IAAI,CAACC,eAAe,CAAC;4BACzCvE;4BACAC;4BACAwB;4BACA+C,QAAQT,MAAMS,MAAM;4BACpBH,MAAMN,MAAMK,UAAU,CAACC,IAAI;4BAC3BN;4BACAU,UAAU;wBACZ;wBACA,IAAIH,SAAS,OAAO;oBACtB,EAAE,OAAOI,UAAU;wBACjB,MAAM,IAAI,CAACC,6BAA6B,CAACD,UAAU1E,KAAK;4BACtD4E,WAAWb,MAAMK,UAAU,CAACC,IAAI;4BAChCQ,YAAY;4BACZC,WAAW;4BACX,oCAAoC;4BACpCC,kBAAkB/B;wBACpB;wBACA,MAAM0B;oBACR;gBACF;gBAEA,oEAAoE;gBACpE,MAAM;gBACN,iDAAiD;gBACjD,IAAIlI,qBAAqBuH,QAAQ;oBAC/B,IAAI,IAAI,CAACzD,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACc,SAAS,CAACrB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBAEA,MAAMoE,UAAU,MAAM,IAAI,CAACU,gBAAgB,CAAChF,KAAKC,KAAKwB,OAAOsC;oBAC7D,IAAIO,SAAS,OAAO;gBACtB;gBAEA,MAAM,IAAI,CAACN,MAAM,CAAChE,KAAKC,KAAKE,UAAUsB,OAAOvB,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOuD,KAAU;gBACjB,IAAIA,eAAevI,iBAAiB;oBAClC,MAAMuI;gBACR;gBAEA,IAAI;oBACF,IAAI,IAAI,CAAC/B,UAAU,CAACC,GAAG,EAAE;wBACvB,MAAM,EAAEsD,iBAAiB,EAAE,GACzBtG,QAAQ;wBACVsG,kBAAkBxB;wBAClB,IAAI,CAACyB,yBAAyB,CAACzB;oBACjC,OAAO;wBACL,IAAI,CAAC0B,QAAQ,CAAC1B;oBAChB;oBACAxD,IAAIO,UAAU,GAAG;oBACjB,MAAM,IAAI,CAAC4E,WAAW,CAAC3B,KAAKzD,KAAKC,KAAKE,UAAUsB;oBAChD,OAAO;gBACT,EAAE,OAAM,CAAC;gBAET,MAAMgC;YACR;QACF,QA+hBU4B,kCAAoD,OAC5DrF,KACAC,KACAqF;YAEA,MAAMC,qBAAqBzL,eAAekG,KAAK;YAE/C,IAAI,CAACuF,oBAAoB;gBACvB,OAAO;YACT;YAEA,MAAMC,iBAAiB;gBACrB3L,eAAemG,KAAK,oBAAoB;gBACxCC,IAAIQ,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;YAEA,MAAM+E,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,IAAI,CAACD,YAAY;gBACf,OAAOD;YACT;YAEA,MAAMG,UAAU7L,eAAekG,KAAK;YACpC,MAAME,YAAYnF,SAAS4K;YAC3B,MAAMC,eAAe5J,oBAAoBkE,UAAUC,QAAQ,EAAE;gBAC3DG,YAAY,IAAI,CAACA,UAAU;gBAC3BuD,cAAc,IAAI,CAACA,YAAY;YACjC;YAEA3D,UAAUC,QAAQ,GAAGyF,aAAazF,QAAQ;YAC1C,MAAM0F,qBAAqB9J,oBAAoBuJ,OAAOnF,QAAQ,IAAI;YAClE,IAAI2F,uBAAuBD;YAE3B,IAAI;gBACFC,uBAAuBC,mBAAmBF;YAC5C,EAAE,OAAM;YACN,+CAA+C,GACjD;YAEA,IACE,CACEJ,CAAAA,WAAW1B,KAAK,CAAC8B,oBAAoB7F,KAAKE,UAAUuB,KAAK,KACzDgE,WAAW1B,KAAK,CAAC+B,sBAAsB9F,KAAKE,UAAUuB,KAAK,CAAA,GAE7D;gBACA,OAAO+D;YACT;YAEA,IAAIQ;YAGJ,IAAIC,iBAAiB;YAErB,IAAI;gBACF,MAAM,IAAI,CAACC,gBAAgB,CAAClG,IAAImG,GAAG;gBAEnCH,SAAS,MAAM,IAAI,CAACI,aAAa,CAAC;oBAChCC,SAASrG;oBACTsG,UAAUrG;oBACVC,WAAWA;oBACXoF,QAAQA;gBACV;gBAEA,IAAI,cAAcU,QAAQ;oBACxB,IAAIT,oBAAoB;wBACtBU,iBAAiB;wBACjB,MAAM,qBAA8B,CAA9B,IAAIvJ,aAAa,MAAMsJ,SAAvB,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC;oBAEA,KAAK,MAAM,CAACO,KAAK/D,MAAM,IAAIgE,OAAOC,OAAO,CACvC9K,0BAA0BqK,OAAOM,QAAQ,CAACI,OAAO,GAChD;wBACD,IAAIH,QAAQ,sBAAsB/D,UAAUQ,WAAW;4BACrD/C,IAAI0G,SAAS,CAACJ,KAAK/D;wBACrB;oBACF;oBACAvC,IAAIO,UAAU,GAAGwF,OAAOM,QAAQ,CAACM,MAAM;oBAEvC,MAAM,EAAEzD,gBAAgB,EAAE,GAAGlD;oBAC7B,IAAI+F,OAAOM,QAAQ,CAAC7F,IAAI,EAAE;wBACxB,MAAM1D,mBAAmBiJ,OAAOM,QAAQ,CAAC7F,IAAI,EAAE0C;oBACjD,OAAO;wBACLA,iBAAiB0D,GAAG;oBACtB;oBACA,OAAO;gBACT;YACF,EAAE,OAAOpD,KAAc;gBACrB,IAAIwC,gBAAgB;oBAClB,MAAMxC;gBACR;gBAEA,IAAIjI,QAAQiI,QAAQA,IAAIqD,IAAI,KAAK,UAAU;oBACzC,MAAM,IAAI,CAACzF,SAAS,CAACrB,KAAKC,KAAKqF;oBAC/B,OAAO;gBACT;gBAEA,IAAI7B,eAAenK,aAAa;oBAC9B2G,IAAIO,UAAU,GAAG;oBACjB,MAAM,IAAI,CAAC4E,WAAW,CAAC3B,KAAKzD,KAAKC,KAAKqF,OAAOnF,QAAQ,IAAI;oBACzD,OAAO;gBACT;gBAEA,MAAM4G,QAAQtL,eAAegI;gBAC7BuD,QAAQD,KAAK,CAACA;gBACd9G,IAAIO,UAAU,GAAG;gBACjB,MAAM,IAAI,CAAC4E,WAAW,CAAC2B,OAAO/G,KAAKC,KAAKqF,OAAOnF,QAAQ,IAAI;gBAC3D,OAAO;YACT;YAEA,OAAO6F,OAAOiB,QAAQ;QACxB;QA3iDE,MAAMC,QAAQtH,QAAQ+B,GAAG,IAAI;QAC7B,IAAI,CAACuF,KAAK,GAAGA;QACb,IAAI,CAACC,UAAU,GAAG3D,SAAQ5D,6BAAAA,QAAQwH,IAAI,CAACC,YAAY,sBAAzBzH,iCAAAA,2BAA2B0H,GAAG,qBAA9B1H,+BAAgC2H,SAAS;QAEnE;;;;KAIC,GACD,IAAI,IAAI,CAAC7F,UAAU,CAAC8F,WAAW,EAAE;YAC/BtJ,QAAQC,GAAG,CAACsJ,mBAAmB,GAAGnI,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI,IAAI,CAACmC,UAAU,CAACgG,iBAAiB,EAAE;YACrCxJ,QAAQC,GAAG,CAACwJ,qBAAqB,GAAGrI,KAAKC,SAAS,CAAC;QACrD;QACArB,QAAQC,GAAG,CAACyJ,kBAAkB,GAAG,IAAI,CAACtH,UAAU,CAACuH,YAAY,IAAI;QAEjE,IAAI,CAAC,IAAI,CAACxH,WAAW,EAAE;YACrB,IAAI,CAACW,kBAAkB,GAAG,IAAI7E,cAAc,IAAI,CAACkE,WAAW;QAC9D;QAEA,MAAM,EAAEyH,qBAAqB,EAAE,GAAG,IAAI,CAACxH,UAAU,CAAC+G,YAAY;QAC9D,MAAMU,mBAAmB,OAAOD,0BAA0B;QAE1D,IACE,CAAClI,QAAQ+B,GAAG,IACXmG,CAAAA,0BAA0B,QACzB,CAAE,CAAA,IAAI,CAACzH,WAAW,IAAI0H,gBAAe,CAAC,GACxC;YACA,+CAA+C;YAC/C,2BAA2B;YAC3BxM,eAAe;gBACbsF,SAAS,IAAI,CAACA,OAAO;gBACrBwD,MAAM;gBACN2D,WAAW;gBACXd,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGc,KAAK,CAAC,KAAO;YAChB1M,eAAe;gBACbsF,SAAS,IAAI,CAACA,OAAO;gBACrBwD,MAAM;gBACN2D,WAAW;gBACXd,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGc,KAAK,CAAC,KAAO;QAClB;QAEA,IACE,CAACrI,QAAQ+B,GAAG,IACZ,CAAC,IAAI,CAACtB,WAAW,IACjB,IAAI,CAACC,UAAU,CAAC+G,YAAY,CAACa,qBAAqB,EAClD;YACA,IAAI,CAACC,uBAAuB;QAC9B;QAEA,IAAI,CAACvI,QAAQ+B,GAAG,EAAE;YAChB,MAAM,EAAEyG,gBAAgB,EAAE,EAAE,GAAG,IAAI,CAACC,iBAAiB,MAAM,CAAC;YAC5D,IAAI,CAACD,aAAa,GAAGA,cAAcE,GAAG,CAAC,CAACC;gBACtC,wDAAwD;gBACxD,MAAMC,QAAQ1L,cAAcyL,EAAElE,IAAI;gBAClC,MAAMN,QAAQnK,gBAAgB4O;gBAE9B,OAAO;oBACLzE;oBACAM,MAAMkE,EAAElE,IAAI;oBACZoE,IAAID,MAAMC,EAAE;gBACd;YACF;QACF;QAEA,sDAAsD;QACtDlM,6BAA6B,IAAI,CAAC+D,UAAU;QAE5C,2CAA2C;QAC3C,IAAI,IAAI,CAACoI,aAAa,CAACC,qBAAqB,EAAE;YAC5CzK,QAAQC,GAAG,CAACyK,uBAAuB,GAAG;YACtC,MAAM,EACJC,iBAAiB,EAClB,GAAGlK,QAAQ;YACZkK;QACF;QAEA,IAAI,CAACC,sBAAsB,GAAGpP,KAAK,IAAI,CAACqP,aAAa,EAAE9O;QAEvD,4EAA4E;QAC5E,2EAA2E;QAC3E,qEAAqE;QACrE,uEAAuE;QACvE,IAAI,CAAC2F,QAAQ+B,GAAG,EAAE;YAChB,IAAI,CAACqH,OAAO,GAAGf,KAAK,CAAC,CAACxE;gBACpBuD,QAAQD,KAAK,CAAC,4BAA4BtD;YAC5C;QACF;QAEA,yDAAyD;QACzD,yCAAyC;QACzC,IAAI,IAAI,CAAC/B,UAAU,CAACuH,qBAAqB,EAAE;YACzCjL,kBAAkB,IAAI,CAACsC,UAAU;QACnC;IACF;IAEA,MAAa6H,0BAAyC;QACpD,MAAMe,mBAAmB,IAAI,CAACC,mBAAmB;QACjD,MAAMC,gBAAgB,IAAI,CAACC,gBAAgB;QAE3C,MAAM,IAAI,CAACC,uBAAuB;QAElC,KAAK,MAAMjF,QAAQmC,OAAO+C,IAAI,CAACH,iBAAiB,CAAC,GAAI;YACnD,MAAM7N,eAAe;gBACnBsF,SAAS,IAAI,CAACA,OAAO;gBACrBwD;gBACA2D,WAAW;gBACXd,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGc,KAAK,CAAC,KAAO;QAClB;QAEA,KAAK,MAAM5D,QAAQmC,OAAO+C,IAAI,CAACL,oBAAoB,CAAC,GAAI;YACtD,MAAM3N,eAAe;gBACnBsF,SAAS,IAAI,CAACA,OAAO;gBACrBwD;gBACA2D,WAAW;gBACXd,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GACG7I,IAAI,CAAC,OAAO,EAAEkL,YAAY,EAAE;gBAC3B,iEAAiE;gBACjE,yEAAyE;gBACzE,oDAAoD;gBACpDA,aAAaC,UAAU;gBAEvB,MAAMC,iBAAiBF,aAAaG,YAAY,CAAChL,OAAO;gBACxD,IAAI+K,kCAAAA,eAAgBE,CAAC,EAAE;oBACrB,KAAK,MAAMvL,MAAMmI,OAAO+C,IAAI,CAACG,eAAeE,CAAC,EAAG;wBAC9C,MAAMF,eAAerL;oBACvB;gBACF;YACF,GACC4J,KAAK,CAAC,KAAO;QAClB;IACF;IAEA,MAAgB4B,gBAA+B;IAC7C,yEAAyE;IACzE,eAAe;IACjB;IAEA,MAAgBC,4BAA4B;QAC1C,IAAI,CAAC,IAAI,CAACpB,aAAa,CAAC/G,GAAG,EAAE;YAC3B,IAAI;gBACF,IAAI,CAACoI,eAAe,GAAG,MAAMtL,eAC3B9E,QACE,IAAI,CAAC+O,aAAa,CAACsB,GAAG,IAAI,KAC1B,IAAI,CAACtB,aAAa,CAACtB,IAAI,CAACvG,OAAO,EAC/B,UACApE;YAGN,EAAE,OAAOgH,KAAU;gBACjB,IAAIA,IAAIqD,IAAI,KAAK,oBAAoB;oBACnC,MAAM,qBAGL,CAHK,IAAIzH,MACR,4DACA;wBAAE4K,OAAOxG;oBAAI,IAFT,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QACA,OAAO,IAAI,CAACsG,eAAe;IAC7B;IAEA,MAAgBG,cAAc;QAC5B,MAAM,KAAK,CAACA;QACZ,MAAM,IAAI,CAACC,iCAAiC;IAC9C;IAEA,MAAgBA,oCAAoC;YAG5C,gCAAA;QAFN,IAAI,IAAI,CAACtK,yBAAyB,EAAE;QACpC,IAAI,CAACA,yBAAyB,GAAG;QACjC,QAAM,wBAAA,IAAI,CAACkK,eAAe,sBAApB,iCAAA,sBAAsBK,QAAQ,qBAA9B,oCAAA;IACR;IAEUvO,cAAc,EACtB8F,GAAG,EACH0I,WAAW,EACXC,MAAM,EAKP,EAAE;QACDzO,cACE,IAAI,CAACmO,GAAG,EACRrI,KACA2I,SAAS;YAAEvL,MAAM,KAAO;YAAGgI,OAAO,KAAO;QAAE,IAAI/L,KAC/CqP;IAEJ;IAEA,MAAcf,0BAA0B;QACtC,MAAM,EAAEiB,aAAa,EAAE,GAAG,IAAI,CAACjK,UAAU,CAAC+G,YAAY;QACtD,IAAI,CAACkD,eAAe;QAEpB,yEAAyE;QACzE,SAAS;QACT,IAAI,CAACzM,2BAA2B;QAEhC,KAAK,MAAM,CAAC2E,MAAM+H,QAAQ,IAAIhE,OAAOC,OAAO,CAAC8D,eAAgB;YAC3D,IAAI,CAACC,SAAS;YAEdzM,gBACE0E,MACAlF,eACE,MAAMU,wBACJT,wBAAwB,IAAI,CAACqD,OAAO,EAAE2J;QAI9C;IACF;IAEA,MAAgBC,oBAAoB,EAClCC,cAAc,EACdC,eAAe,EAIhB,EAAE;QACD,MAAMhJ,MAAM,CAAC,CAAC,IAAI,CAACD,UAAU,CAACC,GAAG;QACjC,IAAIiJ;QACJ,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAI,CAACvK,UAAU;QAExC,IAAIuK,cAAc;YAChBD,eAAerN,eACb,MAAMU,wBACJT,wBAAwB,IAAI,CAACqD,OAAO,EAAEgK;QAG5C;QAEA,MAAM,IAAI,CAACvB,uBAAuB;QAElC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIjN,iBAAiB;YAC1B5C,IAAI,IAAI,CAACqR,kBAAkB;YAC3BnJ;YACA+I;YACAC;YACAI,6BACE,IAAI,CAACzK,UAAU,CAAC+G,YAAY,CAAC0D,2BAA2B;YAC1D1K,aAAa,IAAI,CAACA,WAAW;YAC7B0I,eAAe,IAAI,CAACA,aAAa;YACjCiC,qBAAqB,IAAI,CAAC1K,UAAU,CAAC+G,YAAY,CAAC2D,mBAAmB;YACrEC,oBAAoB,IAAI,CAAC3K,UAAU,CAAC4K,kBAAkB;YACtDC,aACE,CAAC,IAAI,CAAC9K,WAAW,IAAI,IAAI,CAACC,UAAU,CAAC+G,YAAY,CAAC+D,cAAc;YAClEC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrDC,iBAAiBV;QACnB;IACF;IAEUW,mBAAmB;QAC3B,OAAO,IAAIpP,cAAc,IAAI,CAACkE,WAAW;IAC3C;IAEUmL,eAAuB;QAC/B,OAAO9R,KAAK,IAAI,CAACsQ,GAAG,EAAE5P;IACxB;IAEUqR,kBAA2B;QACnC,OAAOhS,GAAGiS,UAAU,CAAChS,KAAK,IAAI,CAACsQ,GAAG,EAAE;IACtC;IAEUX,mBAA8C;QACtD,OAAOjM,aACL1D,KAAK,IAAI,CAACqP,aAAa,EAAEhP;IAE7B;IAEUoP,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACwC,kBAAkB,CAACC,GAAG,EAAE,OAAO5I;QAEzC,OAAO5F,aACL1D,KAAK,IAAI,CAACqP,aAAa,EAAE1O;IAE7B;IAEUwR,+BAAyC;QACjD,IAAI,CAAC,IAAI,CAACF,kBAAkB,CAACC,GAAG,EAAE,OAAO,EAAE;QAE3C,MAAME,iBAAiB,IAAI,CAACzD,iBAAiB;QAC7C,OACEyD,CAAAA,kCAAAA,eAAgBC,QAAQ,CAACC,WAAW,CACjCC,MAAM,CAACxO,4BACP6K,GAAG,CAAC,CAAC4D,UAAY,IAAIC,OAAOD,QAAQ1D,KAAK,OAAM,EAAE;IAExD;IAEA,MAAgB4D,QAAQjM,QAAgB,EAAoB;YAIxD;QAHF,OAAO,CAAC,CAAChF,iBACPgF,UACA,IAAI,CAACU,OAAO,GACZ,wBAAA,IAAI,CAACP,UAAU,CAACsD,IAAI,qBAApB,sBAAsByI,OAAO,EAC7B,IAAI,CAACV,kBAAkB,CAACC,GAAG;IAE/B;IAEUU,aAAqB;QAC7B,MAAMC,cAAc7S,KAAK,IAAI,CAACmH,OAAO,EAAE7G;QACvC,IAAI;YACF,OAAOP,GAAG+S,YAAY,CAACD,aAAa,QAAQE,IAAI;QAClD,EAAE,OAAOhJ,KAAU;YACjB,IAAIA,IAAIqD,IAAI,KAAK,UAAU;gBACzB,MAAM,qBAEL,CAFK,IAAIzH,MACR,CAAC,0CAA0C,EAAE,IAAI,CAACwB,OAAO,CAAC,yJAAyJ,CAAC,GADhN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM4C;QACR;IACF;IAEUiJ,sBAAsB/K,GAAY,EAA0B;QACpE,MAAMqI,MAAMrI,MAAM,IAAI,CAACqI,GAAG,GAAG,IAAI,CAACjB,aAAa;QAE/C,OAAO;YACL6C,KAAKjR,QAAQqP,KAAK,SAAS,OAAO;YAClC2C,OAAOhS,QAAQqP,KAAK,WAAW,OAAO;QACxC;IACF;IAEUlP,iBACRkF,GAAoB,EACpBC,GAAqB,EACrBL,OAMC,EACc;QACf,OAAO9E,iBAAiB;YACtBkF,KAAKA,IAAIwB,eAAe;YACxBvB,KAAKA,IAAIkD,gBAAgB;YACzB6C,QAAQpG,QAAQoG,MAAM;YACtB4G,MAAMhN,QAAQgN,IAAI;YAClBC,eAAejN,QAAQiN,aAAa;YACpCC,iBAAiBlN,QAAQkN,eAAe;YACxCjK,cAAcjD,QAAQiD,YAAY;QACpC;IACF;IAEA,MAAgBkK,OACd/M,GAAoB,EACpBC,GAAqB,EACrBwB,KAAqB,EACrBsC,KAAyB,EACP;QAClB,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;QAErD,KAAK,MAAMC,qBAAqBF,mBAAoB;YAClD,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACjE,QAAQ,EAAE;gBACnD,MAAM6M,wBAAwB,MAAM,IAAI,CAACzI,eAAe,CAAC;oBACvDvE;oBACAC;oBACAwB;oBACA+C,QAAQT,MAAMS,MAAM;oBACpBH,MAAMN,MAAMK,UAAU,CAACjE,QAAQ;oBAC/BsE,UAAU;gBACZ;gBAEA,IAAIuI,uBAAuB;oBACzB,OAAO;gBACT;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAMC,SAAS,MAAM9P,kBAAkB+P,IAAI,CACzCnJ,MAAMK,UAAU,CAAC+I,QAAQ;QAG3B1L,QAAQ;YAAE,GAAGA,KAAK;YAAE,GAAGsC,MAAMS,MAAM;QAAC;QAEpC,MAAMyI,OAAOjJ,MAAM,CAAChE,IAAIwB,eAAe,EAAEvB,IAAIkD,gBAAgB,EAAE;YAC7DiK,cAAc,IAAI,CAAC1L,UAAU,CAAC0L,YAAY;YAC1CtK,YAAY,IAAI,CAACA,UAAU,CAACuK,IAAI,CAAC,IAAI;YACrCC,iBAAiB,IAAI,CAAChN,UAAU,CAAC+G,YAAY,CAACiG,eAAe;YAC7DvC,6BACE,IAAI,CAACzK,UAAU,CAAC+G,YAAY,CAAC0D,2BAA2B;YAC1DwC,UAAU,IAAI,CAACC,aAAa;YAC5BnN,aAAa,IAAI,CAACA,WAAW;YAC7BsB,KAAK,IAAI,CAACD,UAAU,CAACC,GAAG,KAAK;YAC7BF;YACA+C,QAAQT,MAAMS,MAAM;YACpBH,MAAMN,MAAMK,UAAU,CAACjE,QAAQ;YAC/BsN,SAAS,IAAI,CAAC9I,6BAA6B,CAAC0I,IAAI,CAAC,IAAI;YACrDK,oBAAoB,IAAI,CAACpN,UAAU,CAAC+G,YAAY,CAACqG,kBAAkB;QACrE;QAEA,OAAO;IACT;IAEA,MAAgBC,WACd3N,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBsB,KAAyB,EACzBC,UAA4B,EACL;QACvB,OAAO/E,YAAYiR,KAAK,CAAChR,mBAAmB+Q,UAAU,EAAE,UACtD,IAAI,CAACE,cAAc,CAAC7N,KAAKC,KAAKE,UAAUsB,OAAOC;IAEnD;IAEA,MAAcmM,eACZ7N,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBsB,KAAyB,EACzBC,UAA4B,EACL;QACvB,IAAIxD,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACA,+CAA+C;QACjD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9D,4HAA4H;YAC5HqC,WAAWoM,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAEnD,IAAI,IAAI,CAACnC,kBAAkB,CAACC,GAAG,IAAIlK,WAAWsG,SAAS,EAAE;gBACvD,OAAO3K,kBACL2C,KACAC,KACAE,UACAsB,OACA,kEAAkE;gBAClE,oEAAoE;gBACpE,MACAC,YACA,IAAI,CAACqM,2BAA2B,IAChC,OACA;oBACEC,SAAS,IAAI,CAACA,OAAO;gBACvB;YAEJ;YAEA,qEAAqE;YACrE,oEAAoE;YAEpE,OAAO1Q,oBACL0C,IAAIwB,eAAe,EACnBvB,IAAIkD,gBAAgB,EACpBhD,UACAsB,OACAC,YACA;gBACEsM,SAAS,IAAI,CAACA,OAAO;gBACrBnG,cAAc,IAAI,CAACvH,UAAU,CAACuH,YAAY;gBAC1CoG,cAAc,IAAI,CAACvF,aAAa,CAACuF,YAAY,IAAIjL;YACnD,GACA;gBACEJ,YAAY;gBACZsL,aAAaxM,WAAWwM,WAAW;gBACnCC,+BAA+BrU,eAC7BkG,KACA;YAEJ;QAEJ;IACF;IAEA,MAAgBuC,eACdvC,GAAoB,EACpBC,GAAqB,EACrBqB,YAA2D,EAC3DW,kBAAyD,EAOxD;QACD,IAAI/D,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACL,MAAM,EAAEkD,cAAc,EAAE6L,kBAAkB,EAAEC,kBAAkB,EAAE,GAC9D1P,QAAQ;YAEV,MAAM2P,oBAAoB,OACxBC,QACAC;gBAEA,IAAID,OAAOpI,GAAG,KAAKnG,IAAImG,GAAG,EAAE;oBAC1B,MAAM,qBAA+D,CAA/D,IAAI9G,MAAM,CAAC,kDAAkD,CAAC,GAA9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA8D;gBACtE;gBAEA,IAAI,CAAC,IAAI,CAACoP,mBAAmB,EAAE;oBAC7B,MAAM,qBAAkD,CAAlD,IAAIpP,MAAM,CAAC,qCAAqC,CAAC,GAAjD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiD;gBACzD;gBAEA,MAAM,IAAI,CAACoP,mBAAmB,CAACF,QAAQC;gBACvC;YACF;YAEA,MAAM,EAAEE,UAAU,EAAEtL,IAAI,EAAE,GAAG9B;YAE7B,MAAMqN,gBAAgBD,aAClB,MAAMN,mBAAmBhL,QACzB,MAAMiL,mBACJjL,MACApD,IAAIwB,eAAe,EACnBvB,IAAIkD,gBAAgB,EACpBmL;YAGN,OAAO/L,eAAeoM,eAAerN,cAAc,IAAI,CAAChB,UAAU,EAAE;gBAClE4G,OAAO,IAAI,CAACxF,UAAU,CAACC,GAAG;gBAC1BM;YACF;QACF;IACF;IAEU7G,YAAY+E,QAAgB,EAAEkM,OAAkB,EAAU;QAClE,OAAOjR,YACL+E,UACA,IAAI,CAACU,OAAO,EACZwL,SACA,IAAI,CAACV,kBAAkB,CAACC,GAAG;IAE/B;IAEA,MAAgBgD,oBACdC,GAAsD,EACtDC,gBAAyB,EACzB;QACA,MAAM7K,qBAAqB,IAAI,CAACC,qBAAqB,MAAM,EAAE;QAC7D,IAAID,mBAAmB8K,MAAM,EAAE;YAC7B,MAAMtK,WAAW,IAAI,CAACuK,mBAAmB,CAACH,IAAI1O,QAAQ;YACtD,MAAM6H,YAAY9I,MAAMC,OAAO,CAACsF;YAEhC,IAAIJ,OAAOwK,IAAI1O,QAAQ;YACvB,IAAI6H,WAAW;gBACb,yEAAyE;gBACzE3D,OAAOI,QAAQ,CAAC,EAAE;YACpB;YAEA,KAAK,MAAMN,qBAAqBF,mBAAoB;gBAClD,IAAIE,sBAAsBE,MAAM;oBAC9B,MAAM,IAAI,CAACE,eAAe,CAAC;wBACzBvE,KAAK6O,IAAI7O,GAAG;wBACZC,KAAK4O,IAAI5O,GAAG;wBACZwB,OAAOoN,IAAIpN,KAAK;wBAChB+C,QAAQqK,IAAInN,UAAU,CAAC8C,MAAM;wBAC7BH;wBACAI;oBACF;oBACA,OAAO;gBACT;YACF;QACF;QAEA,OAAO,KAAK,CAACmK,oBAAoBC,KAAKC;IACxC;IAEA,MAAgBG,mBAAmB,EACjCC,MAAM,EACN7K,IAAI,EACJ5C,KAAK,EACL+C,MAAM,EACNwD,SAAS,EACT7B,GAAG,EAaJ,EAAwC;QACvC,OAAOxJ,YAAYiR,KAAK,CACtBhR,mBAAmBqS,kBAAkB,EACrC;YACEE,UAAU;YACVC,YAAY;gBACV,cAAcpH,YAAY1L,iBAAiB+H,QAAQA;YACrD;QACF,GACA,IACE,IAAI,CAACgL,sBAAsB,CAAC;gBAC1BH;gBACA7K;gBACA5C;gBACA+C;gBACAwD;gBACA7B;YACF;IAEN;IAEA,MAAckJ,uBAAuB,EACnCH,MAAM,EACN7K,IAAI,EACJ5C,KAAK,EACL+C,MAAM,EACNwD,SAAS,EACT7B,KAAKmJ,IAAI,EAQV,EAAwC;QACvC,MAAMC,YAAsB;YAAClL;SAAK;QAClC,IAAI5C,MAAM+N,GAAG,EAAE;YACb,yCAAyC;YACzCD,UAAUE,OAAO,CACf,AAACzH,CAAAA,YAAY1L,iBAAiB+H,QAAQ/I,kBAAkB+I,KAAI,IAAK;QAErE;QAEA,IAAI6K,QAAQ;YACVK,UAAUE,OAAO,IACZF,UAAUjH,GAAG,CAAC,CAACoH,OAAS,CAAC,CAAC,EAAER,SAASQ,SAAS,MAAM,KAAKA,MAAM;QAEtE;QAEA,KAAK,MAAMC,YAAYJ,UAAW;YAChC,IAAI;gBACF,MAAMK,aAAa,MAAMrU,eAAe;oBACtCsF,SAAS,IAAI,CAACA,OAAO;oBACrBwD,MAAMsL;oBACN3H;oBACAd,OAAO,IAAI,CAACA,KAAK;oBACjBC,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,IACE+H,UACA,OAAOU,WAAWC,SAAS,KAAK,YAChC,CAACF,SAASvP,UAAU,CAAC,CAAC,CAAC,EAAE8O,OAAO,CAAC,CAAC,KAClCS,aAAa,CAAC,CAAC,EAAET,QAAQ,EACzB;oBAGA;gBACF;gBAEA,OAAO;oBACLU;oBACAnO,OAAO;wBACL,GAAI,CAAC,IAAI,CAACC,UAAU,CAACuH,qBAAqB,IAC1C2G,WAAWE,cAAc,GACpB;4BACCN,KAAK/N,MAAM+N,GAAG;wBAChB,IACA/N,KAAK;wBACT,iCAAiC;wBACjC,GAAI,AAACuG,CAAAA,YAAY,CAAC,IAAIxD,MAAK,KAAM,CAAC,CAAC;oBACrC;gBACF;YACF,EAAE,OAAOf,KAAK;gBACZ,yDAAyD;gBACzD,wBAAwB;gBACxB,IAAI,CAAEA,CAAAA,eAAelK,iBAAgB,GAAI;oBACvC,MAAMkK;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEUsM,sBAAoD;QAC5D,OAAO3S,aACL1D,KAAK,IAAI,CAACmH,OAAO,EAAE,UAAUtG,qBAAqB;IAEtD;IA2OA,0DAA0D;IAChD2K,0BACR8K,IAAc,EACdC,KAA0E,EACpE;QACN,MAAM,qBAEL,CAFK,IAAI5Q,MACR,sFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,0DAA0D;IAC1D,MAAgB6Q,WAAWC,KAM1B,EAAiB;QAChB,MAAM,qBAEL,CAFK,IAAI9Q,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA;;;;;GAKC,GACD,MAAgB2F,iBACdhF,GAAoB,EACpBC,GAAqB,EACrBwB,KAAqB,EACrBsC,KAAyB,EACP;QAClB,OAAO,IAAI,CAACgJ,MAAM,CAAC/M,KAAKC,KAAKwB,OAAOsC;IACtC;IAEU+G,qBAA8B;QACtC,OAAOjO;IACT;IAEUuT,aACRpQ,GAAsC,EACrB;QACjB,OAAO,CAAEA,CAAAA,eAAepF,eAAc,IAAK,IAAIA,gBAAgBoF,OAAOA;IACxE;IAEUqQ,aACRpQ,GAAsC,EACpB;QAClB,OAAO,CAAEA,CAAAA,eAAepF,gBAAe,IAAK,IAAIA,iBAAiBoF,OAAOA;IAC1E;IAEOqQ,oBAAwC;QAC7C,MAAM9F,UAAU,IAAI,CAAC+F,kBAAkB;QACvC,IAAI,IAAI,CAAC7H,aAAa,CAACC,qBAAqB,EAAE;YAC5C,MAAM,EACJ6H,sBAAsB,EACvB,GAAG7R,QAAQ;YACZ,OAAO6R,uBAAuBhG;QAChC;QACA,OAAOA;IACT;IAEQ+F,qBAAyC;QAC/C,4EAA4E;QAC5E,2EAA2E;QAC3E,sEAAsE;QACtE,uEAAuE;QACvE,IAAI,CAACvH,OAAO,GAAGf,KAAK,CAAC,CAACxE;YACpBuD,QAAQD,KAAK,CAAC,4BAA4BtD;QAC5C;QAEA,MAAM+G,UAAU,KAAK,CAAC8F;QAEtB,OAAO,CAACtQ,KAAKC,KAAKC,YAChBsK,QAAQ,IAAI,CAAC4F,YAAY,CAACpQ,MAAM,IAAI,CAACqQ,YAAY,CAACpQ,MAAMC;IAC5D;IAEA,MAAa4C,WAAW,EACtB2N,OAAO,EACPC,iBAAiB,EACjBC,IAAI,EAKL,EAAE;QACD,MAAMC,SAAS5T,2BAA2B;YACxCmJ,KAAKsK;YACL/J,SAASgK;QACX;QAEA,MAAMlG,UAAU,IAAI,CAAC8F,iBAAiB;QACtC,MAAM9F,QACJ,IAAI5P,gBAAgBgW,OAAO5Q,GAAG,GAC9B,IAAInF,iBAAiB+V,OAAO3Q,GAAG;QAEjC,MAAM2Q,OAAO3Q,GAAG,CAAC4Q,WAAW;QAE5B,IACED,OAAO3Q,GAAG,CAAC6Q,SAAS,CAAC,sBAAsB,iBAC3CF,OAAO3Q,GAAG,CAACO,UAAU,KAAK,OAC1B,CAAEoQ,CAAAA,OAAO3Q,GAAG,CAACO,UAAU,KAAK,OAAOmQ,KAAKI,sBAAsB,AAAD,GAC7D;YACA,MAAM,qBAAsD,CAAtD,IAAI1R,MAAM,CAAC,iBAAiB,EAAEuR,OAAO3Q,GAAG,CAACO,UAAU,EAAE,GAArD,qBAAA;uBAAA;4BAAA;8BAAA;YAAqD;QAC7D;IACF;IAEA,MAAawD,OACXhE,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBsB,KAA0B,EAC1BvB,SAAkC,EAClC8Q,WAAW,KAAK,EACD;QACf,OAAO,KAAK,CAAChN,OACX,IAAI,CAACoM,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAsB,OACAvB,WACA8Q;IAEJ;IAEA,MAAaC,aACXjR,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBsB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACwP,aACX,IAAI,CAACb,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAsB;IAEJ;IAEA,MAAgByP,0BACdrC,GAAsD,EACtDpL,GAAiB,EACjB;QACA,MAAM,EAAEzD,GAAG,EAAEC,GAAG,EAAEwB,KAAK,EAAE,GAAGoN;QAC5B,MAAMsC,QAAQlR,IAAIO,UAAU,KAAK;QAEjC,IAAI2Q,SAAS,IAAI,CAACxF,kBAAkB,CAACC,GAAG,EAAE;YACxC,IAAI,IAAI,CAAClK,UAAU,CAACC,GAAG,EAAE;gBACvB,MAAM,IAAI,CAACuO,UAAU,CAAC;oBACpB7L,MAAM5J;oBACN2W,YAAY;oBACZjL,KAAKnG,IAAImG,GAAG;gBACd,GAAG8B,KAAK,CAAC,KAAO;YAClB;YAEA,IACE,IAAI,CAAC/D,qBAAqB,GAAGmN,QAAQ,CAAC5W,mCACtC;gBACA,MAAM,IAAI,CAAC8J,eAAe,CAAC;oBACzBvE;oBACAC;oBACAwB,OAAOA,SAAS,CAAC;oBACjB+C,QAAQ,CAAC;oBACTH,MAAM5J;oBACNgK,UAAU;gBACZ;gBACA,OAAO;YACT;QACF;QACA,OAAO,KAAK,CAACyM,0BAA0BrC,KAAKpL;IAC9C;IAEA,MAAa2B,YACX3B,GAAiB,EACjBzD,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBsB,KAA0B,EAC1B6P,UAAoB,EACL;QACf,OAAO,KAAK,CAAClM,YACX3B,KACA,IAAI,CAAC2M,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAsB,OACA6P;IAEJ;IAEA,MAAaC,kBACX9N,GAAiB,EACjBzD,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBsB,KAAsB,EACE;QACxB,OAAO,KAAK,CAAC8P,kBACX9N,KACA,IAAI,CAAC2M,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBE,UACAsB;IAEJ;IAEA,MAAaJ,UACXrB,GAAsC,EACtCC,GAAsC,EACtCC,SAAkC,EAClCoR,UAAoB,EACL;QACf,OAAO,KAAK,CAACjQ,UACX,IAAI,CAAC+O,YAAY,CAACpQ,MAClB,IAAI,CAACqQ,YAAY,CAACpQ,MAClBC,WACAoR;IAEJ;IAEUE,wBAAmD;QAC3D,IAAI,IAAI,CAACnR,WAAW,EAAE;YACpB,OAAO;QACT,OAAO;YACL,MAAMoR,WAA+B9S,QAAQ,IAAI,CAACmK,sBAAsB;YACxE,OAAO2I;QACT;IACF;IAEA,yDAAyD,GACzD,MAAgB/L,gBAA4D;YAEvD+L;QADnB,MAAMA,WAAW,IAAI,CAACD,qBAAqB;QAC3C,MAAM/L,aAAagM,6BAAAA,uBAAAA,SAAUhM,UAAU,qBAApBgM,oBAAsB,CAAC,IAAI;QAC9C,IAAI,CAAChM,YAAY;YACf,MAAMiM,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEtD,IAAID,kBAAkB;oBAGhBA;gBAFJ,OAAO;oBACL3N,OAAOnI,0BACL8V,EAAAA,2BAAAA,iBAAiBE,MAAM,qBAAvBF,yBAAyBtS,QAAQ,KAAI;wBACnC;4BAAEyS,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBAEHzN,MAAM;gBACR;YACF;YAEA;QACF;QAEA,OAAO;YACLN,OAAOjF,qBAAqB2G;YAC5BpB,MAAM;QACR;IACF;IAEUH,wBAAkC;QAC1C,MAAMuN,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO,EAAE;QACX;QAEA,OAAOjL,OAAO+C,IAAI,CAACkI,SAASM,SAAS;IACvC;IAEA;;;;GAIC,GACD,AAAUC,oBAAoBxN,MAI7B,EAMQ;QACP,MAAMiN,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO;QACT;QAEA,IAAIQ;QAEJ,IAAI;YACFA,YAAY5W,oBAAoBC,kBAAkBkJ,OAAOH,IAAI;QAC/D,EAAE,OAAOZ,KAAK;YACZ,OAAO;QACT;QAEA,IAAIyO,WAAW1N,OAAOiB,UAAU,GAC5BgM,SAAShM,UAAU,CAACwM,UAAU,GAC9BR,SAASM,SAAS,CAACE,UAAU;QAEjC,IAAI,CAACC,UAAU;YACb,IAAI,CAAC1N,OAAOiB,UAAU,EAAE;gBACtB,MAAM,IAAIlM,kBAAkB0Y;YAC9B;YACA,OAAO;QACT;QAEA,OAAO;YACLE,MAAMD,SAASC,IAAI;YACnBC,OAAOF,SAASG,KAAK,CAAC/J,GAAG,CAAC,CAACgK,OAAS5Y,KAAK,IAAI,CAACmH,OAAO,EAAEyR;YACvDC,MAAM,AAACL,CAAAA,SAASK,IAAI,IAAI,EAAE,AAAD,EAAGjK,GAAG,CAAC,CAACkK,UAAa,CAAA;oBAC5C,GAAGA,OAAO;oBACVC,UAAU/Y,KAAK,IAAI,CAACmH,OAAO,EAAE2R,QAAQC,QAAQ;gBAC/C,CAAA;YACAC,QACER,SAASQ,MAAM,IACfR,SAASQ,MAAM,CAACpK,GAAG,CAAC,CAACkK;gBACnB,OAAO;oBACL,GAAGA,OAAO;oBACVC,UAAU/Y,KAAK,IAAI,CAACmH,OAAO,EAAE2R,QAAQC,QAAQ;gBAC/C;YACF;YACFtU,KAAK+T,SAAS/T,GAAG;QACnB;IACF;IAEA,MAAcwT,qBAAqB;QACjC,IAAI,CAAC,IAAI,CAACrR,UAAU,CAAC+G,YAAY,CAACsL,cAAc,EAAE;YAChD;QACF;QAEA,IAAI;gBAKyBC;YAJ3B,MAAMA,kBAAkB,IAAI,CAAClR,UAAU,CAACC,GAAG,GACvC,CAAC,IACDhD,QAAQjF,KAAK,IAAI,CAACmH,OAAO,EAAE,UAAUnG;YAEzC,IAAI,IAAI,CAACgH,UAAU,CAACC,GAAG,KAAIiR,oCAAAA,6BAAAA,gBAAiBb,SAAS,qBAA1Ba,0BAA4B,CAAC,eAAe,GAAE;gBACvE,uDAAuD;gBACvD,OAAOjU,QAAQjF,KAAK,IAAI,CAACmH,OAAO,EAAE,UAAU;YAC9C;QACF,EAAE,OAAO4C,KAAK;YACZ,IACEjI,QAAQiI,QACRA,IAAIqD,IAAI,KAAK,YACbrD,IAAIqD,IAAI,KAAK,oBACb;gBACA,MAAMrD;YACR;QACF;IACF;IAEA;;;;GAIC,GACD,MAAgBoP,cAAc1S,QAAgB,EAAoB;QAChE,MAAMpB,OAAO,IAAI,CAACiT,mBAAmB,CAAC;YAAE3N,MAAMlE;YAAUsF,YAAY;QAAK;QACzE,MAAMkN,iBAAiB,MAAM,IAAI,CAAChB,kBAAkB;QAEpD,IAAI,CAAC5S,QAAQ4T,gBAAgB;YAC3B,OAAO;QACT;QACA,OAAOnP,QAAQzE,QAAQA,KAAKqT,KAAK,CAACrD,MAAM,GAAG;IAC7C;IAEA;;;;GAIC,GACD,MAAgB7I,iBAAiBoJ,IAAa,EAAE,CAAC;IACjD,MAAgBwD,mBAAmBC,OAIlC,EAAE,CAAC;IAEJ;;;;;GAKC,GACD,MAAgB3M,cAAc5B,MAM7B,EAAE;QACD,IAAItG,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,0DAA0D;QAC1D,IACEnD,0BAA0BsI,OAAO6B,OAAO,EAAE,IAAI,CAAC3E,UAAU,CAAC0L,YAAY,EACnE4F,oBAAoB,EACvB;YACA,OAAO;gBACL1M,UAAU,IAAI2M,SAAS,MAAM;oBAAEvM,SAAS;wBAAE,qBAAqB;oBAAI;gBAAE;YACvE;QACF;QAEA,IAAIP;QAEJ,IAAI,IAAI,CAAC7F,UAAU,CAAC4S,0BAA0B,EAAE;YAC9C/M,MAAMrM,eAAe0K,OAAO6B,OAAO,EAAE;QACvC,OAAO;YACL,mEAAmE;YACnE,MAAM5E,QAAQ3F,uBAAuB0I,OAAOc,MAAM,CAAC7D,KAAK,EAAE0R,QAAQ;YAClE,MAAMjE,SAASpV,eAAe0K,OAAO6B,OAAO,EAAE;YAE9CF,MAAM,GAAGrM,eAAe0K,OAAO6B,OAAO,EAAE,gBAAgB,GAAG,EACzD,IAAI,CAACmH,aAAa,IAAI,YACvB,CAAC,EAAE,IAAI,CAAC4F,IAAI,GAAGlE,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAK1K,OAAOc,MAAM,CAACnF,QAAQ,GACjEsB,QAAQ,CAAC,CAAC,EAAEA,OAAO,GAAG,IACtB;QACJ;QAEA,IAAI,CAAC0E,IAAI/F,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIf,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMgF,OAGF,CAAC;QAEL,MAAMoB,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IAAI,CAACD,YAAY;YACf,OAAO;gBAAEwB,UAAU;YAAM;QAC3B;QACA,IAAI,CAAE,MAAM,IAAI,CAAC4L,aAAa,CAACpN,WAAWpB,IAAI,GAAI;YAChD,OAAO;gBAAE4C,UAAU;YAAM;QAC3B;QAEA,MAAM,IAAI,CAACf,gBAAgB,CAAC1B,OAAO6B,OAAO,CAACF,GAAG;QAC9C,MAAMkN,iBAAiB,IAAI,CAACrB,mBAAmB,CAAC;YAC9C3N,MAAMoB,WAAWpB,IAAI;YACrBoB,YAAY;QACd;QAEA,MAAM6N,SAAS,AAAC9O,CAAAA,OAAO6B,OAAO,CAACiN,MAAM,IAAI,KAAI,EAAGC,WAAW;QAC3D,MAAMC,cAAc;YAClB9M,SAASlC,OAAO6B,OAAO,CAACK,OAAO;YAC/B4M;YACAhT,YAAY;gBACVmT,UAAU,IAAI,CAACnT,UAAU,CAACmT,QAAQ;gBAClC7P,MAAM,IAAI,CAACtD,UAAU,CAACsD,IAAI;gBAC1B8P,eAAe,IAAI,CAACpT,UAAU,CAACoT,aAAa;gBAC5CrM,cAAc,IAAI,CAAC/G,UAAU,CAAC+G,YAAY;YAC5C;YACAlB,KAAKA;YACL9B;YACA5D,MACE6S,WAAW,SAASA,WAAW,SAC1BxZ,eAAe0K,OAAO6B,OAAO,EAAE,kBAChCrD;YAEN2Q,QAAQzW,uBAAuBsH,OAAO8B,QAAQ,CAACnD,gBAAgB;YAC/DyQ,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,IAAI7N;QAIJ,qDAAqD;QACrD,8DAA8D;QAC9D,6DAA6D;QAC7D,6DAA6D;QAC7D,uBAAuB;QACvB,IAAI,CAACqN,gBAAgB;YACnB,IAAI3B;YACJA,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEhD,IAAI,CAACD,kBAAkB;gBACrB,MAAM,IAAIlY;YACZ;YACA,MAAMsa,YACJpC,iBAAiBlT,OAAO,IAAIkT;YAE9B1L,SAAS,MAAM8N,UAAU;gBACvBtJ,SAASkH,iBAAiBjM,UAAU,IAAIiM;gBACxCrL,SAASmN;gBACTnP,MAAM;YACR;QACF,OAAO;YACL,MAAM,EAAE0P,GAAG,EAAE,GAAGpV,QAAQ;YAExBqH,SAAS,MAAM+N,IAAI;gBACjBlT,SAAS,IAAI,CAACA,OAAO;gBACrBsR,MAAMkB,eAAelB,IAAI;gBACzBC,OAAOiB,eAAejB,KAAK;gBAC3B4B,mBAAmBX;gBACnBhN,SAASmN;gBACTS,UAAU;gBACVC,WAAW1P,OAAO0P,SAAS;YAC7B;QACF;QAEA,IAAI,CAAC,IAAI,CAACxS,UAAU,CAACC,GAAG,EAAE;YACxBqE,OAAO4N,SAAS,CAAC3L,KAAK,CAAC,CAAClB;gBACtBC,QAAQD,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAEA;YAC1D;QACF;QAEA,IAAI,CAACf,QAAQ;YACX,IAAI,CAAC3E,SAAS,CAACmD,OAAO6B,OAAO,EAAE7B,OAAO8B,QAAQ,EAAE9B,OAAOc,MAAM;YAC7D,OAAO;gBAAE2B,UAAU;YAAK;QAC1B;QAEA,sDAAsD;QACtD,IAAIjB,OAAOM,QAAQ,CAACI,OAAO,CAACyN,GAAG,CAAC,eAAe;YAC7C,MAAMC,UAAUpO,OAAOM,QAAQ,CAACI,OAAO,CACpC2N,YAAY,GACZC,OAAO,CAAC,CAACC,sBACR7Y,mBAAmB6Y;YAGvB,2BAA2B;YAC3BvO,OAAOM,QAAQ,CAACI,OAAO,CAAC8N,MAAM,CAAC;YAE/B,mCAAmC;YACnC,KAAK,MAAMC,UAAUL,QAAS;gBAC5BpO,OAAOM,QAAQ,CAACI,OAAO,CAACgO,MAAM,CAAC,cAAcD;YAC/C;YAEA,+BAA+B;YAC/B5a,eAAe2K,OAAO6B,OAAO,EAAE,oBAAoB+N;QACrD;QAEA,OAAOpO;IACT;IAmHUqF,uBAA0C;YAKhD,kBACA;QALF,IAAI,IAAI,CAACsJ,sBAAsB,EAAE;YAC/B,OAAO,IAAI,CAACA,sBAAsB;QACpC;QACA,IACE,EAAA,mBAAA,IAAI,CAACjT,UAAU,qBAAf,iBAAiBC,GAAG,OACpB,sBAAA,IAAI,CAAC+G,aAAa,qBAAlB,oBAAoB/G,GAAG,KACvBzD,QAAQC,GAAG,CAACyW,QAAQ,KAAK,iBACzB1W,QAAQC,GAAG,CAAC0W,UAAU,KAAKra,wBAC3B;YACA,IAAI,CAACma,sBAAsB,GAAG;gBAC5BG,SAAS;gBACTC,QAAQ,CAAC;gBACT3M,eAAe,CAAC;gBAChB4M,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAevW,QAAQ,UAAUwW,WAAW,CAAC,IAAIhC,QAAQ,CAAC;oBAC1DiC,uBAAuBzW,QAAQ,UAC5BwW,WAAW,CAAC,IACZhC,QAAQ,CAAC;oBACZkC,0BAA0B1W,QAAQ,UAC/BwW,WAAW,CAAC,IACZhC,QAAQ,CAAC;gBACd;YACF;YACA,OAAO,IAAI,CAACwB,sBAAsB;QACpC;QAEA,IAAI,CAACA,sBAAsB,GAAGvX,aAC5B1D,KAAK,IAAI,CAACmH,OAAO,EAAE3G;QAGrB,OAAO,IAAI,CAACya,sBAAsB;IACpC;IAEUtM,oBAAyD;QACjE,OAAO1L,YAAYiR,KAAK,CAAChR,mBAAmByL,iBAAiB,EAAE;YAC7D,MAAMoJ,WAAWrU,aAAa1D,KAAK,IAAI,CAACmH,OAAO,EAAE1G;YAEjD,IAAI4R,WAAW0F,SAAS1F,QAAQ,IAAI;gBAClCC,aAAa,EAAE;gBACfsJ,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YAEA,IAAIrW,MAAMC,OAAO,CAAC4M,WAAW;gBAC3BA,WAAW;oBACTC,aAAa,EAAE;oBACfsJ,YAAYvJ;oBACZwJ,UAAU,EAAE;gBACd;YACF;YAEA,OAAO;gBAAE,GAAG9D,QAAQ;gBAAE1F;YAAS;QACjC;IACF;IAEUyJ,kBACRxV,GAAoB,EACpBE,SAAiC,EACjCuV,YAAsB,EACtB;YAEiBzV;QADjB,6BAA6B;QAC7B,MAAM0V,WAAW1V,EAAAA,+BAAAA,IAAI0G,OAAO,CAAC,oBAAoB,qBAAhC1G,6BAAkCqR,QAAQ,CAAC,YACxD,UACA;QAEJ,4DAA4D;QAC5D,MAAM1L,UACJ,IAAI,CAAC6H,aAAa,IAAI,IAAI,CAAC4F,IAAI,GAC3B,GAAGsC,SAAS,GAAG,EAAE,IAAI,CAAClI,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC4F,IAAI,GAAGpT,IAAImG,GAAG,EAAE,GAC5D,IAAI,CAAC7F,UAAU,CAAC+G,YAAY,CAACiG,eAAe,GAC1C,CAAC,QAAQ,EAAEtN,IAAI0G,OAAO,CAACiP,IAAI,IAAI,cAAc3V,IAAImG,GAAG,EAAE,GACtDnG,IAAImG,GAAG;QAEftM,eAAemG,KAAK,WAAW2F;QAC/B9L,eAAemG,KAAK,aAAa;YAAE,GAAGE,UAAUuB,KAAK;QAAC;QACtD5H,eAAemG,KAAK,gBAAgB0V;QAEpC,IAAI,CAACD,cAAc;YACjB5b,eAAemG,KAAK,gBAAgB/D,iBAAiB+D,IAAIwB,eAAe;QAC1E;IACF;IAEA,MAAgB+C,gBAAgBC,MAU/B,EAAoC;QACnC,IAAItG,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,wGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIuW;QAEJ,MAAM,EAAEnU,KAAK,EAAE4C,IAAI,EAAEN,KAAK,EAAE,GAAGS;QAE/B,IAAI,CAACT,OACH,MAAM,IAAI,CAAC+O,kBAAkB,CAAC;YAC5BzO;YACAI,UAAUD,OAAOC,QAAQ;YACzB0B,KAAK3B,OAAOxE,GAAG,CAACmG,GAAG;QACrB;QACFyP,WAAW,IAAI,CAAC5D,mBAAmB,CAAC;YAClC3N;YACAoB,YAAY;QACd;QAEA,IAAI,CAACmQ,UAAU;YACb,OAAO;QACT;QAEA,6DAA6D;QAC7D,MAAMC,oBAAoB/b,eAAe0K,OAAOxE,GAAG,EAAE;QACrD,MAAM8V,aAAa,IAAIC,IACrBjc,eAAe0K,OAAOxE,GAAG,EAAE,cAAc,KACzC;QAEF,MAAMgW,cAAcla,uBAAuB;YACzC,GAAG0K,OAAOyP,WAAW,CAACH,WAAWI,YAAY,CAAC;YAC9C,GAAGzU,KAAK;YACR,GAAG+C,OAAOA,MAAM;QAClB,GAAG2O,QAAQ;QAEX,IAAI0C,mBAAmB;YACrBrR,OAAOxE,GAAG,CAAC0G,OAAO,CAAC,gBAAgB,GAAG;QACxC;QACAoP,WAAWK,MAAM,GAAGH;QACpB,MAAM7P,MAAM2P,WAAW3C,QAAQ;QAE/B,IAAI,CAAChN,IAAI/F,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIf,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAE0U,GAAG,EAAE,GAAGpV,QAAQ;QACxB,MAAMqH,SAAS,MAAM+N,IAAI;YACvBlT,SAAS,IAAI,CAACA,OAAO;YACrBsR,MAAMyD,SAASzD,IAAI;YACnBC,OAAOwD,SAASxD,KAAK;YACrB4B,mBAAmB4B;YACnBvP,SAAS;gBACPK,SAASlC,OAAOxE,GAAG,CAAC0G,OAAO;gBAC3B4M,QAAQ9O,OAAOxE,GAAG,CAACsT,MAAM;gBACzBhT,YAAY;oBACVmT,UAAU,IAAI,CAACnT,UAAU,CAACmT,QAAQ;oBAClC7P,MAAM,IAAI,CAACtD,UAAU,CAACsD,IAAI;oBAC1B8P,eAAe,IAAI,CAACpT,UAAU,CAACoT,aAAa;gBAC9C;gBACAvN;gBACA9B,MAAM;oBACJ8N,MAAM3N,OAAOH,IAAI;oBACjB,GAAIG,OAAOA,MAAM,IAAI;wBAAEA,QAAQA,OAAOA,MAAM;oBAAC,CAAC;gBAChD;gBACA/D,MAAM3G,eAAe0K,OAAOxE,GAAG,EAAE;gBACjC2T,QAAQzW,uBAAuBsH,OAAOvE,GAAG,CAACkD,gBAAgB;gBAC1DyQ,WAAW,IAAI,CAACC,YAAY;YAC9B;YACAI,UAAU;YACVxG,SAASjJ,OAAOiJ,OAAO;YACvByG,WAAW1P,OAAO0P,SAAS;YAC3BhR,kBACE,AAACkT,WAAmBC,kBAAkB,IACtCvc,eAAe0K,OAAOxE,GAAG,EAAE;YAC7BsW,0BAA0Bxc,eACxB0K,OAAOxE,GAAG,EACV;QAEJ;QAEA,IAAIgG,OAAOuQ,YAAY,EAAE;YACvB/R,OAAOxE,GAAG,CAACuW,YAAY,GAAGvQ,OAAOuQ,YAAY;QAC/C;QAEA,IAAI,CAAC/R,OAAOvE,GAAG,CAACO,UAAU,IAAIgE,OAAOvE,GAAG,CAACO,UAAU,GAAG,KAAK;YACzDgE,OAAOvE,GAAG,CAACO,UAAU,GAAGwF,OAAOM,QAAQ,CAACM,MAAM;YAC9CpC,OAAOvE,GAAG,CAACuW,aAAa,GAAGxQ,OAAOM,QAAQ,CAACmQ,UAAU;QACvD;QAEA,8CAA8C;QAE9CzQ,OAAOM,QAAQ,CAACI,OAAO,CAACgQ,OAAO,CAAC,CAAClU,OAAO+D;YACtC,yDAAyD;YACzD,IAAIA,IAAIoQ,WAAW,OAAO,cAAc;gBACtC,qFAAqF;gBACrF,KAAK,MAAMlC,UAAU/Y,mBAAmB8G,OAAQ;oBAC9CgC,OAAOvE,GAAG,CAAC2W,YAAY,CAACrQ,KAAKkO;gBAC/B;YACF,OAAO;gBACLjQ,OAAOvE,GAAG,CAAC2W,YAAY,CAACrQ,KAAK/D;YAC/B;QACF;QAEA,MAAM,EAAEW,gBAAgB,EAAE,GAAGqB,OAAOvE,GAAG;QACvC,IAAI+F,OAAOM,QAAQ,CAAC7F,IAAI,EAAE;YACxB,MAAM1D,mBAAmBiJ,OAAOM,QAAQ,CAAC7F,IAAI,EAAE0C;QACjD,OAAO;YACLA,iBAAiB0D,GAAG;QACtB;QAEA,OAAOb;IACT;IAEA,IAAc+C,gBAAwB;QACpC,IAAI,IAAI,CAAC8N,cAAc,EAAE;YACvB,OAAO,IAAI,CAACA,cAAc;QAC5B;QACA,MAAM9N,gBAAgBrP,KAAK,IAAI,CAACmH,OAAO,EAAEvG;QACzC,IAAI,CAACuc,cAAc,GAAG9N;QACtB,OAAOA;IACT;IAEA,MAAgB+N,2BACdxH,IAAa,EAC6B;QAC1C,uEAAuE;QACvE,sBAAsB;QACtB,OAAO;IACT;IAEA,MAAgB3K,8BACd,GAAGoS,IAAqD,EACxD;QACA,MAAM,KAAK,CAACpS,iCAAiCoS;QAE7C,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAACrV,UAAU,CAACC,GAAG,EAAE;YACxB,IAAI,CAACwD,QAAQ,CAAC4R,IAAI,CAAC,EAAE;QACvB;IACF;IAEUC,cAAcC,QAA6B,EAAE;QACrD,IAAI,CAACnX,gBAAgB,CAACoX,GAAG,CAACD;IAC5B;IAEA,MAAME,QAAuB;QAC3B,MAAM,IAAI,CAACrX,gBAAgB,CAACsX,MAAM;IACpC;IAEUC,uBAAkC;QAC1C,IAAI,CAACC,iBAAiB,KAAK,IAAI,CAACC,uBAAuB;QACvD,OAAO,IAAI,CAACD,iBAAiB;IAC/B;IAEQC,0BAA0B;QAChC,IAAI,IAAI,CAAClX,WAAW,EAAE;YACpB,MAAM,qBAEL,CAFK,IAAI1C,eACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM6Z,UAAU,IAAI5Z,YAAY;YAAE6P,SAASzG,QAAQD,KAAK;QAAC;QAEzD,kEAAkE;QAClE,IAAI,CAACiQ,aAAa,CAAC,IAAMQ,QAAQC,QAAQ;QAEzC,OAAOD,QAAQ5D,SAAS;IAC1B;AACF"}