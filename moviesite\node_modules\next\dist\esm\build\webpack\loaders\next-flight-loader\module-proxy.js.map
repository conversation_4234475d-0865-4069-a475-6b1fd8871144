{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-flight-loader/module-proxy.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport { createClientModuleProxy } from 'react-server-dom-webpack/server.edge'\n\n// Re-assign to make it typed.\nexport const createProxy: (moduleId: string) => any = createClientModuleProxy\n"], "names": ["createClientModuleProxy", "createProxy"], "mappings": "AAAA,oDAAoD,GACpD,SAASA,uBAAuB,QAAQ,uCAAsC;AAE9E,8BAA8B;AAC9B,OAAO,MAAMC,cAAyCD,wBAAuB"}