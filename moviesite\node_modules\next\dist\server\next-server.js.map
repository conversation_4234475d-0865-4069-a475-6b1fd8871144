{"version": 3, "sources": ["../../src/server/next-server.ts"], "sourcesContent": ["import './node-environment'\nimport './require-hook'\nimport './node-polyfill-crypto'\n\nimport type { CacheFs } from '../shared/lib/utils'\nimport {\n  DecodeError,\n  PageNotFoundError,\n  MiddlewareNotFoundError,\n} from '../shared/lib/utils'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport type RenderResult from './render-result'\nimport type { FetchEventResult } from './web/types'\nimport type { PrerenderManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport type { NextParsedUrlQuery, NextUrlWithParsedQuery } from './request-meta'\nimport type { Params } from './request/params'\nimport type { MiddlewareRouteMatch } from '../shared/lib/router/utils/middleware-route-matcher'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { PagesAPIRouteModule } from './route-modules/pages-api/module'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { ParsedUrl } from '../shared/lib/router/utils/parse-url'\nimport type { CacheControl } from './lib/cache-control'\nimport type { WaitUntil } from './after/builtin-request-context'\n\nimport fs from 'fs'\nimport { join, resolve } from 'path'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { addRequestMeta, getRequestMeta } from './request-meta'\nimport {\n  PAGES_MANIFEST,\n  BUILD_ID_FILE,\n  MIDDLEWARE_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n  CLIENT_PUBLIC_FILES_PATH,\n  APP_PATHS_MANIFEST,\n  SERVER_DIRECTORY,\n  NEXT_FONT_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport { findDir } from '../lib/find-pages-dir'\nimport { NodeNextRequest, NodeNextResponse } from './base-http/node'\nimport { sendRenderResult } from './send-payload'\nimport { parseUrl } from '../shared/lib/router/utils/parse-url'\nimport * as Log from '../build/output/log'\n\nimport type {\n  Options,\n  FindComponentsResult,\n  MiddlewareRoutingItem,\n  RequestContext,\n  NormalizedRouteManifest,\n  LoadedRenderOpts,\n  RouteHandler,\n  NextEnabledDirectories,\n  BaseRequestHandler,\n} from './base-server'\nimport BaseServer, { NoFallbackError } from './base-server'\nimport { getMaybePagePath, getPagePath } from './require'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { loadComponents } from './load-components'\nimport type { LoadComponentsReturnType } from './load-components'\nimport isError, { getProperError } from '../lib/is-error'\nimport { splitCookiesString, toNodeOutgoingHttpHeaders } from './web/utils'\nimport { getMiddlewareRouteMatcher } from '../shared/lib/router/utils/middleware-route-matcher'\nimport { loadEnvConfig } from '@next/env'\nimport { urlQueryToSearchParams } from '../shared/lib/router/utils/querystring'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { getNextPathnameInfo } from '../shared/lib/router/utils/get-next-pathname-info'\nimport { getCloneableBody } from './body-streams'\nimport { checkIsOnDemandRevalidate } from './api-utils'\nimport ResponseCache, {\n  CachedRouteKind,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { IncrementalCache } from './lib/incremental-cache'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\n\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\n\nimport { isPagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { PagesAPIRouteMatch } from './route-matches/pages-api-route-match'\nimport type { MatchOptions } from './route-matcher-managers/route-matcher-manager'\nimport { INSTRUMENTATION_HOOK_FILENAME } from '../lib/constants'\nimport { BubbledError, getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { nodeFs } from './lib/node-fs-methods'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { pipeToNodeResponse } from './pipe-readable'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport { signalFromNodeResponse } from './web/spec-extension/adapters/next-request'\nimport { RouteModuleLoader } from './lib/module-loader/route-module-loader'\nimport { loadManifest } from './load-manifest'\nimport { lazyRenderAppPage } from './route-modules/app-page/module.render'\nimport { lazyRenderPagesPage } from './route-modules/pages/module.render'\nimport { interopDefault } from '../lib/interop-default'\nimport { formatDynamicImportPath } from '../lib/format-dynamic-import-path'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ServerOnInstrumentationRequestError } from './app-render/types'\nimport { RouteKind } from './route-kind'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { AwaiterOnce } from './after/awaiter'\nimport { AsyncCallbackSet } from './lib/async-callback-set'\nimport { initializeCacheHandlers, setCacheHandler } from './use-cache/handlers'\nimport type { UnwrapPromise } from '../lib/coalesced-function'\nimport { populateStaticEnv } from '../lib/static-env'\n\nexport * from './base-server'\n\ndeclare const __non_webpack_require__: NodeRequire\n\n// For module that can be both CJS or ESM\nconst dynamicImportEsmDefault = process.env.NEXT_MINIMAL\n  ? (id: string) =>\n      import(/* webpackIgnore: true */ id).then((mod) => mod.default || mod)\n  : (id: string) => import(id).then((mod) => mod.default || mod)\n\n// For module that will be compiled to CJS, e.g. instrument\nconst dynamicRequire = process.env.NEXT_MINIMAL\n  ? __non_webpack_require__\n  : require\n\nexport type NodeRequestHandler = BaseRequestHandler<\n  IncomingMessage | NodeNextRequest,\n  ServerResponse | NodeNextResponse\n>\n\ntype NodeRouteHandler = RouteHandler<NodeNextRequest, NodeNextResponse>\n\nconst MiddlewareMatcherCache = new WeakMap<\n  MiddlewareManifest['middleware'][string],\n  MiddlewareRouteMatch\n>()\n\nfunction getMiddlewareMatcher(\n  info: MiddlewareManifest['middleware'][string]\n): MiddlewareRouteMatch {\n  const stored = MiddlewareMatcherCache.get(info)\n  if (stored) {\n    return stored\n  }\n\n  if (!Array.isArray(info.matchers)) {\n    throw new Error(\n      `Invariant: invalid matchers for middleware ${JSON.stringify(info)}`\n    )\n  }\n\n  const matcher = getMiddlewareRouteMatcher(info.matchers)\n  MiddlewareMatcherCache.set(info, matcher)\n  return matcher\n}\n\nexport default class NextNodeServer extends BaseServer<\n  Options,\n  NodeNextRequest,\n  NodeNextResponse\n> {\n  protected middlewareManifestPath: string\n  private _serverDistDir: string | undefined\n  private imageResponseCache?: ResponseCache\n  private registeredInstrumentation: boolean = false\n  protected renderWorkersPromises?: Promise<void>\n  protected dynamicRoutes?: {\n    match: import('../shared/lib/router/utils/route-matcher').RouteMatchFn\n    page: string\n    re: RegExp\n  }[]\n  private routerServerHandler?: (\n    req: IncomingMessage,\n    res: ServerResponse\n  ) => void\n\n  protected cleanupListeners = new AsyncCallbackSet()\n  protected internalWaitUntil: WaitUntil | undefined\n  private isDev: boolean\n  private sriEnabled: boolean\n\n  constructor(options: Options) {\n    // Initialize super class\n    super(options)\n\n    const isDev = options.dev ?? false\n    this.isDev = isDev\n    this.sriEnabled = Boolean(options.conf.experimental?.sri?.algorithm)\n\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_CSS`.\n     */\n    if (this.renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n    if (this.renderOpts.nextScriptWorkers) {\n      process.env.__NEXT_SCRIPT_WORKERS = JSON.stringify(true)\n    }\n    process.env.NEXT_DEPLOYMENT_ID = this.nextConfig.deploymentId || ''\n\n    if (!this.minimalMode) {\n      this.imageResponseCache = new ResponseCache(this.minimalMode)\n    }\n\n    const { appDocumentPreloading } = this.nextConfig.experimental\n    const isDefaultEnabled = typeof appDocumentPreloading === 'undefined'\n\n    if (\n      !options.dev &&\n      (appDocumentPreloading === true ||\n        !(this.minimalMode && isDefaultEnabled))\n    ) {\n      // pre-warm _document and _app as these will be\n      // needed for most requests\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_document',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n      loadComponents({\n        distDir: this.distDir,\n        page: '/_app',\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    if (\n      !options.dev &&\n      !this.minimalMode &&\n      this.nextConfig.experimental.preloadEntriesOnStart\n    ) {\n      this.unstable_preloadEntries()\n    }\n\n    if (!options.dev) {\n      const { dynamicRoutes = [] } = this.getRoutesManifest() ?? {}\n      this.dynamicRoutes = dynamicRoutes.map((r) => {\n        // TODO: can we just re-use the regex from the manifest?\n        const regex = getRouteRegex(r.page)\n        const match = getRouteMatcher(regex)\n\n        return {\n          match,\n          page: r.page,\n          re: regex.re,\n        }\n      })\n    }\n\n    // ensure options are set when loadConfig isn't called\n    setHttpClientAndAgentOptions(this.nextConfig)\n\n    // Intercept fetch and other testmode apis.\n    if (this.serverOptions.experimentalTestProxy) {\n      process.env.NEXT_PRIVATE_TEST_PROXY = 'true'\n      const {\n        interceptTestApis,\n      } = require('next/dist/experimental/testmode/server')\n      interceptTestApis()\n    }\n\n    this.middlewareManifestPath = join(this.serverDistDir, MIDDLEWARE_MANIFEST)\n\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause a unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    if (!options.dev) {\n      this.prepare().catch((err) => {\n        console.error('Failed to prepare server', err)\n      })\n    }\n\n    // when using compile mode static env isn't inlined so we\n    // need to populate in normal runtime env\n    if (this.renderOpts.isExperimentalCompile) {\n      populateStaticEnv(this.nextConfig)\n    }\n  }\n\n  public async unstable_preloadEntries(): Promise<void> {\n    const appPathsManifest = this.getAppPathsManifest()\n    const pagesManifest = this.getPagesManifest()\n\n    await this.loadCustomCacheHandlers()\n\n    for (const page of Object.keys(pagesManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: false,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      }).catch(() => {})\n    }\n\n    for (const page of Object.keys(appPathsManifest || {})) {\n      await loadComponents({\n        distDir: this.distDir,\n        page,\n        isAppPath: true,\n        isDev: this.isDev,\n        sriEnabled: this.sriEnabled,\n      })\n        .then(async ({ ComponentMod }) => {\n          // we need to ensure fetch is patched before we require the page,\n          // otherwise if the fetch is patched by user code, we will be patching it\n          // too late and there won't be any caching behaviors\n          ComponentMod.patchFetch()\n\n          const webpackRequire = ComponentMod.__next_app__.require\n          if (webpackRequire?.m) {\n            for (const id of Object.keys(webpackRequire.m)) {\n              await webpackRequire(id)\n            }\n          }\n        })\n        .catch(() => {})\n    }\n  }\n\n  protected async handleUpgrade(): Promise<void> {\n    // The web server does not support web sockets, it's only used for HMR in\n    // development.\n  }\n\n  protected async loadInstrumentationModule() {\n    if (!this.serverOptions.dev) {\n      try {\n        this.instrumentation = await dynamicRequire(\n          resolve(\n            this.serverOptions.dir || '.',\n            this.serverOptions.conf.distDir!,\n            'server',\n            INSTRUMENTATION_HOOK_FILENAME\n          )\n        )\n      } catch (err: any) {\n        if (err.code !== 'MODULE_NOT_FOUND') {\n          throw new Error(\n            'An error occurred while loading the instrumentation hook',\n            { cause: err }\n          )\n        }\n      }\n    }\n    return this.instrumentation\n  }\n\n  protected async prepareImpl() {\n    await super.prepareImpl()\n    await this.runInstrumentationHookIfAvailable()\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    if (this.registeredInstrumentation) return\n    this.registeredInstrumentation = true\n    await this.instrumentation?.register?.()\n  }\n\n  protected loadEnvConfig({\n    dev,\n    forceReload,\n    silent,\n  }: {\n    dev: boolean\n    forceReload?: boolean\n    silent?: boolean\n  }) {\n    loadEnvConfig(\n      this.dir,\n      dev,\n      silent ? { info: () => {}, error: () => {} } : Log,\n      forceReload\n    )\n  }\n\n  private async loadCustomCacheHandlers() {\n    const { cacheHandlers } = this.nextConfig.experimental\n    if (!cacheHandlers) return\n\n    // If we've already initialized the cache handlers interface, don't do it\n    // again.\n    if (!initializeCacheHandlers()) return\n\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await dynamicImportEsmDefault(\n            formatDynamicImportPath(this.distDir, handler)\n          )\n        )\n      )\n    }\n  }\n\n  protected async getIncrementalCache({\n    requestHeaders,\n    requestProtocol,\n  }: {\n    requestHeaders: IncrementalCache['requestHeaders']\n    requestProtocol: 'http' | 'https'\n  }) {\n    const dev = !!this.renderOpts.dev\n    let CacheHandler: any\n    const { cacheHandler } = this.nextConfig\n\n    if (cacheHandler) {\n      CacheHandler = interopDefault(\n        await dynamicImportEsmDefault(\n          formatDynamicImportPath(this.distDir, cacheHandler)\n        )\n      )\n    }\n\n    await this.loadCustomCacheHandlers()\n\n    // incremental-cache is request specific\n    // although can have shared caches in module scope\n    // per-cache handler\n    return new IncrementalCache({\n      fs: this.getCacheFilesystem(),\n      dev,\n      requestHeaders,\n      requestProtocol,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      minimalMode: this.minimalMode,\n      serverDistDir: this.serverDistDir,\n      fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n      maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n      flushToDisk:\n        !this.minimalMode && this.nextConfig.experimental.isrFlushToDisk,\n      getPrerenderManifest: () => this.getPrerenderManifest(),\n      CurCacheHandler: CacheHandler,\n    })\n  }\n\n  protected getResponseCache() {\n    return new ResponseCache(this.minimalMode)\n  }\n\n  protected getPublicDir(): string {\n    return join(this.dir, CLIENT_PUBLIC_FILES_PATH)\n  }\n\n  protected getHasStaticDir(): boolean {\n    return fs.existsSync(join(this.dir, 'static'))\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return loadManifest(\n      join(this.serverDistDir, PAGES_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return loadManifest(\n      join(this.serverDistDir, APP_PATHS_MANIFEST)\n    ) as PagesManifest\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    if (!this.enabledDirectories.app) return []\n\n    const routesManifest = this.getRoutesManifest()\n    return (\n      routesManifest?.rewrites.beforeFiles\n        .filter(isInterceptionRouteRewrite)\n        .map((rewrite) => new RegExp(rewrite.regex)) ?? []\n    )\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    return !!getMaybePagePath(\n      pathname,\n      this.distDir,\n      this.nextConfig.i18n?.locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected getBuildId(): string {\n    const buildIdFile = join(this.distDir, BUILD_ID_FILE)\n    try {\n      return fs.readFileSync(buildIdFile, 'utf8').trim()\n    } catch (err: any) {\n      if (err.code === 'ENOENT') {\n        throw new Error(\n          `Could not find a production build in the '${this.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n        )\n      }\n\n      throw err\n    }\n  }\n\n  protected getEnabledDirectories(dev: boolean): NextEnabledDirectories {\n    const dir = dev ? this.dir : this.serverDistDir\n\n    return {\n      app: findDir(dir, 'app') ? true : false,\n      pages: findDir(dir, 'pages') ? true : false,\n    }\n  }\n\n  protected sendRenderResult(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    options: {\n      result: RenderResult\n      type: 'html' | 'json' | 'rsc'\n      generateEtags: boolean\n      poweredByHeader: boolean\n      cacheControl: CacheControl | undefined\n    }\n  ): Promise<void> {\n    return sendRenderResult({\n      req: req.originalRequest,\n      res: res.originalResponse,\n      result: options.result,\n      type: options.type,\n      generateEtags: options.generateEtags,\n      poweredByHeader: options.poweredByHeader,\n      cacheControl: options.cacheControl,\n    })\n  }\n\n  protected async runApi(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages()\n\n    for (const edgeFunctionsPage of edgeFunctionsPages) {\n      if (edgeFunctionsPage === match.definition.pathname) {\n        const handledAsEdgeFunction = await this.runEdgeFunction({\n          req,\n          res,\n          query,\n          params: match.params,\n          page: match.definition.pathname,\n          appPaths: null,\n        })\n\n        if (handledAsEdgeFunction) {\n          return true\n        }\n      }\n    }\n\n    // The module supports minimal mode, load the minimal module.\n    const module = await RouteModuleLoader.load<PagesAPIRouteModule>(\n      match.definition.filename\n    )\n\n    query = { ...query, ...match.params }\n\n    await module.render(req.originalRequest, res.originalResponse, {\n      previewProps: this.renderOpts.previewProps,\n      revalidate: this.revalidate.bind(this),\n      trustHostHeader: this.nextConfig.experimental.trustHostHeader,\n      allowedRevalidateHeaderKeys:\n        this.nextConfig.experimental.allowedRevalidateHeaderKeys,\n      hostname: this.fetchHostname,\n      minimalMode: this.minimalMode,\n      dev: this.renderOpts.dev === true,\n      query,\n      params: match.params,\n      page: match.definition.pathname,\n      onError: this.instrumentationOnRequestError.bind(this),\n      multiZoneDraftMode: this.nextConfig.experimental.multiZoneDraftMode,\n    })\n\n    return true\n  }\n\n  protected async renderHTML(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    return getTracer().trace(NextNodeServerSpan.renderHTML, async () =>\n      this.renderHTMLImpl(req, res, pathname, query, renderOpts)\n    )\n  }\n\n  private async renderHTMLImpl(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    pathname: string,\n    query: NextParsedUrlQuery,\n    renderOpts: LoadedRenderOpts\n  ): Promise<RenderResult> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Invariant: renderHTML should not be called in minimal mode'\n      )\n      // the `else` branch is needed for tree-shaking\n    } else {\n      // Due to the way we pass data by mutating `renderOpts`, we can't extend the\n      // object here but only updating its `nextFontManifest` field.\n      // https://github.com/vercel/next.js/blob/df7cbd904c3bd85f399d1ce90680c0ecf92d2752/packages/next/server/render.tsx#L947-L952\n      renderOpts.nextFontManifest = this.nextFontManifest\n\n      if (this.enabledDirectories.app && renderOpts.isAppPath) {\n        return lazyRenderAppPage(\n          req,\n          res,\n          pathname,\n          query,\n          // This code path does not service revalidations for unknown param\n          // shells. As a result, we don't need to pass in the unknown params.\n          null,\n          renderOpts,\n          this.getServerComponentsHmrCache(),\n          false,\n          {\n            buildId: this.buildId,\n          }\n        )\n      }\n\n      // TODO: re-enable this once we've refactored to use implicit matches\n      // throw new Error('Invariant: render should have used routeModule')\n\n      return lazyRenderPagesPage(\n        req.originalRequest,\n        res.originalResponse,\n        pathname,\n        query,\n        renderOpts,\n        {\n          buildId: this.buildId,\n          deploymentId: this.nextConfig.deploymentId,\n          customServer: this.serverOptions.customServer || undefined,\n        },\n        {\n          isFallback: false,\n          isDraftMode: renderOpts.isDraftMode,\n          developmentNotFoundSourcePage: getRequestMeta(\n            req,\n            'developmentNotFoundSourcePage'\n          ),\n        }\n      )\n    }\n  }\n\n  protected async imageOptimizer(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    paramsResult: import('./image-optimizer').ImageParamsResult,\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  ): Promise<{\n    buffer: Buffer\n    contentType: string\n    maxAge: number\n    upstreamEtag: string\n    etag: string\n  }> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: imageOptimizer should not be called in minimal mode'\n      )\n    } else {\n      const { imageOptimizer, fetchExternalImage, fetchInternalImage } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const handleInternalReq = async (\n        newReq: IncomingMessage,\n        newRes: ServerResponse\n      ) => {\n        if (newReq.url === req.url) {\n          throw new Error(`Invariant attempted to optimize _next/image itself`)\n        }\n\n        if (!this.routerServerHandler) {\n          throw new Error(`Invariant missing routerServerHandler`)\n        }\n\n        await this.routerServerHandler(newReq, newRes)\n        return\n      }\n\n      const { isAbsolute, href } = paramsResult\n\n      const imageUpstream = isAbsolute\n        ? await fetchExternalImage(href)\n        : await fetchInternalImage(\n            href,\n            req.originalRequest,\n            res.originalResponse,\n            handleInternalReq\n          )\n\n      return imageOptimizer(imageUpstream, paramsResult, this.nextConfig, {\n        isDev: this.renderOpts.dev,\n        previousCacheEntry,\n      })\n    }\n  }\n\n  protected getPagePath(pathname: string, locales?: string[]): string {\n    return getPagePath(\n      pathname,\n      this.distDir,\n      locales,\n      this.enabledDirectories.app\n    )\n  }\n\n  protected async renderPageComponent(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    bubbleNoFallback: boolean\n  ) {\n    const edgeFunctionsPages = this.getEdgeFunctionsPages() || []\n    if (edgeFunctionsPages.length) {\n      const appPaths = this.getOriginalAppPaths(ctx.pathname)\n      const isAppPath = Array.isArray(appPaths)\n\n      let page = ctx.pathname\n      if (isAppPath) {\n        // When it's an array, we need to pass all parallel routes to the loader.\n        page = appPaths[0]\n      }\n\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        if (edgeFunctionsPage === page) {\n          await this.runEdgeFunction({\n            req: ctx.req,\n            res: ctx.res,\n            query: ctx.query,\n            params: ctx.renderOpts.params,\n            page,\n            appPaths,\n          })\n          return null\n        }\n      }\n    }\n\n    return super.renderPageComponent(ctx, bubbleNoFallback)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    // The following parameters are used in the development server's\n    // implementation.\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    return getTracer().trace(\n      NextNodeServerSpan.findPageComponents,\n      {\n        spanName: 'resolve page components',\n        attributes: {\n          'next.route': isAppPath ? normalizeAppPath(page) : page,\n        },\n      },\n      () =>\n        this.findPageComponentsImpl({\n          locale,\n          page,\n          query,\n          params,\n          isAppPath,\n          url,\n        })\n    )\n  }\n\n  private async findPageComponentsImpl({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    url: _url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    const pagePaths: string[] = [page]\n    if (query.amp) {\n      // try serving a static AMP version first\n      pagePaths.unshift(\n        (isAppPath ? normalizeAppPath(page) : normalizePagePath(page)) + '.amp'\n      )\n    }\n\n    if (locale) {\n      pagePaths.unshift(\n        ...pagePaths.map((path) => `/${locale}${path === '/' ? '' : path}`)\n      )\n    }\n\n    for (const pagePath of pagePaths) {\n      try {\n        const components = await loadComponents({\n          distDir: this.distDir,\n          page: pagePath,\n          isAppPath,\n          isDev: this.isDev,\n          sriEnabled: this.sriEnabled,\n        })\n\n        if (\n          locale &&\n          typeof components.Component === 'string' &&\n          !pagePath.startsWith(`/${locale}/`) &&\n          pagePath !== `/${locale}`\n        ) {\n          // if loading an static HTML file the locale is required\n          // to be present since all HTML files are output under their locale\n          continue\n        }\n\n        return {\n          components,\n          query: {\n            ...(!this.renderOpts.isExperimentalCompile &&\n            components.getStaticProps\n              ? ({\n                  amp: query.amp,\n                } as NextParsedUrlQuery)\n              : query),\n            // For appDir params is excluded.\n            ...((isAppPath ? {} : params) || {}),\n          },\n        }\n      } catch (err) {\n        // we should only not throw if we failed to find the page\n        // in the pages-manifest\n        if (!(err instanceof PageNotFoundError)) {\n          throw err\n        }\n      }\n    }\n    return null\n  }\n\n  protected getNextFontManifest(): NextFontManifest | undefined {\n    return loadManifest(\n      join(this.distDir, 'server', NEXT_FONT_MANIFEST + '.json')\n    ) as NextFontManifest\n  }\n\n  protected handleNextImageRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    if (!parsedUrl.pathname || !parsedUrl.pathname.startsWith('/_next/image')) {\n      return false\n    }\n    // Ignore if its a middleware request\n    if (getRequestMeta(req, 'middlewareInvoke')) {\n      return false\n    }\n\n    if (\n      this.minimalMode ||\n      this.nextConfig.output === 'export' ||\n      process.env.NEXT_MINIMAL\n    ) {\n      res.statusCode = 400\n      res.body('Bad Request').send()\n      return true\n      // the `else` branch is needed for tree-shaking\n    } else {\n      const { ImageOptimizerCache } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      const imageOptimizerCache = new ImageOptimizerCache({\n        distDir: this.distDir,\n        nextConfig: this.nextConfig,\n      })\n\n      const { sendResponse, ImageError } =\n        require('./image-optimizer') as typeof import('./image-optimizer')\n\n      if (!this.imageResponseCache) {\n        throw new Error('invariant image optimizer cache was not initialized')\n      }\n      const imagesConfig = this.nextConfig.images\n\n      if (imagesConfig.loader !== 'default' || imagesConfig.unoptimized) {\n        await this.render404(req, res)\n        return true\n      }\n\n      const paramsResult = ImageOptimizerCache.validateParams(\n        req.originalRequest,\n        parsedUrl.query,\n        this.nextConfig,\n        !!this.renderOpts.dev\n      )\n\n      if ('errorMessage' in paramsResult) {\n        res.statusCode = 400\n        res.body(paramsResult.errorMessage).send()\n        return true\n      }\n\n      const cacheKey = ImageOptimizerCache.getCacheKey(paramsResult)\n\n      try {\n        const { getExtension } =\n          require('./serve-static') as typeof import('./serve-static')\n        const cacheEntry = await this.imageResponseCache.get(\n          cacheKey,\n          async ({ previousCacheEntry }) => {\n            const { buffer, contentType, maxAge, upstreamEtag, etag } =\n              await this.imageOptimizer(\n                req,\n                res,\n                paramsResult,\n                previousCacheEntry\n              )\n\n            return {\n              value: {\n                kind: CachedRouteKind.IMAGE,\n                buffer,\n                etag,\n                extension: getExtension(contentType) as string,\n                upstreamEtag,\n              },\n              isFallback: false,\n              cacheControl: { revalidate: maxAge, expire: undefined },\n            }\n          },\n          {\n            routeKind: RouteKind.IMAGE,\n            incrementalCache: imageOptimizerCache,\n            isFallback: false,\n          }\n        )\n\n        if (cacheEntry?.value?.kind !== CachedRouteKind.IMAGE) {\n          throw new Error(\n            'invariant did not get entry from image response cache'\n          )\n        }\n\n        sendResponse(\n          req.originalRequest,\n          res.originalResponse,\n          paramsResult.href,\n          cacheEntry.value.extension,\n          cacheEntry.value.buffer,\n          cacheEntry.value.etag,\n          paramsResult.isStatic,\n          cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT',\n          imagesConfig,\n          cacheEntry.cacheControl?.revalidate || 0,\n          Boolean(this.renderOpts.dev)\n        )\n        return true\n      } catch (err) {\n        if (err instanceof ImageError) {\n          res.statusCode = err.statusCode\n          res.body(err.message).send()\n          return true\n        }\n        throw err\n      }\n    }\n  }\n\n  protected handleCatchallRenderRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsedUrl\n  ) => {\n    let { pathname, query } = parsedUrl\n    if (!pathname) {\n      throw new Error('Invariant: pathname is undefined')\n    }\n\n    // This is a catch-all route, there should be no fallbacks so mark it as\n    // such.\n    addRequestMeta(req, 'bubbleNoFallback', true)\n\n    try {\n      // next.js core assumes page path without trailing slash\n      pathname = removeTrailingSlash(pathname)\n\n      const options: MatchOptions = {\n        i18n: this.i18nProvider?.fromRequest(req, pathname),\n      }\n      const match = await this.matchers.match(pathname, options)\n\n      // If we don't have a match, try to render it anyways.\n      if (!match) {\n        await this.render(req, res, pathname, query, parsedUrl, true)\n\n        return true\n      }\n\n      // Add the match to the request so we don't have to re-run the matcher\n      // for the same request.\n      addRequestMeta(req, 'match', match)\n\n      // TODO-APP: move this to a route handler\n      const edgeFunctionsPages = this.getEdgeFunctionsPages()\n      for (const edgeFunctionsPage of edgeFunctionsPages) {\n        // If the page doesn't match the edge function page, skip it.\n        if (edgeFunctionsPage !== match.definition.page) continue\n\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n        delete query[NEXT_RSC_UNION_QUERY]\n\n        // If we handled the request, we can return early.\n        // For api routes edge runtime\n        try {\n          const handled = await this.runEdgeFunction({\n            req,\n            res,\n            query,\n            params: match.params,\n            page: match.definition.page,\n            match,\n            appPaths: null,\n          })\n          if (handled) return true\n        } catch (apiError) {\n          await this.instrumentationOnRequestError(apiError, req, {\n            routePath: match.definition.page,\n            routerKind: 'Pages Router',\n            routeType: 'route',\n            // Edge runtime does not support ISR\n            revalidateReason: undefined,\n          })\n          throw apiError\n        }\n      }\n\n      // If the route was detected as being a Pages API route, then handle\n      // it.\n      // TODO: move this behavior into a route handler.\n      if (isPagesAPIRouteMatch(match)) {\n        if (this.nextConfig.output === 'export') {\n          await this.render404(req, res, parsedUrl)\n          return true\n        }\n\n        const handled = await this.handleApiRequest(req, res, query, match)\n        if (handled) return true\n      }\n\n      await this.render(req, res, pathname, query, parsedUrl, true)\n\n      return true\n    } catch (err: any) {\n      if (err instanceof NoFallbackError) {\n        throw err\n      }\n\n      try {\n        if (this.renderOpts.dev) {\n          const { formatServerError } =\n            require('../lib/format-server-error') as typeof import('../lib/format-server-error')\n          formatServerError(err)\n          this.logErrorWithOriginalStack(err)\n        } else {\n          this.logError(err)\n        }\n        res.statusCode = 500\n        await this.renderError(err, req, res, pathname, query)\n        return true\n      } catch {}\n\n      throw err\n    }\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected logErrorWithOriginalStack(\n    _err?: unknown,\n    _type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    throw new Error(\n      'Invariant: logErrorWithOriginalStack can only be called on the development server'\n    )\n  }\n\n  // Used in development only, overloaded in next-dev-server\n  protected async ensurePage(_opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    match?: RouteMatch\n    url?: string\n  }): Promise<void> {\n    throw new Error(\n      'Invariant: ensurePage can only be called on the development server'\n    )\n  }\n\n  /**\n   * Resolves `API` request, in development builds on demand\n   * @param req http request\n   * @param res http response\n   * @param pathname path of request\n   */\n  protected async handleApiRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    query: ParsedUrlQuery,\n    match: PagesAPIRouteMatch\n  ): Promise<boolean> {\n    return this.runApi(req, res, query, match)\n  }\n\n  protected getCacheFilesystem(): CacheFs {\n    return nodeFs\n  }\n\n  protected normalizeReq(\n    req: NodeNextRequest | IncomingMessage\n  ): NodeNextRequest {\n    return !(req instanceof NodeNextRequest) ? new NodeNextRequest(req) : req\n  }\n\n  protected normalizeRes(\n    res: NodeNextResponse | ServerResponse\n  ): NodeNextResponse {\n    return !(res instanceof NodeNextResponse) ? new NodeNextResponse(res) : res\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = this.makeRequestHandler()\n    if (this.serverOptions.experimentalTestProxy) {\n      const {\n        wrapRequestHandlerNode,\n      } = require('next/dist/experimental/testmode/server')\n      return wrapRequestHandlerNode(handler)\n    }\n    return handler\n  }\n\n  private makeRequestHandler(): NodeRequestHandler {\n    // This is just optimization to fire prepare as soon as possible. It will be\n    // properly awaited later. We add the catch here to ensure that it does not\n    // cause an unhandled promise rejection. The promise rejection will be\n    // handled later on via the `await` when the request handler is called.\n    this.prepare().catch((err) => {\n      console.error('Failed to prepare server', err)\n    })\n\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) =>\n      handler(this.normalizeReq(req), this.normalizeRes(res), parsedUrl)\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts,\n  }: {\n    urlPath: string\n    revalidateHeaders: { [key: string]: string | string[] }\n    opts: { unstable_onlyGenerated?: boolean }\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    const handler = this.getRequestHandler()\n    await handler(\n      new NodeNextRequest(mocked.req),\n      new NodeNextResponse(mocked.res)\n    )\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      mocked.res.statusCode !== 200 &&\n      !(mocked.res.statusCode === 404 && opts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n  }\n\n  public async render(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    parsedUrl?: NextUrlWithParsedQuery,\n    internal = false\n  ): Promise<void> {\n    return super.render(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      parsedUrl,\n      internal\n    )\n  }\n\n  public async renderToHTML(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderToHTML(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  protected async renderErrorToResponseImpl(\n    ctx: RequestContext<NodeNextRequest, NodeNextResponse>,\n    err: Error | null\n  ) {\n    const { req, res, query } = ctx\n    const is404 = res.statusCode === 404\n\n    if (is404 && this.enabledDirectories.app) {\n      if (this.renderOpts.dev) {\n        await this.ensurePage({\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          clientOnly: false,\n          url: req.url,\n        }).catch(() => {})\n      }\n\n      if (\n        this.getEdgeFunctionsPages().includes(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      ) {\n        await this.runEdgeFunction({\n          req,\n          res,\n          query: query || {},\n          params: {},\n          page: UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n          appPaths: null,\n        })\n        return null\n      }\n    }\n    return super.renderErrorToResponseImpl(ctx, err)\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: NextParsedUrlQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.renderError(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query,\n      setHeaders\n    )\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    pathname: string,\n    query?: ParsedUrlQuery\n  ): Promise<string | null> {\n    return super.renderErrorToHTML(\n      err,\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      pathname,\n      query\n    )\n  }\n\n  public async render404(\n    req: NodeNextRequest | IncomingMessage,\n    res: NodeNextResponse | ServerResponse,\n    parsedUrl?: NextUrlWithParsedQuery,\n    setHeaders?: boolean\n  ): Promise<void> {\n    return super.render404(\n      this.normalizeReq(req),\n      this.normalizeRes(res),\n      parsedUrl,\n      setHeaders\n    )\n  }\n\n  protected getMiddlewareManifest(): MiddlewareManifest | null {\n    if (this.minimalMode) {\n      return null\n    } else {\n      const manifest: MiddlewareManifest = require(this.middlewareManifestPath)\n      return manifest\n    }\n  }\n\n  /** Returns the middleware routing item if there is one. */\n  protected async getMiddleware(): Promise<MiddlewareRoutingItem | undefined> {\n    const manifest = this.getMiddlewareManifest()\n    const middleware = manifest?.middleware?.['/']\n    if (!middleware) {\n      const middlewareModule = await this.loadNodeMiddleware()\n\n      if (middlewareModule) {\n        return {\n          match: getMiddlewareRouteMatcher(\n            middlewareModule.config?.matchers || [\n              { regexp: '.*', originalSource: '/:path*' },\n            ]\n          ),\n          page: '/',\n        }\n      }\n\n      return\n    }\n\n    return {\n      match: getMiddlewareMatcher(middleware),\n      page: '/',\n    }\n  }\n\n  protected getEdgeFunctionsPages(): string[] {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return []\n    }\n\n    return Object.keys(manifest.functions)\n  }\n\n  /**\n   * Get information for the edge function located in the provided page\n   * folder. If the edge function info can't be found it will throw\n   * an error.\n   */\n  protected getEdgeFunctionInfo(params: {\n    page: string\n    /** Whether we should look for a middleware or not */\n    middleware: boolean\n  }): {\n    name: string\n    paths: string[]\n    wasm: { filePath: string; name: string }[]\n    env: { [key: string]: string }\n    assets?: { filePath: string; name: string }[]\n  } | null {\n    const manifest = this.getMiddlewareManifest()\n    if (!manifest) {\n      return null\n    }\n\n    let foundPage: string\n\n    try {\n      foundPage = denormalizePagePath(normalizePagePath(params.page))\n    } catch (err) {\n      return null\n    }\n\n    let pageInfo = params.middleware\n      ? manifest.middleware[foundPage]\n      : manifest.functions[foundPage]\n\n    if (!pageInfo) {\n      if (!params.middleware) {\n        throw new PageNotFoundError(foundPage)\n      }\n      return null\n    }\n\n    return {\n      name: pageInfo.name,\n      paths: pageInfo.files.map((file) => join(this.distDir, file)),\n      wasm: (pageInfo.wasm ?? []).map((binding) => ({\n        ...binding,\n        filePath: join(this.distDir, binding.filePath),\n      })),\n      assets:\n        pageInfo.assets &&\n        pageInfo.assets.map((binding) => {\n          return {\n            ...binding,\n            filePath: join(this.distDir, binding.filePath),\n          }\n        }),\n      env: pageInfo.env,\n    }\n  }\n\n  private async loadNodeMiddleware() {\n    if (!this.nextConfig.experimental.nodeMiddleware) {\n      return\n    }\n\n    try {\n      const functionsConfig = this.renderOpts.dev\n        ? {}\n        : require(join(this.distDir, 'server', FUNCTIONS_CONFIG_MANIFEST))\n\n      if (this.renderOpts.dev || functionsConfig?.functions?.['/_middleware']) {\n        // if used with top level await, this will be a promise\n        return require(join(this.distDir, 'server', 'middleware.js'))\n      }\n    } catch (err) {\n      if (\n        isError(err) &&\n        err.code !== 'ENOENT' &&\n        err.code !== 'MODULE_NOT_FOUND'\n      ) {\n        throw err\n      }\n    }\n  }\n\n  /**\n   * Checks if a middleware exists. This method is useful for the development\n   * server where we need to check the filesystem. Here we just check the\n   * middleware manifest.\n   */\n  protected async hasMiddleware(pathname: string): Promise<boolean> {\n    const info = this.getEdgeFunctionInfo({ page: pathname, middleware: true })\n    const nodeMiddleware = await this.loadNodeMiddleware()\n\n    if (!info && nodeMiddleware) {\n      return true\n    }\n    return Boolean(info && info.paths.length > 0)\n  }\n\n  /**\n   * A placeholder for a function to be defined in the development server.\n   * It will make sure that the root middleware or an edge function has been compiled\n   * so that we can run it.\n   */\n  protected async ensureMiddleware(_url?: string) {}\n  protected async ensureEdgeFunction(_params: {\n    page: string\n    appPaths: string[] | null\n    url?: string\n  }) {}\n\n  /**\n   * This method gets all middleware matchers and execute them when the request\n   * matches. It will make sure that each middleware exists and is compiled and\n   * ready to be invoked. The development server will decorate it to add warns\n   * and errors with rich traces.\n   */\n  protected async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    onWarning?: (warning: Error) => void\n  }) {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'invariant: runMiddleware should not be called in minimal mode'\n      )\n    }\n\n    // Middleware is skipped for on-demand revalidate requests\n    if (\n      checkIsOnDemandRevalidate(params.request, this.renderOpts.previewProps)\n        .isOnDemandRevalidate\n    ) {\n      return {\n        response: new Response(null, { headers: { 'x-middleware-next': '1' } }),\n      } as FetchEventResult\n    }\n\n    let url: string\n\n    if (this.nextConfig.skipMiddlewareUrlNormalize) {\n      url = getRequestMeta(params.request, 'initURL')!\n    } else {\n      // For middleware to \"fetch\" we must always provide an absolute URL\n      const query = urlQueryToSearchParams(params.parsed.query).toString()\n      const locale = getRequestMeta(params.request, 'locale')\n\n      url = `${getRequestMeta(params.request, 'initProtocol')}://${\n        this.fetchHostname || 'localhost'\n      }:${this.port}${locale ? `/${locale}` : ''}${params.parsed.pathname}${\n        query ? `?${query}` : ''\n      }`\n    }\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const page: {\n      name?: string\n      params?: { [key: string]: string | string[] }\n    } = {}\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return { finished: false }\n    }\n    if (!(await this.hasMiddleware(middleware.page))) {\n      return { finished: false }\n    }\n\n    await this.ensureMiddleware(params.request.url)\n    const middlewareInfo = this.getEdgeFunctionInfo({\n      page: middleware.page,\n      middleware: true,\n    })\n\n    const method = (params.request.method || 'GET').toUpperCase()\n    const requestData = {\n      headers: params.request.headers,\n      method,\n      nextConfig: {\n        basePath: this.nextConfig.basePath,\n        i18n: this.nextConfig.i18n,\n        trailingSlash: this.nextConfig.trailingSlash,\n        experimental: this.nextConfig.experimental,\n      },\n      url: url,\n      page,\n      body:\n        method !== 'GET' && method !== 'HEAD'\n          ? (getRequestMeta(params.request, 'clonableBody') as any)\n          : undefined,\n\n      signal: signalFromNodeResponse(params.response.originalResponse),\n      waitUntil: this.getWaitUntil(),\n    }\n    let result:\n      | UnwrapPromise<ReturnType<typeof import('./web/sandbox').run>>\n      | undefined\n\n    // if no middleware info check for Node.js middleware\n    // this is not in the middleware-manifest as that historically\n    // has only included edge-functions, we need to do a breaking\n    // version bump for that manifest to write this info there if\n    // we decide we want to\n    if (!middlewareInfo) {\n      let middlewareModule\n      middlewareModule = await this.loadNodeMiddleware()\n\n      if (!middlewareModule) {\n        throw new MiddlewareNotFoundError()\n      }\n      const adapterFn: typeof import('./web/adapter').adapter =\n        middlewareModule.default || middlewareModule\n\n      result = await adapterFn({\n        handler: middlewareModule.middleware || middlewareModule,\n        request: requestData,\n        page: 'middleware',\n      })\n    } else {\n      const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n\n      result = await run({\n        distDir: this.distDir,\n        name: middlewareInfo.name,\n        paths: middlewareInfo.paths,\n        edgeFunctionEntry: middlewareInfo,\n        request: requestData,\n        useCache: true,\n        onWarning: params.onWarning,\n      })\n    }\n\n    if (!this.renderOpts.dev) {\n      result.waitUntil.catch((error) => {\n        console.error(`Uncaught: middleware waitUntil errored`, error)\n      })\n    }\n\n    if (!result) {\n      this.render404(params.request, params.response, params.parsed)\n      return { finished: true }\n    }\n\n    // Split compound (comma-separated) set-cookie headers\n    if (result.response.headers.has('set-cookie')) {\n      const cookies = result.response.headers\n        .getSetCookie()\n        .flatMap((maybeCompoundCookie) =>\n          splitCookiesString(maybeCompoundCookie)\n        )\n\n      // Clear existing header(s)\n      result.response.headers.delete('set-cookie')\n\n      // Append each cookie individually.\n      for (const cookie of cookies) {\n        result.response.headers.append('set-cookie', cookie)\n      }\n\n      // Add cookies to request meta.\n      addRequestMeta(params.request, 'middlewareCookie', cookies)\n    }\n\n    return result\n  }\n\n  protected handleCatchallMiddlewareRequest: NodeRouteHandler = async (\n    req,\n    res,\n    parsed\n  ) => {\n    const isMiddlewareInvoke = getRequestMeta(req, 'middlewareInvoke')\n\n    if (!isMiddlewareInvoke) {\n      return false\n    }\n\n    const handleFinished = () => {\n      addRequestMeta(req, 'middlewareInvoke', true)\n      res.body('').send()\n      return true\n    }\n\n    const middleware = await this.getMiddleware()\n    if (!middleware) {\n      return handleFinished()\n    }\n\n    const initUrl = getRequestMeta(req, 'initURL')!\n    const parsedUrl = parseUrl(initUrl)\n    const pathnameInfo = getNextPathnameInfo(parsedUrl.pathname, {\n      nextConfig: this.nextConfig,\n      i18nProvider: this.i18nProvider,\n    })\n\n    parsedUrl.pathname = pathnameInfo.pathname\n    const normalizedPathname = removeTrailingSlash(parsed.pathname || '')\n    let maybeDecodedPathname = normalizedPathname\n\n    try {\n      maybeDecodedPathname = decodeURIComponent(normalizedPathname)\n    } catch {\n      /* non-fatal we can't decode so can't match it */\n    }\n\n    if (\n      !(\n        middleware.match(normalizedPathname, req, parsedUrl.query) ||\n        middleware.match(maybeDecodedPathname, req, parsedUrl.query)\n      )\n    ) {\n      return handleFinished()\n    }\n\n    let result: Awaited<\n      ReturnType<typeof NextNodeServer.prototype.runMiddleware>\n    >\n    let bubblingResult = false\n\n    try {\n      await this.ensureMiddleware(req.url)\n\n      result = await this.runMiddleware({\n        request: req,\n        response: res,\n        parsedUrl: parsedUrl,\n        parsed: parsed,\n      })\n\n      if ('response' in result) {\n        if (isMiddlewareInvoke) {\n          bubblingResult = true\n          throw new BubbledError(true, result)\n        }\n\n        for (const [key, value] of Object.entries(\n          toNodeOutgoingHttpHeaders(result.response.headers)\n        )) {\n          if (key !== 'content-encoding' && value !== undefined) {\n            res.setHeader(key, value as string | string[])\n          }\n        }\n        res.statusCode = result.response.status\n\n        const { originalResponse } = res\n        if (result.response.body) {\n          await pipeToNodeResponse(result.response.body, originalResponse)\n        } else {\n          originalResponse.end()\n        }\n        return true\n      }\n    } catch (err: unknown) {\n      if (bubblingResult) {\n        throw err\n      }\n\n      if (isError(err) && err.code === 'ENOENT') {\n        await this.render404(req, res, parsed)\n        return true\n      }\n\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        await this.renderError(err, req, res, parsed.pathname || '')\n        return true\n      }\n\n      const error = getProperError(err)\n      console.error(error)\n      res.statusCode = 500\n      await this.renderError(error, req, res, parsed.pathname || '')\n      return true\n    }\n\n    return result.finished\n  }\n\n  private _cachedPreviewManifest: PrerenderManifest | undefined\n  protected getPrerenderManifest(): PrerenderManifest {\n    if (this._cachedPreviewManifest) {\n      return this._cachedPreviewManifest\n    }\n    if (\n      this.renderOpts?.dev ||\n      this.serverOptions?.dev ||\n      process.env.NODE_ENV === 'development' ||\n      process.env.NEXT_PHASE === PHASE_PRODUCTION_BUILD\n    ) {\n      this._cachedPreviewManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: {\n          previewModeId: require('crypto').randomBytes(16).toString('hex'),\n          previewModeSigningKey: require('crypto')\n            .randomBytes(32)\n            .toString('hex'),\n          previewModeEncryptionKey: require('crypto')\n            .randomBytes(32)\n            .toString('hex'),\n        },\n      }\n      return this._cachedPreviewManifest\n    }\n\n    this._cachedPreviewManifest = loadManifest(\n      join(this.distDir, PRERENDER_MANIFEST)\n    ) as PrerenderManifest\n\n    return this._cachedPreviewManifest\n  }\n\n  protected getRoutesManifest(): NormalizedRouteManifest | undefined {\n    return getTracer().trace(NextNodeServerSpan.getRoutesManifest, () => {\n      const manifest = loadManifest(join(this.distDir, ROUTES_MANIFEST)) as any\n\n      let rewrites = manifest.rewrites ?? {\n        beforeFiles: [],\n        afterFiles: [],\n        fallback: [],\n      }\n\n      if (Array.isArray(rewrites)) {\n        rewrites = {\n          beforeFiles: [],\n          afterFiles: rewrites,\n          fallback: [],\n        }\n      }\n\n      return { ...manifest, rewrites }\n    })\n  }\n\n  protected attachRequestMeta(\n    req: NodeNextRequest,\n    parsedUrl: NextUrlWithParsedQuery,\n    isUpgradeReq?: boolean\n  ) {\n    // Injected in base-server.ts\n    const protocol = req.headers['x-forwarded-proto']?.includes('https')\n      ? 'https'\n      : 'http'\n\n    // When there are hostname and port we build an absolute URL\n    const initUrl =\n      this.fetchHostname && this.port\n        ? `${protocol}://${this.fetchHostname}:${this.port}${req.url}`\n        : this.nextConfig.experimental.trustHostHeader\n          ? `https://${req.headers.host || 'localhost'}${req.url}`\n          : req.url\n\n    addRequestMeta(req, 'initURL', initUrl)\n    addRequestMeta(req, 'initQuery', { ...parsedUrl.query })\n    addRequestMeta(req, 'initProtocol', protocol)\n\n    if (!isUpgradeReq) {\n      addRequestMeta(req, 'clonableBody', getCloneableBody(req.originalRequest))\n    }\n  }\n\n  protected async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    match?: RouteMatch\n    onError?: (err: unknown) => void\n    onWarning?: (warning: Error) => void\n  }): Promise<FetchEventResult | null> {\n    if (process.env.NEXT_MINIMAL) {\n      throw new Error(\n        'Middleware is not supported in minimal mode. Please remove the `NEXT_MINIMAL` environment variable.'\n      )\n    }\n    let edgeInfo: ReturnType<typeof this.getEdgeFunctionInfo> | undefined\n\n    const { query, page, match } = params\n\n    if (!match)\n      await this.ensureEdgeFunction({\n        page,\n        appPaths: params.appPaths,\n        url: params.req.url,\n      })\n    edgeInfo = this.getEdgeFunctionInfo({\n      page,\n      middleware: false,\n    })\n\n    if (!edgeInfo) {\n      return null\n    }\n\n    // For edge to \"fetch\" we must always provide an absolute URL\n    const isNextDataRequest = getRequestMeta(params.req, 'isNextDataReq')\n    const initialUrl = new URL(\n      getRequestMeta(params.req, 'initURL') || '/',\n      'http://n'\n    )\n    const queryString = urlQueryToSearchParams({\n      ...Object.fromEntries(initialUrl.searchParams),\n      ...query,\n      ...params.params,\n    }).toString()\n\n    if (isNextDataRequest) {\n      params.req.headers['x-nextjs-data'] = '1'\n    }\n    initialUrl.search = queryString\n    const url = initialUrl.toString()\n\n    if (!url.startsWith('http')) {\n      throw new Error(\n        'To use middleware you must provide a `hostname` and `port` to the Next.js Server'\n      )\n    }\n\n    const { run } = require('./web/sandbox') as typeof import('./web/sandbox')\n    const result = await run({\n      distDir: this.distDir,\n      name: edgeInfo.name,\n      paths: edgeInfo.paths,\n      edgeFunctionEntry: edgeInfo,\n      request: {\n        headers: params.req.headers,\n        method: params.req.method,\n        nextConfig: {\n          basePath: this.nextConfig.basePath,\n          i18n: this.nextConfig.i18n,\n          trailingSlash: this.nextConfig.trailingSlash,\n        },\n        url,\n        page: {\n          name: params.page,\n          ...(params.params && { params: params.params }),\n        },\n        body: getRequestMeta(params.req, 'clonableBody'),\n        signal: signalFromNodeResponse(params.res.originalResponse),\n        waitUntil: this.getWaitUntil(),\n      },\n      useCache: true,\n      onError: params.onError,\n      onWarning: params.onWarning,\n      incrementalCache:\n        (globalThis as any).__incrementalCache ||\n        getRequestMeta(params.req, 'incrementalCache'),\n      serverComponentsHmrCache: getRequestMeta(\n        params.req,\n        'serverComponentsHmrCache'\n      ),\n    })\n\n    if (result.fetchMetrics) {\n      params.req.fetchMetrics = result.fetchMetrics\n    }\n\n    if (!params.res.statusCode || params.res.statusCode < 400) {\n      params.res.statusCode = result.response.status\n      params.res.statusMessage = result.response.statusText\n    }\n\n    // TODO: (wyattjoh) investigate improving this\n\n    result.response.headers.forEach((value, key) => {\n      // The append handling is special cased for `set-cookie`.\n      if (key.toLowerCase() === 'set-cookie') {\n        // TODO: (wyattjoh) replace with native response iteration when we can upgrade undici\n        for (const cookie of splitCookiesString(value)) {\n          params.res.appendHeader(key, cookie)\n        }\n      } else {\n        params.res.appendHeader(key, value)\n      }\n    })\n\n    const { originalResponse } = params.res\n    if (result.response.body) {\n      await pipeToNodeResponse(result.response.body, originalResponse)\n    } else {\n      originalResponse.end()\n    }\n\n    return result\n  }\n\n  protected get serverDistDir(): string {\n    if (this._serverDistDir) {\n      return this._serverDistDir\n    }\n    const serverDistDir = join(this.distDir, SERVER_DIRECTORY)\n    this._serverDistDir = serverDistDir\n    return serverDistDir\n  }\n\n  protected async getFallbackErrorComponents(\n    _url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    // Not implemented for production use cases, this is implemented on the\n    // development server.\n    return null\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    // For Node.js runtime production logs, in dev it will be overridden by next-dev-server\n    if (!this.renderOpts.dev) {\n      this.logError(args[0] as Error)\n    }\n  }\n\n  protected onServerClose(listener: () => Promise<void>) {\n    this.cleanupListeners.add(listener)\n  }\n\n  async close(): Promise<void> {\n    await this.cleanupListeners.runAll()\n  }\n\n  protected getInternalWaitUntil(): WaitUntil {\n    this.internalWaitUntil ??= this.createInternalWaitUntil()\n    return this.internalWaitUntil\n  }\n\n  private createInternalWaitUntil() {\n    if (this.minimalMode) {\n      throw new InvariantError(\n        'createInternalWaitUntil should never be called in minimal mode'\n      )\n    }\n\n    const awaiter = new AwaiterOnce({ onError: console.error })\n\n    // TODO(after): warn if the process exits before these are awaited\n    this.onServerClose(() => awaiter.awaiting())\n\n    return awaiter.waitUntil\n  }\n}\n"], "names": ["NextNodeServer", "dynamicImportEsmDefault", "process", "env", "NEXT_MINIMAL", "id", "then", "mod", "default", "dynamicRequire", "__non_webpack_require__", "require", "MiddlewareMatcherCache", "WeakMap", "getMiddlewareMatcher", "info", "stored", "get", "Array", "isArray", "matchers", "Error", "JSON", "stringify", "matcher", "getMiddlewareRouteMatcher", "set", "BaseServer", "constructor", "options", "registeredInstrumentation", "cleanupListeners", "AsyncCallbackSet", "handleNextImageRequest", "req", "res", "parsedUrl", "pathname", "startsWith", "getRequestMeta", "minimalMode", "nextConfig", "output", "statusCode", "body", "send", "ImageOptimizerCache", "imageOptimizerCache", "distDir", "sendResponse", "ImageError", "imageResponseCache", "imagesConfig", "images", "loader", "unoptimized", "render404", "paramsResult", "validateParams", "originalRequest", "query", "renderOpts", "dev", "errorMessage", "cache<PERSON>ey", "get<PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "getExtension", "previousCacheEntry", "buffer", "contentType", "maxAge", "upstreamEtag", "etag", "imageOptimizer", "value", "kind", "CachedRouteKind", "IMAGE", "extension", "<PERSON><PERSON><PERSON><PERSON>", "cacheControl", "revalidate", "expire", "undefined", "routeKind", "RouteKind", "incrementalCache", "originalResponse", "href", "isStatic", "isMiss", "isStale", "Boolean", "err", "message", "handleCatchallRenderRequest", "addRequestMeta", "removeTrailingSlash", "i18n", "i18nProvider", "fromRequest", "match", "render", "edgeFunctionsPages", "getEdgeFunctionsPages", "edgeFunctionsPage", "definition", "page", "NEXT_RSC_UNION_QUERY", "handled", "runEdgeFunction", "params", "appPaths", "apiError", "instrumentationOnRequestError", "routePath", "routerKind", "routeType", "revalidateReason", "isPagesAPIRouteMatch", "handleApiRequest", "NoFallbackError", "formatServerError", "logErrorWithOriginalStack", "logError", "renderError", "handleCatchallMiddlewareRequest", "parsed", "isMiddlewareInvoke", "handleFinished", "middleware", "getMiddleware", "initUrl", "parseUrl", "pathnameInfo", "getNextPathnameInfo", "normalizedPathname", "maybeDecodedPathname", "decodeURIComponent", "result", "bubblingResult", "ensureMiddleware", "url", "runMiddleware", "request", "response", "BubbledError", "key", "Object", "entries", "toNodeOutgoingHttpHeaders", "headers", "<PERSON><PERSON><PERSON><PERSON>", "status", "pipeToNodeResponse", "end", "isError", "code", "DecodeError", "error", "getProperError", "console", "finished", "isDev", "sriEnabled", "conf", "experimental", "sri", "algorithm", "optimizeCss", "__NEXT_OPTIMIZE_CSS", "nextScriptWorkers", "__NEXT_SCRIPT_WORKERS", "NEXT_DEPLOYMENT_ID", "deploymentId", "ResponseCache", "appDocumentPreloading", "isDefaultEnabled", "loadComponents", "isAppPath", "catch", "preloadEntriesOnStart", "unstable_preloadEntries", "dynamicRoutes", "getRoutesManifest", "map", "r", "regex", "getRouteRegex", "getRouteMatcher", "re", "setHttpClientAndAgentOptions", "serverOptions", "experimentalTestProxy", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "middlewareManifestPath", "join", "serverDistDir", "MIDDLEWARE_MANIFEST", "prepare", "isExperimentalCompile", "populateStaticEnv", "appPathsManifest", "getAppPathsManifest", "pagesManifest", "getPagesManifest", "loadCustomCacheHandlers", "keys", "ComponentMod", "patchFetch", "webpackRequire", "__next_app__", "m", "handleUpgrade", "loadInstrumentationModule", "instrumentation", "resolve", "dir", "INSTRUMENTATION_HOOK_FILENAME", "cause", "prepareImpl", "runInstrumentationHookIfAvailable", "register", "loadEnvConfig", "forceReload", "silent", "Log", "cacheHandlers", "initializeCacheHandlers", "handler", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interopDefault", "formatDynamicImportPath", "getIncrementalCache", "requestHeaders", "requestProtocol", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cache<PERSON><PERSON><PERSON>", "IncrementalCache", "fs", "getCacheFilesystem", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "cacheMaxMemorySize", "flushToDisk", "isrFlushToDisk", "getPrerenderManifest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getResponseCache", "getPublicDir", "CLIENT_PUBLIC_FILES_PATH", "getHasStaticDir", "existsSync", "loadManifest", "PAGES_MANIFEST", "enabledDirectories", "app", "APP_PATHS_MANIFEST", "getinterceptionRoutePatterns", "routesManifest", "rewrites", "beforeFiles", "filter", "isInterceptionRouteRewrite", "rewrite", "RegExp", "hasPage", "getMaybePagePath", "locales", "getBuildId", "buildIdFile", "BUILD_ID_FILE", "readFileSync", "trim", "getEnabledDirectories", "findDir", "pages", "sendRenderResult", "type", "generateEtags", "poweredByHeader", "run<PERSON><PERSON>", "handledAsEdgeFunction", "module", "RouteModuleLoader", "load", "filename", "previewProps", "bind", "trustHostHeader", "hostname", "fetchHostname", "onError", "multiZoneDraftMode", "renderHTML", "getTracer", "trace", "NextNodeServerSpan", "renderHTMLImpl", "nextFontManifest", "lazyRenderAppPage", "getServerComponentsHmrCache", "buildId", "lazyRenderPagesPage", "customServer", "isDraftMode", "developmentNotFoundSourcePage", "fetchExternalImage", "fetchInternalImage", "handleInternalReq", "newReq", "newRes", "routerServerHandler", "isAbsolute", "imageUpstream", "getPagePath", "renderPageComponent", "ctx", "bubbleNoFallback", "length", "getOriginalAppPaths", "findPageComponents", "locale", "spanName", "attributes", "normalizeAppPath", "findPageComponentsImpl", "_url", "pagePaths", "amp", "unshift", "normalizePagePath", "path", "pagePath", "components", "Component", "getStaticProps", "PageNotFoundError", "getNextFontManifest", "NEXT_FONT_MANIFEST", "_err", "_type", "ensurePage", "_opts", "nodeFs", "normalizeReq", "NodeNextRequest", "normalizeRes", "NodeNextResponse", "getRequestHandler", "makeRequestHandler", "wrapRequestHandlerNode", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "mocked", "createRequestResponseMocks", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "unstable_onlyGenerated", "internal", "renderToHTML", "renderErrorToResponseImpl", "is404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "clientOnly", "includes", "setHeaders", "renderErrorToHTML", "getMiddlewareManifest", "manifest", "middlewareModule", "loadNodeMiddleware", "config", "regexp", "originalSource", "functions", "getEdgeFunctionInfo", "foundPage", "denormalizePagePath", "pageInfo", "name", "paths", "files", "file", "wasm", "binding", "filePath", "assets", "nodeMiddleware", "functionsConfig", "FUNCTIONS_CONFIG_MANIFEST", "hasMiddleware", "ensureEdgeFunction", "_params", "checkIsOnDemandRevalidate", "isOnDemandRevalidate", "Response", "skipMiddlewareUrlNormalize", "urlQueryToSearchParams", "toString", "port", "middlewareInfo", "method", "toUpperCase", "requestData", "basePath", "trailingSlash", "signal", "signalFromNodeResponse", "waitUntil", "getWaitUntil", "MiddlewareNotFoundError", "adapterFn", "run", "edgeFunctionEntry", "useCache", "onWarning", "has", "cookies", "getSetCookie", "flatMap", "maybeCompoundCookie", "splitCookiesString", "delete", "cookie", "append", "_cachedPreviewManifest", "NODE_ENV", "NEXT_PHASE", "PHASE_PRODUCTION_BUILD", "version", "routes", "notFoundRoutes", "preview", "previewModeId", "randomBytes", "previewModeSigningKey", "previewModeEncryptionKey", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "afterFiles", "fallback", "attachRequestMeta", "isUpgradeReq", "protocol", "host", "getCloneableBody", "edgeInfo", "isNextDataRequest", "initialUrl", "URL", "queryString", "fromEntries", "searchParams", "search", "globalThis", "__incrementalCache", "serverComponentsHmrCache", "fetchMetrics", "statusMessage", "statusText", "for<PERSON>ach", "toLowerCase", "append<PERSON><PERSON>er", "_serverDistDir", "SERVER_DIRECTORY", "getFallbackErrorComponents", "args", "onServerClose", "listener", "add", "close", "runAll", "getInternalWaitUntil", "internalWaitUntil", "createInternalWaitUntil", "InvariantError", "awaiter", "Awaiter<PERSON>nce", "awaiting"], "mappings": ";;;;+BAiKA;;;eAAqBA;;;;QAjKd;QACA;QACA;uBAOA;2DAkBQ;sBACe;8BACE;6BACe;2BAcxC;8BACiB;sBAC0B;6BACjB;0BACR;6DACJ;iFAauB;yBACE;qCACV;mCACF;gCACH;iEAES;wBACsB;wCACpB;qBACZ;6BACS;qCACH;qCACA;6BACH;0BACS;uEAInC;kCAC0B;0BACA;mCAEY;oCAER;4BAGS;wBACN;4BACL;+BACZ;4BACO;8BACK;6BACQ;kCACN;6BACE;mCACL;8BACL;8BACK;+BACE;gCACL;yCACS;oDAEG;2BAEjB;gCACK;yBACH;kCACK;0BACwB;2BAEvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC,yCAAyC;AACzC,MAAMC,0BAA0BC,QAAQC,GAAG,CAACC,YAAY,GACpD,CAACC,KACC,MAAM,CAAC,uBAAuB,GAAGA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID,OACpE,CAACF,KAAe,MAAM,CAACA,IAAIC,IAAI,CAAC,CAACC,MAAQA,IAAIC,OAAO,IAAID;AAE5D,2DAA2D;AAC3D,MAAME,iBAAiBP,QAAQC,GAAG,CAACC,YAAY,GAC3CM,0BACAC;AASJ,MAAMC,yBAAyB,IAAIC;AAKnC,SAASC,qBACPC,IAA8C;IAE9C,MAAMC,SAASJ,uBAAuBK,GAAG,CAACF;IAC1C,IAAIC,QAAQ;QACV,OAAOA;IACT;IAEA,IAAI,CAACE,MAAMC,OAAO,CAACJ,KAAKK,QAAQ,GAAG;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAEC,KAAKC,SAAS,CAACR,OAAO,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMS,UAAUC,IAAAA,iDAAyB,EAACV,KAAKK,QAAQ;IACvDR,uBAAuBc,GAAG,CAACX,MAAMS;IACjC,OAAOA;AACT;AAEe,MAAMxB,uBAAuB2B,mBAAU;IAyBpDC,YAAYC,OAAgB,CAAE;YAMFA,gCAAAA;QAL1B,yBAAyB;QACzB,KAAK,CAACA,eAnBAC,4BAAqC,YAYnCC,mBAAmB,IAAIC,kCAAgB,SAgsBvCC,yBAA2C,OACnDC,KACAC,KACAC;YAEA,IAAI,CAACA,UAAUC,QAAQ,IAAI,CAACD,UAAUC,QAAQ,CAACC,UAAU,CAAC,iBAAiB;gBACzE,OAAO;YACT;YACA,qCAAqC;YACrC,IAAIC,IAAAA,2BAAc,EAACL,KAAK,qBAAqB;gBAC3C,OAAO;YACT;YAEA,IACE,IAAI,CAACM,WAAW,IAChB,IAAI,CAACC,UAAU,CAACC,MAAM,KAAK,YAC3BxC,QAAQC,GAAG,CAACC,YAAY,EACxB;gBACA+B,IAAIQ,UAAU,GAAG;gBACjBR,IAAIS,IAAI,CAAC,eAAeC,IAAI;gBAC5B,OAAO;YACP,+CAA+C;YACjD,OAAO;gBACL,MAAM,EAAEC,mBAAmB,EAAE,GAC3BnC,QAAQ;gBAEV,MAAMoC,sBAAsB,IAAID,oBAAoB;oBAClDE,SAAS,IAAI,CAACA,OAAO;oBACrBP,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,MAAM,EAAEQ,YAAY,EAAEC,UAAU,EAAE,GAChCvC,QAAQ;gBAEV,IAAI,CAAC,IAAI,CAACwC,kBAAkB,EAAE;oBAC5B,MAAM,qBAAgE,CAAhE,IAAI9B,MAAM,wDAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA+D;gBACvE;gBACA,MAAM+B,eAAe,IAAI,CAACX,UAAU,CAACY,MAAM;gBAE3C,IAAID,aAAaE,MAAM,KAAK,aAAaF,aAAaG,WAAW,EAAE;oBACjE,MAAM,IAAI,CAACC,SAAS,CAACtB,KAAKC;oBAC1B,OAAO;gBACT;gBAEA,MAAMsB,eAAeX,oBAAoBY,cAAc,CACrDxB,IAAIyB,eAAe,EACnBvB,UAAUwB,KAAK,EACf,IAAI,CAACnB,UAAU,EACf,CAAC,CAAC,IAAI,CAACoB,UAAU,CAACC,GAAG;gBAGvB,IAAI,kBAAkBL,cAAc;oBAClCtB,IAAIQ,UAAU,GAAG;oBACjBR,IAAIS,IAAI,CAACa,aAAaM,YAAY,EAAElB,IAAI;oBACxC,OAAO;gBACT;gBAEA,MAAMmB,WAAWlB,oBAAoBmB,WAAW,CAACR;gBAEjD,IAAI;wBAiCES,mBAgBFA;oBAhDF,MAAM,EAAEC,YAAY,EAAE,GACpBxD,QAAQ;oBACV,MAAMuD,aAAa,MAAM,IAAI,CAACf,kBAAkB,CAAClC,GAAG,CAClD+C,UACA,OAAO,EAAEI,kBAAkB,EAAE;wBAC3B,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAE,GACvD,MAAM,IAAI,CAACC,cAAc,CACvBxC,KACAC,KACAsB,cACAW;wBAGJ,OAAO;4BACLO,OAAO;gCACLC,MAAMC,8BAAe,CAACC,KAAK;gCAC3BT;gCACAI;gCACAM,WAAWZ,aAAaG;gCACxBE;4BACF;4BACAQ,YAAY;4BACZC,cAAc;gCAAEC,YAAYX;gCAAQY,QAAQC;4BAAU;wBACxD;oBACF,GACA;wBACEC,WAAWC,oBAAS,CAACR,KAAK;wBAC1BS,kBAAkBxC;wBAClBiC,YAAY;oBACd;oBAGF,IAAId,CAAAA,+BAAAA,oBAAAA,WAAYS,KAAK,qBAAjBT,kBAAmBU,IAAI,MAAKC,8BAAe,CAACC,KAAK,EAAE;wBACrD,MAAM,qBAEL,CAFK,IAAIzD,MACR,0DADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA4B,aACEf,IAAIyB,eAAe,EACnBxB,IAAIqD,gBAAgB,EACpB/B,aAAagC,IAAI,EACjBvB,WAAWS,KAAK,CAACI,SAAS,EAC1Bb,WAAWS,KAAK,CAACN,MAAM,EACvBH,WAAWS,KAAK,CAACF,IAAI,EACrBhB,aAAaiC,QAAQ,EACrBxB,WAAWyB,MAAM,GAAG,SAASzB,WAAW0B,OAAO,GAAG,UAAU,OAC5DxC,cACAc,EAAAA,2BAAAA,WAAWe,YAAY,qBAAvBf,yBAAyBgB,UAAU,KAAI,GACvCW,QAAQ,IAAI,CAAChC,UAAU,CAACC,GAAG;oBAE7B,OAAO;gBACT,EAAE,OAAOgC,KAAK;oBACZ,IAAIA,eAAe5C,YAAY;wBAC7Bf,IAAIQ,UAAU,GAAGmD,IAAInD,UAAU;wBAC/BR,IAAIS,IAAI,CAACkD,IAAIC,OAAO,EAAElD,IAAI;wBAC1B,OAAO;oBACT;oBACA,MAAMiD;gBACR;YACF;QACF,QAEUE,8BAAgD,OACxD9D,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEuB,KAAK,EAAE,GAAGxB;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,qBAA6C,CAA7C,IAAIhB,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,wEAAwE;YACxE,QAAQ;YACR4E,IAAAA,2BAAc,EAAC/D,KAAK,oBAAoB;YAExC,IAAI;oBAKM;gBAJR,wDAAwD;gBACxDG,WAAW6D,IAAAA,wCAAmB,EAAC7D;gBAE/B,MAAMR,UAAwB;oBAC5BsE,IAAI,GAAE,qBAAA,IAAI,CAACC,YAAY,qBAAjB,mBAAmBC,WAAW,CAACnE,KAAKG;gBAC5C;gBACA,MAAMiE,QAAQ,MAAM,IAAI,CAAClF,QAAQ,CAACkF,KAAK,CAACjE,UAAUR;gBAElD,sDAAsD;gBACtD,IAAI,CAACyE,OAAO;oBACV,MAAM,IAAI,CAACC,MAAM,CAACrE,KAAKC,KAAKE,UAAUuB,OAAOxB,WAAW;oBAExD,OAAO;gBACT;gBAEA,sEAAsE;gBACtE,wBAAwB;gBACxB6D,IAAAA,2BAAc,EAAC/D,KAAK,SAASoE;gBAE7B,yCAAyC;gBACzC,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;gBACrD,KAAK,MAAMC,qBAAqBF,mBAAoB;oBAClD,6DAA6D;oBAC7D,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACC,IAAI,EAAE;oBAEjD,IAAI,IAAI,CAACnE,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACc,SAAS,CAACtB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBACA,OAAOwB,KAAK,CAACiD,sCAAoB,CAAC;oBAElC,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI;wBACF,MAAMC,UAAU,MAAM,IAAI,CAACC,eAAe,CAAC;4BACzC7E;4BACAC;4BACAyB;4BACAoD,QAAQV,MAAMU,MAAM;4BACpBJ,MAAMN,MAAMK,UAAU,CAACC,IAAI;4BAC3BN;4BACAW,UAAU;wBACZ;wBACA,IAAIH,SAAS,OAAO;oBACtB,EAAE,OAAOI,UAAU;wBACjB,MAAM,IAAI,CAACC,6BAA6B,CAACD,UAAUhF,KAAK;4BACtDkF,WAAWd,MAAMK,UAAU,CAACC,IAAI;4BAChCS,YAAY;4BACZC,WAAW;4BACX,oCAAoC;4BACpCC,kBAAkBnC;wBACpB;wBACA,MAAM8B;oBACR;gBACF;gBAEA,oEAAoE;gBACpE,MAAM;gBACN,iDAAiD;gBACjD,IAAIM,IAAAA,wCAAoB,EAAClB,QAAQ;oBAC/B,IAAI,IAAI,CAAC7D,UAAU,CAACC,MAAM,KAAK,UAAU;wBACvC,MAAM,IAAI,CAACc,SAAS,CAACtB,KAAKC,KAAKC;wBAC/B,OAAO;oBACT;oBAEA,MAAM0E,UAAU,MAAM,IAAI,CAACW,gBAAgB,CAACvF,KAAKC,KAAKyB,OAAO0C;oBAC7D,IAAIQ,SAAS,OAAO;gBACtB;gBAEA,MAAM,IAAI,CAACP,MAAM,CAACrE,KAAKC,KAAKE,UAAUuB,OAAOxB,WAAW;gBAExD,OAAO;YACT,EAAE,OAAO0D,KAAU;gBACjB,IAAIA,eAAe4B,2BAAe,EAAE;oBAClC,MAAM5B;gBACR;gBAEA,IAAI;oBACF,IAAI,IAAI,CAACjC,UAAU,CAACC,GAAG,EAAE;wBACvB,MAAM,EAAE6D,iBAAiB,EAAE,GACzBhH,QAAQ;wBACVgH,kBAAkB7B;wBAClB,IAAI,CAAC8B,yBAAyB,CAAC9B;oBACjC,OAAO;wBACL,IAAI,CAAC+B,QAAQ,CAAC/B;oBAChB;oBACA3D,IAAIQ,UAAU,GAAG;oBACjB,MAAM,IAAI,CAACmF,WAAW,CAAChC,KAAK5D,KAAKC,KAAKE,UAAUuB;oBAChD,OAAO;gBACT,EAAE,OAAM,CAAC;gBAET,MAAMkC;YACR;QACF,QA+hBUiC,kCAAoD,OAC5D7F,KACAC,KACA6F;YAEA,MAAMC,qBAAqB1F,IAAAA,2BAAc,EAACL,KAAK;YAE/C,IAAI,CAAC+F,oBAAoB;gBACvB,OAAO;YACT;YAEA,MAAMC,iBAAiB;gBACrBjC,IAAAA,2BAAc,EAAC/D,KAAK,oBAAoB;gBACxCC,IAAIS,IAAI,CAAC,IAAIC,IAAI;gBACjB,OAAO;YACT;YAEA,MAAMsF,aAAa,MAAM,IAAI,CAACC,aAAa;YAC3C,IAAI,CAACD,YAAY;gBACf,OAAOD;YACT;YAEA,MAAMG,UAAU9F,IAAAA,2BAAc,EAACL,KAAK;YACpC,MAAME,YAAYkG,IAAAA,kBAAQ,EAACD;YAC3B,MAAME,eAAeC,IAAAA,wCAAmB,EAACpG,UAAUC,QAAQ,EAAE;gBAC3DI,YAAY,IAAI,CAACA,UAAU;gBAC3B2D,cAAc,IAAI,CAACA,YAAY;YACjC;YAEAhE,UAAUC,QAAQ,GAAGkG,aAAalG,QAAQ;YAC1C,MAAMoG,qBAAqBvC,IAAAA,wCAAmB,EAAC8B,OAAO3F,QAAQ,IAAI;YAClE,IAAIqG,uBAAuBD;YAE3B,IAAI;gBACFC,uBAAuBC,mBAAmBF;YAC5C,EAAE,OAAM;YACN,+CAA+C,GACjD;YAEA,IACE,CACEN,CAAAA,WAAW7B,KAAK,CAACmC,oBAAoBvG,KAAKE,UAAUwB,KAAK,KACzDuE,WAAW7B,KAAK,CAACoC,sBAAsBxG,KAAKE,UAAUwB,KAAK,CAAA,GAE7D;gBACA,OAAOsE;YACT;YAEA,IAAIU;YAGJ,IAAIC,iBAAiB;YAErB,IAAI;gBACF,MAAM,IAAI,CAACC,gBAAgB,CAAC5G,IAAI6G,GAAG;gBAEnCH,SAAS,MAAM,IAAI,CAACI,aAAa,CAAC;oBAChCC,SAAS/G;oBACTgH,UAAU/G;oBACVC,WAAWA;oBACX4F,QAAQA;gBACV;gBAEA,IAAI,cAAcY,QAAQ;oBACxB,IAAIX,oBAAoB;wBACtBY,iBAAiB;wBACjB,MAAM,qBAA8B,CAA9B,IAAIM,oBAAY,CAAC,MAAMP,SAAvB,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC;oBAEA,KAAK,MAAM,CAACQ,KAAKzE,MAAM,IAAI0E,OAAOC,OAAO,CACvCC,IAAAA,iCAAyB,EAACX,OAAOM,QAAQ,CAACM,OAAO,GAChD;wBACD,IAAIJ,QAAQ,sBAAsBzE,UAAUS,WAAW;4BACrDjD,IAAIsH,SAAS,CAACL,KAAKzE;wBACrB;oBACF;oBACAxC,IAAIQ,UAAU,GAAGiG,OAAOM,QAAQ,CAACQ,MAAM;oBAEvC,MAAM,EAAElE,gBAAgB,EAAE,GAAGrD;oBAC7B,IAAIyG,OAAOM,QAAQ,CAACtG,IAAI,EAAE;wBACxB,MAAM+G,IAAAA,gCAAkB,EAACf,OAAOM,QAAQ,CAACtG,IAAI,EAAE4C;oBACjD,OAAO;wBACLA,iBAAiBoE,GAAG;oBACtB;oBACA,OAAO;gBACT;YACF,EAAE,OAAO9D,KAAc;gBACrB,IAAI+C,gBAAgB;oBAClB,MAAM/C;gBACR;gBAEA,IAAI+D,IAAAA,gBAAO,EAAC/D,QAAQA,IAAIgE,IAAI,KAAK,UAAU;oBACzC,MAAM,IAAI,CAACtG,SAAS,CAACtB,KAAKC,KAAK6F;oBAC/B,OAAO;gBACT;gBAEA,IAAIlC,eAAeiE,kBAAW,EAAE;oBAC9B5H,IAAIQ,UAAU,GAAG;oBACjB,MAAM,IAAI,CAACmF,WAAW,CAAChC,KAAK5D,KAAKC,KAAK6F,OAAO3F,QAAQ,IAAI;oBACzD,OAAO;gBACT;gBAEA,MAAM2H,QAAQC,IAAAA,uBAAc,EAACnE;gBAC7BoE,QAAQF,KAAK,CAACA;gBACd7H,IAAIQ,UAAU,GAAG;gBACjB,MAAM,IAAI,CAACmF,WAAW,CAACkC,OAAO9H,KAAKC,KAAK6F,OAAO3F,QAAQ,IAAI;gBAC3D,OAAO;YACT;YAEA,OAAOuG,OAAOuB,QAAQ;QACxB;QA3iDE,MAAMC,QAAQvI,QAAQiC,GAAG,IAAI;QAC7B,IAAI,CAACsG,KAAK,GAAGA;QACb,IAAI,CAACC,UAAU,GAAGxE,SAAQhE,6BAAAA,QAAQyI,IAAI,CAACC,YAAY,sBAAzB1I,iCAAAA,2BAA2B2I,GAAG,qBAA9B3I,+BAAgC4I,SAAS;QAEnE;;;;KAIC,GACD,IAAI,IAAI,CAAC5G,UAAU,CAAC6G,WAAW,EAAE;YAC/BxK,QAAQC,GAAG,CAACwK,mBAAmB,GAAGrJ,KAAKC,SAAS,CAAC;QACnD;QACA,IAAI,IAAI,CAACsC,UAAU,CAAC+G,iBAAiB,EAAE;YACrC1K,QAAQC,GAAG,CAAC0K,qBAAqB,GAAGvJ,KAAKC,SAAS,CAAC;QACrD;QACArB,QAAQC,GAAG,CAAC2K,kBAAkB,GAAG,IAAI,CAACrI,UAAU,CAACsI,YAAY,IAAI;QAEjE,IAAI,CAAC,IAAI,CAACvI,WAAW,EAAE;YACrB,IAAI,CAACW,kBAAkB,GAAG,IAAI6H,sBAAa,CAAC,IAAI,CAACxI,WAAW;QAC9D;QAEA,MAAM,EAAEyI,qBAAqB,EAAE,GAAG,IAAI,CAACxI,UAAU,CAAC8H,YAAY;QAC9D,MAAMW,mBAAmB,OAAOD,0BAA0B;QAE1D,IACE,CAACpJ,QAAQiC,GAAG,IACXmH,CAAAA,0BAA0B,QACzB,CAAE,CAAA,IAAI,CAACzI,WAAW,IAAI0I,gBAAe,CAAC,GACxC;YACA,+CAA+C;YAC/C,2BAA2B;YAC3BC,IAAAA,8BAAc,EAAC;gBACbnI,SAAS,IAAI,CAACA,OAAO;gBACrB4D,MAAM;gBACNwE,WAAW;gBACXhB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGgB,KAAK,CAAC,KAAO;YAChBF,IAAAA,8BAAc,EAAC;gBACbnI,SAAS,IAAI,CAACA,OAAO;gBACrB4D,MAAM;gBACNwE,WAAW;gBACXhB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGgB,KAAK,CAAC,KAAO;QAClB;QAEA,IACE,CAACxJ,QAAQiC,GAAG,IACZ,CAAC,IAAI,CAACtB,WAAW,IACjB,IAAI,CAACC,UAAU,CAAC8H,YAAY,CAACe,qBAAqB,EAClD;YACA,IAAI,CAACC,uBAAuB;QAC9B;QAEA,IAAI,CAAC1J,QAAQiC,GAAG,EAAE;YAChB,MAAM,EAAE0H,gBAAgB,EAAE,EAAE,GAAG,IAAI,CAACC,iBAAiB,MAAM,CAAC;YAC5D,IAAI,CAACD,aAAa,GAAGA,cAAcE,GAAG,CAAC,CAACC;gBACtC,wDAAwD;gBACxD,MAAMC,QAAQC,IAAAA,yBAAa,EAACF,EAAE/E,IAAI;gBAClC,MAAMN,QAAQwF,IAAAA,6BAAe,EAACF;gBAE9B,OAAO;oBACLtF;oBACAM,MAAM+E,EAAE/E,IAAI;oBACZmF,IAAIH,MAAMG,EAAE;gBACd;YACF;QACF;QAEA,sDAAsD;QACtDC,IAAAA,+CAA4B,EAAC,IAAI,CAACvJ,UAAU;QAE5C,2CAA2C;QAC3C,IAAI,IAAI,CAACwJ,aAAa,CAACC,qBAAqB,EAAE;YAC5ChM,QAAQC,GAAG,CAACgM,uBAAuB,GAAG;YACtC,MAAM,EACJC,iBAAiB,EAClB,GAAGzL,QAAQ;YACZyL;QACF;QAEA,IAAI,CAACC,sBAAsB,GAAGC,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAEC,8BAAmB;QAE1E,4EAA4E;QAC5E,2EAA2E;QAC3E,qEAAqE;QACrE,uEAAuE;QACvE,IAAI,CAAC3K,QAAQiC,GAAG,EAAE;YAChB,IAAI,CAAC2I,OAAO,GAAGpB,KAAK,CAAC,CAACvF;gBACpBoE,QAAQF,KAAK,CAAC,4BAA4BlE;YAC5C;QACF;QAEA,yDAAyD;QACzD,yCAAyC;QACzC,IAAI,IAAI,CAACjC,UAAU,CAAC6I,qBAAqB,EAAE;YACzCC,IAAAA,4BAAiB,EAAC,IAAI,CAAClK,UAAU;QACnC;IACF;IAEA,MAAa8I,0BAAyC;QACpD,MAAMqB,mBAAmB,IAAI,CAACC,mBAAmB;QACjD,MAAMC,gBAAgB,IAAI,CAACC,gBAAgB;QAE3C,MAAM,IAAI,CAACC,uBAAuB;QAElC,KAAK,MAAMpG,QAAQyC,OAAO4D,IAAI,CAACH,iBAAiB,CAAC,GAAI;YACnD,MAAM3B,IAAAA,8BAAc,EAAC;gBACnBnI,SAAS,IAAI,CAACA,OAAO;gBACrB4D;gBACAwE,WAAW;gBACXhB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GAAGgB,KAAK,CAAC,KAAO;QAClB;QAEA,KAAK,MAAMzE,QAAQyC,OAAO4D,IAAI,CAACL,oBAAoB,CAAC,GAAI;YACtD,MAAMzB,IAAAA,8BAAc,EAAC;gBACnBnI,SAAS,IAAI,CAACA,OAAO;gBACrB4D;gBACAwE,WAAW;gBACXhB,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAY,IAAI,CAACA,UAAU;YAC7B,GACG/J,IAAI,CAAC,OAAO,EAAE4M,YAAY,EAAE;gBAC3B,iEAAiE;gBACjE,yEAAyE;gBACzE,oDAAoD;gBACpDA,aAAaC,UAAU;gBAEvB,MAAMC,iBAAiBF,aAAaG,YAAY,CAAC1M,OAAO;gBACxD,IAAIyM,kCAAAA,eAAgBE,CAAC,EAAE;oBACrB,KAAK,MAAMjN,MAAMgJ,OAAO4D,IAAI,CAACG,eAAeE,CAAC,EAAG;wBAC9C,MAAMF,eAAe/M;oBACvB;gBACF;YACF,GACCgL,KAAK,CAAC,KAAO;QAClB;IACF;IAEA,MAAgBkC,gBAA+B;IAC7C,yEAAyE;IACzE,eAAe;IACjB;IAEA,MAAgBC,4BAA4B;QAC1C,IAAI,CAAC,IAAI,CAACvB,aAAa,CAACnI,GAAG,EAAE;YAC3B,IAAI;gBACF,IAAI,CAAC2J,eAAe,GAAG,MAAMhN,eAC3BiN,IAAAA,aAAO,EACL,IAAI,CAACzB,aAAa,CAAC0B,GAAG,IAAI,KAC1B,IAAI,CAAC1B,aAAa,CAAC3B,IAAI,CAACtH,OAAO,EAC/B,UACA4K,yCAA6B;YAGnC,EAAE,OAAO9H,KAAU;gBACjB,IAAIA,IAAIgE,IAAI,KAAK,oBAAoB;oBACnC,MAAM,qBAGL,CAHK,IAAIzI,MACR,4DACA;wBAAEwM,OAAO/H;oBAAI,IAFT,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF;YACF;QACF;QACA,OAAO,IAAI,CAAC2H,eAAe;IAC7B;IAEA,MAAgBK,cAAc;QAC5B,MAAM,KAAK,CAACA;QACZ,MAAM,IAAI,CAACC,iCAAiC;IAC9C;IAEA,MAAgBA,oCAAoC;YAG5C,gCAAA;QAFN,IAAI,IAAI,CAACjM,yBAAyB,EAAE;QACpC,IAAI,CAACA,yBAAyB,GAAG;QACjC,QAAM,wBAAA,IAAI,CAAC2L,eAAe,sBAApB,iCAAA,sBAAsBO,QAAQ,qBAA9B,oCAAA;IACR;IAEUC,cAAc,EACtBnK,GAAG,EACHoK,WAAW,EACXC,MAAM,EAKP,EAAE;QACDF,IAAAA,kBAAa,EACX,IAAI,CAACN,GAAG,EACR7J,KACAqK,SAAS;YAAEpN,MAAM,KAAO;YAAGiJ,OAAO,KAAO;QAAE,IAAIoE,MAC/CF;IAEJ;IAEA,MAAclB,0BAA0B;QACtC,MAAM,EAAEqB,aAAa,EAAE,GAAG,IAAI,CAAC5L,UAAU,CAAC8H,YAAY;QACtD,IAAI,CAAC8D,eAAe;QAEpB,yEAAyE;QACzE,SAAS;QACT,IAAI,CAACC,IAAAA,iCAAuB,KAAI;QAEhC,KAAK,MAAM,CAAC1J,MAAM2J,QAAQ,IAAIlF,OAAOC,OAAO,CAAC+E,eAAgB;YAC3D,IAAI,CAACE,SAAS;YAEdC,IAAAA,yBAAe,EACb5J,MACA6J,IAAAA,8BAAc,EACZ,MAAMxO,wBACJyO,IAAAA,gDAAuB,EAAC,IAAI,CAAC1L,OAAO,EAAEuL;QAI9C;IACF;IAEA,MAAgBI,oBAAoB,EAClCC,cAAc,EACdC,eAAe,EAIhB,EAAE;QACD,MAAM/K,MAAM,CAAC,CAAC,IAAI,CAACD,UAAU,CAACC,GAAG;QACjC,IAAIgL;QACJ,MAAM,EAAEC,YAAY,EAAE,GAAG,IAAI,CAACtM,UAAU;QAExC,IAAIsM,cAAc;YAChBD,eAAeL,IAAAA,8BAAc,EAC3B,MAAMxO,wBACJyO,IAAAA,gDAAuB,EAAC,IAAI,CAAC1L,OAAO,EAAE+L;QAG5C;QAEA,MAAM,IAAI,CAAC/B,uBAAuB;QAElC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIgC,kCAAgB,CAAC;YAC1BC,IAAI,IAAI,CAACC,kBAAkB;YAC3BpL;YACA8K;YACAC;YACAM,6BACE,IAAI,CAAC1M,UAAU,CAAC8H,YAAY,CAAC4E,2BAA2B;YAC1D3M,aAAa,IAAI,CAACA,WAAW;YAC7B+J,eAAe,IAAI,CAACA,aAAa;YACjC6C,qBAAqB,IAAI,CAAC3M,UAAU,CAAC8H,YAAY,CAAC6E,mBAAmB;YACrEC,oBAAoB,IAAI,CAAC5M,UAAU,CAAC6M,kBAAkB;YACtDC,aACE,CAAC,IAAI,CAAC/M,WAAW,IAAI,IAAI,CAACC,UAAU,CAAC8H,YAAY,CAACiF,cAAc;YAClEC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;YACrDC,iBAAiBZ;QACnB;IACF;IAEUa,mBAAmB;QAC3B,OAAO,IAAI3E,sBAAa,CAAC,IAAI,CAACxI,WAAW;IAC3C;IAEUoN,eAAuB;QAC/B,OAAOtD,IAAAA,UAAI,EAAC,IAAI,CAACqB,GAAG,EAAEkC,mCAAwB;IAChD;IAEUC,kBAA2B;QACnC,OAAOb,WAAE,CAACc,UAAU,CAACzD,IAAAA,UAAI,EAAC,IAAI,CAACqB,GAAG,EAAE;IACtC;IAEUZ,mBAA8C;QACtD,OAAOiD,IAAAA,0BAAY,EACjB1D,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAE0D,yBAAc;IAE3C;IAEUpD,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACqD,kBAAkB,CAACC,GAAG,EAAE,OAAO/K;QAEzC,OAAO4K,IAAAA,0BAAY,EACjB1D,IAAAA,UAAI,EAAC,IAAI,CAACC,aAAa,EAAE6D,6BAAkB;IAE/C;IAEUC,+BAAyC;QACjD,IAAI,CAAC,IAAI,CAACH,kBAAkB,CAACC,GAAG,EAAE,OAAO,EAAE;QAE3C,MAAMG,iBAAiB,IAAI,CAAC7E,iBAAiB;QAC7C,OACE6E,CAAAA,kCAAAA,eAAgBC,QAAQ,CAACC,WAAW,CACjCC,MAAM,CAACC,8DAA0B,EACjChF,GAAG,CAAC,CAACiF,UAAY,IAAIC,OAAOD,QAAQ/E,KAAK,OAAM,EAAE;IAExD;IAEA,MAAgBiF,QAAQxO,QAAgB,EAAoB;YAIxD;QAHF,OAAO,CAAC,CAACyO,IAAAA,yBAAgB,EACvBzO,UACA,IAAI,CAACW,OAAO,GACZ,wBAAA,IAAI,CAACP,UAAU,CAAC0D,IAAI,qBAApB,sBAAsB4K,OAAO,EAC7B,IAAI,CAACb,kBAAkB,CAACC,GAAG;IAE/B;IAEUa,aAAqB;QAC7B,MAAMC,cAAc3E,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAEkO,wBAAa;QACpD,IAAI;YACF,OAAOjC,WAAE,CAACkC,YAAY,CAACF,aAAa,QAAQG,IAAI;QAClD,EAAE,OAAOtL,KAAU;YACjB,IAAIA,IAAIgE,IAAI,KAAK,UAAU;gBACzB,MAAM,qBAEL,CAFK,IAAIzI,MACR,CAAC,0CAA0C,EAAE,IAAI,CAAC2B,OAAO,CAAC,yJAAyJ,CAAC,GADhN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,MAAM8C;QACR;IACF;IAEUuL,sBAAsBvN,GAAY,EAA0B;QACpE,MAAM6J,MAAM7J,MAAM,IAAI,CAAC6J,GAAG,GAAG,IAAI,CAACpB,aAAa;QAE/C,OAAO;YACL4D,KAAKmB,IAAAA,qBAAO,EAAC3D,KAAK,SAAS,OAAO;YAClC4D,OAAOD,IAAAA,qBAAO,EAAC3D,KAAK,WAAW,OAAO;QACxC;IACF;IAEU6D,iBACRtP,GAAoB,EACpBC,GAAqB,EACrBN,OAMC,EACc;QACf,OAAO2P,IAAAA,6BAAgB,EAAC;YACtBtP,KAAKA,IAAIyB,eAAe;YACxBxB,KAAKA,IAAIqD,gBAAgB;YACzBoD,QAAQ/G,QAAQ+G,MAAM;YACtB6I,MAAM5P,QAAQ4P,IAAI;YAClBC,eAAe7P,QAAQ6P,aAAa;YACpCC,iBAAiB9P,QAAQ8P,eAAe;YACxC1M,cAAcpD,QAAQoD,YAAY;QACpC;IACF;IAEA,MAAgB2M,OACd1P,GAAoB,EACpBC,GAAqB,EACrByB,KAAqB,EACrB0C,KAAyB,EACP;QAClB,MAAME,qBAAqB,IAAI,CAACC,qBAAqB;QAErD,KAAK,MAAMC,qBAAqBF,mBAAoB;YAClD,IAAIE,sBAAsBJ,MAAMK,UAAU,CAACtE,QAAQ,EAAE;gBACnD,MAAMwP,wBAAwB,MAAM,IAAI,CAAC9K,eAAe,CAAC;oBACvD7E;oBACAC;oBACAyB;oBACAoD,QAAQV,MAAMU,MAAM;oBACpBJ,MAAMN,MAAMK,UAAU,CAACtE,QAAQ;oBAC/B4E,UAAU;gBACZ;gBAEA,IAAI4K,uBAAuB;oBACzB,OAAO;gBACT;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAMC,SAAS,MAAMC,oCAAiB,CAACC,IAAI,CACzC1L,MAAMK,UAAU,CAACsL,QAAQ;QAG3BrO,QAAQ;YAAE,GAAGA,KAAK;YAAE,GAAG0C,MAAMU,MAAM;QAAC;QAEpC,MAAM8K,OAAOvL,MAAM,CAACrE,IAAIyB,eAAe,EAAExB,IAAIqD,gBAAgB,EAAE;YAC7D0M,cAAc,IAAI,CAACrO,UAAU,CAACqO,YAAY;YAC1ChN,YAAY,IAAI,CAACA,UAAU,CAACiN,IAAI,CAAC,IAAI;YACrCC,iBAAiB,IAAI,CAAC3P,UAAU,CAAC8H,YAAY,CAAC6H,eAAe;YAC7DjD,6BACE,IAAI,CAAC1M,UAAU,CAAC8H,YAAY,CAAC4E,2BAA2B;YAC1DkD,UAAU,IAAI,CAACC,aAAa;YAC5B9P,aAAa,IAAI,CAACA,WAAW;YAC7BsB,KAAK,IAAI,CAACD,UAAU,CAACC,GAAG,KAAK;YAC7BF;YACAoD,QAAQV,MAAMU,MAAM;YACpBJ,MAAMN,MAAMK,UAAU,CAACtE,QAAQ;YAC/BkQ,SAAS,IAAI,CAACpL,6BAA6B,CAACgL,IAAI,CAAC,IAAI;YACrDK,oBAAoB,IAAI,CAAC/P,UAAU,CAAC8H,YAAY,CAACiI,kBAAkB;QACrE;QAEA,OAAO;IACT;IAEA,MAAgBC,WACdvQ,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBuB,KAAyB,EACzBC,UAA4B,EACL;QACvB,OAAO6O,IAAAA,iBAAS,IAAGC,KAAK,CAACC,8BAAkB,CAACH,UAAU,EAAE,UACtD,IAAI,CAACI,cAAc,CAAC3Q,KAAKC,KAAKE,UAAUuB,OAAOC;IAEnD;IAEA,MAAcgP,eACZ3Q,GAAoB,EACpBC,GAAqB,EACrBE,QAAgB,EAChBuB,KAAyB,EACzBC,UAA4B,EACL;QACvB,IAAI3D,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACA,+CAA+C;QACjD,OAAO;YACL,4EAA4E;YAC5E,8DAA8D;YAC9D,4HAA4H;YAC5HwC,WAAWiP,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAEnD,IAAI,IAAI,CAAC5C,kBAAkB,CAACC,GAAG,IAAItM,WAAWuH,SAAS,EAAE;gBACvD,OAAO2H,IAAAA,+BAAiB,EACtB7Q,KACAC,KACAE,UACAuB,OACA,kEAAkE;gBAClE,oEAAoE;gBACpE,MACAC,YACA,IAAI,CAACmP,2BAA2B,IAChC,OACA;oBACEC,SAAS,IAAI,CAACA,OAAO;gBACvB;YAEJ;YAEA,qEAAqE;YACrE,oEAAoE;YAEpE,OAAOC,IAAAA,kCAAmB,EACxBhR,IAAIyB,eAAe,EACnBxB,IAAIqD,gBAAgB,EACpBnD,UACAuB,OACAC,YACA;gBACEoP,SAAS,IAAI,CAACA,OAAO;gBACrBlI,cAAc,IAAI,CAACtI,UAAU,CAACsI,YAAY;gBAC1CoI,cAAc,IAAI,CAAClH,aAAa,CAACkH,YAAY,IAAI/N;YACnD,GACA;gBACEJ,YAAY;gBACZoO,aAAavP,WAAWuP,WAAW;gBACnCC,+BAA+B9Q,IAAAA,2BAAc,EAC3CL,KACA;YAEJ;QAEJ;IACF;IAEA,MAAgBwC,eACdxC,GAAoB,EACpBC,GAAqB,EACrBsB,YAA2D,EAC3DW,kBAAyD,EAOxD;QACD,IAAIlE,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO;YACL,MAAM,EAAEqD,cAAc,EAAE4O,kBAAkB,EAAEC,kBAAkB,EAAE,GAC9D5S,QAAQ;YAEV,MAAM6S,oBAAoB,OACxBC,QACAC;gBAEA,IAAID,OAAO1K,GAAG,KAAK7G,IAAI6G,GAAG,EAAE;oBAC1B,MAAM,qBAA+D,CAA/D,IAAI1H,MAAM,CAAC,kDAAkD,CAAC,GAA9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA8D;gBACtE;gBAEA,IAAI,CAAC,IAAI,CAACsS,mBAAmB,EAAE;oBAC7B,MAAM,qBAAkD,CAAlD,IAAItS,MAAM,CAAC,qCAAqC,CAAC,GAAjD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiD;gBACzD;gBAEA,MAAM,IAAI,CAACsS,mBAAmB,CAACF,QAAQC;gBACvC;YACF;YAEA,MAAM,EAAEE,UAAU,EAAEnO,IAAI,EAAE,GAAGhC;YAE7B,MAAMoQ,gBAAgBD,aAClB,MAAMN,mBAAmB7N,QACzB,MAAM8N,mBACJ9N,MACAvD,IAAIyB,eAAe,EACnBxB,IAAIqD,gBAAgB,EACpBgO;YAGN,OAAO9O,eAAemP,eAAepQ,cAAc,IAAI,CAAChB,UAAU,EAAE;gBAClE2H,OAAO,IAAI,CAACvG,UAAU,CAACC,GAAG;gBAC1BM;YACF;QACF;IACF;IAEU0P,YAAYzR,QAAgB,EAAE0O,OAAkB,EAAU;QAClE,OAAO+C,IAAAA,oBAAW,EAChBzR,UACA,IAAI,CAACW,OAAO,EACZ+N,SACA,IAAI,CAACb,kBAAkB,CAACC,GAAG;IAE/B;IAEA,MAAgB4D,oBACdC,GAAsD,EACtDC,gBAAyB,EACzB;QACA,MAAMzN,qBAAqB,IAAI,CAACC,qBAAqB,MAAM,EAAE;QAC7D,IAAID,mBAAmB0N,MAAM,EAAE;YAC7B,MAAMjN,WAAW,IAAI,CAACkN,mBAAmB,CAACH,IAAI3R,QAAQ;YACtD,MAAM+I,YAAYlK,MAAMC,OAAO,CAAC8F;YAEhC,IAAIL,OAAOoN,IAAI3R,QAAQ;YACvB,IAAI+I,WAAW;gBACb,yEAAyE;gBACzExE,OAAOK,QAAQ,CAAC,EAAE;YACpB;YAEA,KAAK,MAAMP,qBAAqBF,mBAAoB;gBAClD,IAAIE,sBAAsBE,MAAM;oBAC9B,MAAM,IAAI,CAACG,eAAe,CAAC;wBACzB7E,KAAK8R,IAAI9R,GAAG;wBACZC,KAAK6R,IAAI7R,GAAG;wBACZyB,OAAOoQ,IAAIpQ,KAAK;wBAChBoD,QAAQgN,IAAInQ,UAAU,CAACmD,MAAM;wBAC7BJ;wBACAK;oBACF;oBACA,OAAO;gBACT;YACF;QACF;QAEA,OAAO,KAAK,CAAC8M,oBAAoBC,KAAKC;IACxC;IAEA,MAAgBG,mBAAmB,EACjCC,MAAM,EACNzN,IAAI,EACJhD,KAAK,EACLoD,MAAM,EACNoE,SAAS,EACTrC,GAAG,EAaJ,EAAwC;QACvC,OAAO2J,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,8BAAkB,CAACwB,kBAAkB,EACrC;YACEE,UAAU;YACVC,YAAY;gBACV,cAAcnJ,YAAYoJ,IAAAA,0BAAgB,EAAC5N,QAAQA;YACrD;QACF,GACA,IACE,IAAI,CAAC6N,sBAAsB,CAAC;gBAC1BJ;gBACAzN;gBACAhD;gBACAoD;gBACAoE;gBACArC;YACF;IAEN;IAEA,MAAc0L,uBAAuB,EACnCJ,MAAM,EACNzN,IAAI,EACJhD,KAAK,EACLoD,MAAM,EACNoE,SAAS,EACTrC,KAAK2L,IAAI,EAQV,EAAwC;QACvC,MAAMC,YAAsB;YAAC/N;SAAK;QAClC,IAAIhD,MAAMgR,GAAG,EAAE;YACb,yCAAyC;YACzCD,UAAUE,OAAO,CACf,AAACzJ,CAAAA,YAAYoJ,IAAAA,0BAAgB,EAAC5N,QAAQkO,IAAAA,oCAAiB,EAAClO,KAAI,IAAK;QAErE;QAEA,IAAIyN,QAAQ;YACVM,UAAUE,OAAO,IACZF,UAAUjJ,GAAG,CAAC,CAACqJ,OAAS,CAAC,CAAC,EAAEV,SAASU,SAAS,MAAM,KAAKA,MAAM;QAEtE;QAEA,KAAK,MAAMC,YAAYL,UAAW;YAChC,IAAI;gBACF,MAAMM,aAAa,MAAM9J,IAAAA,8BAAc,EAAC;oBACtCnI,SAAS,IAAI,CAACA,OAAO;oBACrB4D,MAAMoO;oBACN5J;oBACAhB,OAAO,IAAI,CAACA,KAAK;oBACjBC,YAAY,IAAI,CAACA,UAAU;gBAC7B;gBAEA,IACEgK,UACA,OAAOY,WAAWC,SAAS,KAAK,YAChC,CAACF,SAAS1S,UAAU,CAAC,CAAC,CAAC,EAAE+R,OAAO,CAAC,CAAC,KAClCW,aAAa,CAAC,CAAC,EAAEX,QAAQ,EACzB;oBAGA;gBACF;gBAEA,OAAO;oBACLY;oBACArR,OAAO;wBACL,GAAI,CAAC,IAAI,CAACC,UAAU,CAAC6I,qBAAqB,IAC1CuI,WAAWE,cAAc,GACpB;4BACCP,KAAKhR,MAAMgR,GAAG;wBAChB,IACAhR,KAAK;wBACT,iCAAiC;wBACjC,GAAI,AAACwH,CAAAA,YAAY,CAAC,IAAIpE,MAAK,KAAM,CAAC,CAAC;oBACrC;gBACF;YACF,EAAE,OAAOlB,KAAK;gBACZ,yDAAyD;gBACzD,wBAAwB;gBACxB,IAAI,CAAEA,CAAAA,eAAesP,wBAAiB,AAAD,GAAI;oBACvC,MAAMtP;gBACR;YACF;QACF;QACA,OAAO;IACT;IAEUuP,sBAAoD;QAC5D,OAAOrF,IAAAA,0BAAY,EACjB1D,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAE,UAAUsS,6BAAkB,GAAG;IAEtD;IA2OA,0DAA0D;IAChD1N,0BACR2N,IAAc,EACdC,KAA0E,EACpE;QACN,MAAM,qBAEL,CAFK,IAAInU,MACR,sFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,0DAA0D;IAC1D,MAAgBoU,WAAWC,KAM1B,EAAiB;QAChB,MAAM,qBAEL,CAFK,IAAIrU,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA;;;;;GAKC,GACD,MAAgBoG,iBACdvF,GAAoB,EACpBC,GAAqB,EACrByB,KAAqB,EACrB0C,KAAyB,EACP;QAClB,OAAO,IAAI,CAACsL,MAAM,CAAC1P,KAAKC,KAAKyB,OAAO0C;IACtC;IAEU4I,qBAA8B;QACtC,OAAOyG,qBAAM;IACf;IAEUC,aACR1T,GAAsC,EACrB;QACjB,OAAO,CAAEA,CAAAA,eAAe2T,qBAAe,AAAD,IAAK,IAAIA,qBAAe,CAAC3T,OAAOA;IACxE;IAEU4T,aACR3T,GAAsC,EACpB;QAClB,OAAO,CAAEA,CAAAA,eAAe4T,sBAAgB,AAAD,IAAK,IAAIA,sBAAgB,CAAC5T,OAAOA;IAC1E;IAEO6T,oBAAwC;QAC7C,MAAMzH,UAAU,IAAI,CAAC0H,kBAAkB;QACvC,IAAI,IAAI,CAAChK,aAAa,CAACC,qBAAqB,EAAE;YAC5C,MAAM,EACJgK,sBAAsB,EACvB,GAAGvV,QAAQ;YACZ,OAAOuV,uBAAuB3H;QAChC;QACA,OAAOA;IACT;IAEQ0H,qBAAyC;QAC/C,4EAA4E;QAC5E,2EAA2E;QAC3E,sEAAsE;QACtE,uEAAuE;QACvE,IAAI,CAACxJ,OAAO,GAAGpB,KAAK,CAAC,CAACvF;YACpBoE,QAAQF,KAAK,CAAC,4BAA4BlE;QAC5C;QAEA,MAAMyI,UAAU,KAAK,CAACyH;QAEtB,OAAO,CAAC9T,KAAKC,KAAKC,YAChBmM,QAAQ,IAAI,CAACqH,YAAY,CAAC1T,MAAM,IAAI,CAAC4T,YAAY,CAAC3T,MAAMC;IAC5D;IAEA,MAAa8C,WAAW,EACtBiR,OAAO,EACPC,iBAAiB,EACjBC,IAAI,EAKL,EAAE;QACD,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxCxN,KAAKoN;YACL3M,SAAS4M;QACX;QAEA,MAAM7H,UAAU,IAAI,CAACyH,iBAAiB;QACtC,MAAMzH,QACJ,IAAIsH,qBAAe,CAACS,OAAOpU,GAAG,GAC9B,IAAI6T,sBAAgB,CAACO,OAAOnU,GAAG;QAEjC,MAAMmU,OAAOnU,GAAG,CAACqU,WAAW;QAE5B,IACEF,OAAOnU,GAAG,CAACsU,SAAS,CAAC,sBAAsB,iBAC3CH,OAAOnU,GAAG,CAACQ,UAAU,KAAK,OAC1B,CAAE2T,CAAAA,OAAOnU,GAAG,CAACQ,UAAU,KAAK,OAAO0T,KAAKK,sBAAsB,AAAD,GAC7D;YACA,MAAM,qBAAsD,CAAtD,IAAIrV,MAAM,CAAC,iBAAiB,EAAEiV,OAAOnU,GAAG,CAACQ,UAAU,EAAE,GAArD,qBAAA;uBAAA;4BAAA;8BAAA;YAAqD;QAC7D;IACF;IAEA,MAAa4D,OACXrE,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAA0B,EAC1BxB,SAAkC,EAClCuU,WAAW,KAAK,EACD;QACf,OAAO,KAAK,CAACpQ,OACX,IAAI,CAACqP,YAAY,CAAC1T,MAClB,IAAI,CAAC4T,YAAY,CAAC3T,MAClBE,UACAuB,OACAxB,WACAuU;IAEJ;IAEA,MAAaC,aACX1U,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACgT,aACX,IAAI,CAAChB,YAAY,CAAC1T,MAClB,IAAI,CAAC4T,YAAY,CAAC3T,MAClBE,UACAuB;IAEJ;IAEA,MAAgBiT,0BACd7C,GAAsD,EACtDlO,GAAiB,EACjB;QACA,MAAM,EAAE5D,GAAG,EAAEC,GAAG,EAAEyB,KAAK,EAAE,GAAGoQ;QAC5B,MAAM8C,QAAQ3U,IAAIQ,UAAU,KAAK;QAEjC,IAAImU,SAAS,IAAI,CAAC5G,kBAAkB,CAACC,GAAG,EAAE;YACxC,IAAI,IAAI,CAACtM,UAAU,CAACC,GAAG,EAAE;gBACvB,MAAM,IAAI,CAAC2R,UAAU,CAAC;oBACpB7O,MAAMmQ,2CAAgC;oBACtCC,YAAY;oBACZjO,KAAK7G,IAAI6G,GAAG;gBACd,GAAGsC,KAAK,CAAC,KAAO;YAClB;YAEA,IACE,IAAI,CAAC5E,qBAAqB,GAAGwQ,QAAQ,CAACF,2CAAgC,GACtE;gBACA,MAAM,IAAI,CAAChQ,eAAe,CAAC;oBACzB7E;oBACAC;oBACAyB,OAAOA,SAAS,CAAC;oBACjBoD,QAAQ,CAAC;oBACTJ,MAAMmQ,2CAAgC;oBACtC9P,UAAU;gBACZ;gBACA,OAAO;YACT;QACF;QACA,OAAO,KAAK,CAAC4P,0BAA0B7C,KAAKlO;IAC9C;IAEA,MAAagC,YACXhC,GAAiB,EACjB5D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAA0B,EAC1BsT,UAAoB,EACL;QACf,OAAO,KAAK,CAACpP,YACXhC,KACA,IAAI,CAAC8P,YAAY,CAAC1T,MAClB,IAAI,CAAC4T,YAAY,CAAC3T,MAClBE,UACAuB,OACAsT;IAEJ;IAEA,MAAaC,kBACXrR,GAAiB,EACjB5D,GAAsC,EACtCC,GAAsC,EACtCE,QAAgB,EAChBuB,KAAsB,EACE;QACxB,OAAO,KAAK,CAACuT,kBACXrR,KACA,IAAI,CAAC8P,YAAY,CAAC1T,MAClB,IAAI,CAAC4T,YAAY,CAAC3T,MAClBE,UACAuB;IAEJ;IAEA,MAAaJ,UACXtB,GAAsC,EACtCC,GAAsC,EACtCC,SAAkC,EAClC8U,UAAoB,EACL;QACf,OAAO,KAAK,CAAC1T,UACX,IAAI,CAACoS,YAAY,CAAC1T,MAClB,IAAI,CAAC4T,YAAY,CAAC3T,MAClBC,WACA8U;IAEJ;IAEUE,wBAAmD;QAC3D,IAAI,IAAI,CAAC5U,WAAW,EAAE;YACpB,OAAO;QACT,OAAO;YACL,MAAM6U,WAA+B1W,QAAQ,IAAI,CAAC0L,sBAAsB;YACxE,OAAOgL;QACT;IACF;IAEA,yDAAyD,GACzD,MAAgBjP,gBAA4D;YAEvDiP;QADnB,MAAMA,WAAW,IAAI,CAACD,qBAAqB;QAC3C,MAAMjP,aAAakP,6BAAAA,uBAAAA,SAAUlP,UAAU,qBAApBkP,oBAAsB,CAAC,IAAI;QAC9C,IAAI,CAAClP,YAAY;YACf,MAAMmP,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEtD,IAAID,kBAAkB;oBAGhBA;gBAFJ,OAAO;oBACLhR,OAAO7E,IAAAA,iDAAyB,EAC9B6V,EAAAA,2BAAAA,iBAAiBE,MAAM,qBAAvBF,yBAAyBlW,QAAQ,KAAI;wBACnC;4BAAEqW,QAAQ;4BAAMC,gBAAgB;wBAAU;qBAC3C;oBAEH9Q,MAAM;gBACR;YACF;YAEA;QACF;QAEA,OAAO;YACLN,OAAOxF,qBAAqBqH;YAC5BvB,MAAM;QACR;IACF;IAEUH,wBAAkC;QAC1C,MAAM4Q,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO,EAAE;QACX;QAEA,OAAOhO,OAAO4D,IAAI,CAACoK,SAASM,SAAS;IACvC;IAEA;;;;GAIC,GACD,AAAUC,oBAAoB5Q,MAI7B,EAMQ;QACP,MAAMqQ,WAAW,IAAI,CAACD,qBAAqB;QAC3C,IAAI,CAACC,UAAU;YACb,OAAO;QACT;QAEA,IAAIQ;QAEJ,IAAI;YACFA,YAAYC,IAAAA,wCAAmB,EAAChD,IAAAA,oCAAiB,EAAC9N,OAAOJ,IAAI;QAC/D,EAAE,OAAOd,KAAK;YACZ,OAAO;QACT;QAEA,IAAIiS,WAAW/Q,OAAOmB,UAAU,GAC5BkP,SAASlP,UAAU,CAAC0P,UAAU,GAC9BR,SAASM,SAAS,CAACE,UAAU;QAEjC,IAAI,CAACE,UAAU;YACb,IAAI,CAAC/Q,OAAOmB,UAAU,EAAE;gBACtB,MAAM,IAAIiN,wBAAiB,CAACyC;YAC9B;YACA,OAAO;QACT;QAEA,OAAO;YACLG,MAAMD,SAASC,IAAI;YACnBC,OAAOF,SAASG,KAAK,CAACxM,GAAG,CAAC,CAACyM,OAAS7L,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAEmV;YACvDC,MAAM,AAACL,CAAAA,SAASK,IAAI,IAAI,EAAE,AAAD,EAAG1M,GAAG,CAAC,CAAC2M,UAAa,CAAA;oBAC5C,GAAGA,OAAO;oBACVC,UAAUhM,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAEqV,QAAQC,QAAQ;gBAC/C,CAAA;YACAC,QACER,SAASQ,MAAM,IACfR,SAASQ,MAAM,CAAC7M,GAAG,CAAC,CAAC2M;gBACnB,OAAO;oBACL,GAAGA,OAAO;oBACVC,UAAUhM,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAEqV,QAAQC,QAAQ;gBAC/C;YACF;YACFnY,KAAK4X,SAAS5X,GAAG;QACnB;IACF;IAEA,MAAcoX,qBAAqB;QACjC,IAAI,CAAC,IAAI,CAAC9U,UAAU,CAAC8H,YAAY,CAACiO,cAAc,EAAE;YAChD;QACF;QAEA,IAAI;gBAKyBC;YAJ3B,MAAMA,kBAAkB,IAAI,CAAC5U,UAAU,CAACC,GAAG,GACvC,CAAC,IACDnD,QAAQ2L,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAE,UAAU0V,oCAAyB;YAElE,IAAI,IAAI,CAAC7U,UAAU,CAACC,GAAG,KAAI2U,oCAAAA,6BAAAA,gBAAiBd,SAAS,qBAA1Bc,0BAA4B,CAAC,eAAe,GAAE;gBACvE,uDAAuD;gBACvD,OAAO9X,QAAQ2L,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAE,UAAU;YAC9C;QACF,EAAE,OAAO8C,KAAK;YACZ,IACE+D,IAAAA,gBAAO,EAAC/D,QACRA,IAAIgE,IAAI,KAAK,YACbhE,IAAIgE,IAAI,KAAK,oBACb;gBACA,MAAMhE;YACR;QACF;IACF;IAEA;;;;GAIC,GACD,MAAgB6S,cAActW,QAAgB,EAAoB;QAChE,MAAMtB,OAAO,IAAI,CAAC6W,mBAAmB,CAAC;YAAEhR,MAAMvE;YAAU8F,YAAY;QAAK;QACzE,MAAMqQ,iBAAiB,MAAM,IAAI,CAACjB,kBAAkB;QAEpD,IAAI,CAACxW,QAAQyX,gBAAgB;YAC3B,OAAO;QACT;QACA,OAAO3S,QAAQ9E,QAAQA,KAAKkX,KAAK,CAAC/D,MAAM,GAAG;IAC7C;IAEA;;;;GAIC,GACD,MAAgBpL,iBAAiB4L,IAAa,EAAE,CAAC;IACjD,MAAgBkE,mBAAmBC,OAIlC,EAAE,CAAC;IAEJ;;;;;GAKC,GACD,MAAgB7P,cAAchC,MAM7B,EAAE;QACD,IAAI9G,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,kEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,0DAA0D;QAC1D,IACEyX,IAAAA,mCAAyB,EAAC9R,OAAOiC,OAAO,EAAE,IAAI,CAACpF,UAAU,CAACqO,YAAY,EACnE6G,oBAAoB,EACvB;YACA,OAAO;gBACL7P,UAAU,IAAI8P,SAAS,MAAM;oBAAExP,SAAS;wBAAE,qBAAqB;oBAAI;gBAAE;YACvE;QACF;QAEA,IAAIT;QAEJ,IAAI,IAAI,CAACtG,UAAU,CAACwW,0BAA0B,EAAE;YAC9ClQ,MAAMxG,IAAAA,2BAAc,EAACyE,OAAOiC,OAAO,EAAE;QACvC,OAAO;YACL,mEAAmE;YACnE,MAAMrF,QAAQsV,IAAAA,mCAAsB,EAAClS,OAAOgB,MAAM,CAACpE,KAAK,EAAEuV,QAAQ;YAClE,MAAM9E,SAAS9R,IAAAA,2BAAc,EAACyE,OAAOiC,OAAO,EAAE;YAE9CF,MAAM,GAAGxG,IAAAA,2BAAc,EAACyE,OAAOiC,OAAO,EAAE,gBAAgB,GAAG,EACzD,IAAI,CAACqJ,aAAa,IAAI,YACvB,CAAC,EAAE,IAAI,CAAC8G,IAAI,GAAG/E,SAAS,CAAC,CAAC,EAAEA,QAAQ,GAAG,KAAKrN,OAAOgB,MAAM,CAAC3F,QAAQ,GACjEuB,QAAQ,CAAC,CAAC,EAAEA,OAAO,GAAG,IACtB;QACJ;QAEA,IAAI,CAACmF,IAAIzG,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIjB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMuF,OAGF,CAAC;QAEL,MAAMuB,aAAa,MAAM,IAAI,CAACC,aAAa;QAC3C,IAAI,CAACD,YAAY;YACf,OAAO;gBAAEgC,UAAU;YAAM;QAC3B;QACA,IAAI,CAAE,MAAM,IAAI,CAACwO,aAAa,CAACxQ,WAAWvB,IAAI,GAAI;YAChD,OAAO;gBAAEuD,UAAU;YAAM;QAC3B;QAEA,MAAM,IAAI,CAACrB,gBAAgB,CAAC9B,OAAOiC,OAAO,CAACF,GAAG;QAC9C,MAAMsQ,iBAAiB,IAAI,CAACzB,mBAAmB,CAAC;YAC9ChR,MAAMuB,WAAWvB,IAAI;YACrBuB,YAAY;QACd;QAEA,MAAMmR,SAAS,AAACtS,CAAAA,OAAOiC,OAAO,CAACqQ,MAAM,IAAI,KAAI,EAAGC,WAAW;QAC3D,MAAMC,cAAc;YAClBhQ,SAASxC,OAAOiC,OAAO,CAACO,OAAO;YAC/B8P;YACA7W,YAAY;gBACVgX,UAAU,IAAI,CAAChX,UAAU,CAACgX,QAAQ;gBAClCtT,MAAM,IAAI,CAAC1D,UAAU,CAAC0D,IAAI;gBAC1BuT,eAAe,IAAI,CAACjX,UAAU,CAACiX,aAAa;gBAC5CnP,cAAc,IAAI,CAAC9H,UAAU,CAAC8H,YAAY;YAC5C;YACAxB,KAAKA;YACLnC;YACAhE,MACE0W,WAAW,SAASA,WAAW,SAC1B/W,IAAAA,2BAAc,EAACyE,OAAOiC,OAAO,EAAE,kBAChC7D;YAENuU,QAAQC,IAAAA,mCAAsB,EAAC5S,OAAOkC,QAAQ,CAAC1D,gBAAgB;YAC/DqU,WAAW,IAAI,CAACC,YAAY;QAC9B;QACA,IAAIlR;QAIJ,qDAAqD;QACrD,8DAA8D;QAC9D,6DAA6D;QAC7D,6DAA6D;QAC7D,uBAAuB;QACvB,IAAI,CAACyQ,gBAAgB;YACnB,IAAI/B;YACJA,mBAAmB,MAAM,IAAI,CAACC,kBAAkB;YAEhD,IAAI,CAACD,kBAAkB;gBACrB,MAAM,IAAIyC,8BAAuB;YACnC;YACA,MAAMC,YACJ1C,iBAAiB9W,OAAO,IAAI8W;YAE9B1O,SAAS,MAAMoR,UAAU;gBACvBzL,SAAS+I,iBAAiBnP,UAAU,IAAImP;gBACxCrO,SAASuQ;gBACT5S,MAAM;YACR;QACF,OAAO;YACL,MAAM,EAAEqT,GAAG,EAAE,GAAGtZ,QAAQ;YAExBiI,SAAS,MAAMqR,IAAI;gBACjBjX,SAAS,IAAI,CAACA,OAAO;gBACrBgV,MAAMqB,eAAerB,IAAI;gBACzBC,OAAOoB,eAAepB,KAAK;gBAC3BiC,mBAAmBb;gBACnBpQ,SAASuQ;gBACTW,UAAU;gBACVC,WAAWpT,OAAOoT,SAAS;YAC7B;QACF;QAEA,IAAI,CAAC,IAAI,CAACvW,UAAU,CAACC,GAAG,EAAE;YACxB8E,OAAOiR,SAAS,CAACxO,KAAK,CAAC,CAACrB;gBACtBE,QAAQF,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAEA;YAC1D;QACF;QAEA,IAAI,CAACpB,QAAQ;YACX,IAAI,CAACpF,SAAS,CAACwD,OAAOiC,OAAO,EAAEjC,OAAOkC,QAAQ,EAAElC,OAAOgB,MAAM;YAC7D,OAAO;gBAAEmC,UAAU;YAAK;QAC1B;QAEA,sDAAsD;QACtD,IAAIvB,OAAOM,QAAQ,CAACM,OAAO,CAAC6Q,GAAG,CAAC,eAAe;YAC7C,MAAMC,UAAU1R,OAAOM,QAAQ,CAACM,OAAO,CACpC+Q,YAAY,GACZC,OAAO,CAAC,CAACC,sBACRC,IAAAA,0BAAkB,EAACD;YAGvB,2BAA2B;YAC3B7R,OAAOM,QAAQ,CAACM,OAAO,CAACmR,MAAM,CAAC;YAE/B,mCAAmC;YACnC,KAAK,MAAMC,UAAUN,QAAS;gBAC5B1R,OAAOM,QAAQ,CAACM,OAAO,CAACqR,MAAM,CAAC,cAAcD;YAC/C;YAEA,+BAA+B;YAC/B3U,IAAAA,2BAAc,EAACe,OAAOiC,OAAO,EAAE,oBAAoBqR;QACrD;QAEA,OAAO1R;IACT;IAmHU6G,uBAA0C;YAKhD,kBACA;QALF,IAAI,IAAI,CAACqL,sBAAsB,EAAE;YAC/B,OAAO,IAAI,CAACA,sBAAsB;QACpC;QACA,IACE,EAAA,mBAAA,IAAI,CAACjX,UAAU,qBAAf,iBAAiBC,GAAG,OACpB,sBAAA,IAAI,CAACmI,aAAa,qBAAlB,oBAAoBnI,GAAG,KACvB5D,QAAQC,GAAG,CAAC4a,QAAQ,KAAK,iBACzB7a,QAAQC,GAAG,CAAC6a,UAAU,KAAKC,iCAAsB,EACjD;YACA,IAAI,CAACH,sBAAsB,GAAG;gBAC5BI,SAAS;gBACTC,QAAQ,CAAC;gBACT3P,eAAe,CAAC;gBAChB4P,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe3a,QAAQ,UAAU4a,WAAW,CAAC,IAAIpC,QAAQ,CAAC;oBAC1DqC,uBAAuB7a,QAAQ,UAC5B4a,WAAW,CAAC,IACZpC,QAAQ,CAAC;oBACZsC,0BAA0B9a,QAAQ,UAC/B4a,WAAW,CAAC,IACZpC,QAAQ,CAAC;gBACd;YACF;YACA,OAAO,IAAI,CAAC2B,sBAAsB;QACpC;QAEA,IAAI,CAACA,sBAAsB,GAAG9K,IAAAA,0BAAY,EACxC1D,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAE0Y,6BAAkB;QAGvC,OAAO,IAAI,CAACZ,sBAAsB;IACpC;IAEUrP,oBAAyD;QACjE,OAAOiH,IAAAA,iBAAS,IAAGC,KAAK,CAACC,8BAAkB,CAACnH,iBAAiB,EAAE;YAC7D,MAAM4L,WAAWrH,IAAAA,0BAAY,EAAC1D,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAE2Y,0BAAe;YAEhE,IAAIpL,WAAW8G,SAAS9G,QAAQ,IAAI;gBAClCC,aAAa,EAAE;gBACfoL,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YAEA,IAAI3a,MAAMC,OAAO,CAACoP,WAAW;gBAC3BA,WAAW;oBACTC,aAAa,EAAE;oBACfoL,YAAYrL;oBACZsL,UAAU,EAAE;gBACd;YACF;YAEA,OAAO;gBAAE,GAAGxE,QAAQ;gBAAE9G;YAAS;QACjC;IACF;IAEUuL,kBACR5Z,GAAoB,EACpBE,SAAiC,EACjC2Z,YAAsB,EACtB;YAEiB7Z;QADjB,6BAA6B;QAC7B,MAAM8Z,WAAW9Z,EAAAA,+BAAAA,IAAIsH,OAAO,CAAC,oBAAoB,qBAAhCtH,6BAAkC+U,QAAQ,CAAC,YACxD,UACA;QAEJ,4DAA4D;QAC5D,MAAM5O,UACJ,IAAI,CAACiK,aAAa,IAAI,IAAI,CAAC8G,IAAI,GAC3B,GAAG4C,SAAS,GAAG,EAAE,IAAI,CAAC1J,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC8G,IAAI,GAAGlX,IAAI6G,GAAG,EAAE,GAC5D,IAAI,CAACtG,UAAU,CAAC8H,YAAY,CAAC6H,eAAe,GAC1C,CAAC,QAAQ,EAAElQ,IAAIsH,OAAO,CAACyS,IAAI,IAAI,cAAc/Z,IAAI6G,GAAG,EAAE,GACtD7G,IAAI6G,GAAG;QAEf9C,IAAAA,2BAAc,EAAC/D,KAAK,WAAWmG;QAC/BpC,IAAAA,2BAAc,EAAC/D,KAAK,aAAa;YAAE,GAAGE,UAAUwB,KAAK;QAAC;QACtDqC,IAAAA,2BAAc,EAAC/D,KAAK,gBAAgB8Z;QAEpC,IAAI,CAACD,cAAc;YACjB9V,IAAAA,2BAAc,EAAC/D,KAAK,gBAAgBga,IAAAA,6BAAgB,EAACha,IAAIyB,eAAe;QAC1E;IACF;IAEA,MAAgBoD,gBAAgBC,MAU/B,EAAoC;QACnC,IAAI9G,QAAQC,GAAG,CAACC,YAAY,EAAE;YAC5B,MAAM,qBAEL,CAFK,IAAIiB,MACR,wGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI8a;QAEJ,MAAM,EAAEvY,KAAK,EAAEgD,IAAI,EAAEN,KAAK,EAAE,GAAGU;QAE/B,IAAI,CAACV,OACH,MAAM,IAAI,CAACsS,kBAAkB,CAAC;YAC5BhS;YACAK,UAAUD,OAAOC,QAAQ;YACzB8B,KAAK/B,OAAO9E,GAAG,CAAC6G,GAAG;QACrB;QACFoT,WAAW,IAAI,CAACvE,mBAAmB,CAAC;YAClChR;YACAuB,YAAY;QACd;QAEA,IAAI,CAACgU,UAAU;YACb,OAAO;QACT;QAEA,6DAA6D;QAC7D,MAAMC,oBAAoB7Z,IAAAA,2BAAc,EAACyE,OAAO9E,GAAG,EAAE;QACrD,MAAMma,aAAa,IAAIC,IACrB/Z,IAAAA,2BAAc,EAACyE,OAAO9E,GAAG,EAAE,cAAc,KACzC;QAEF,MAAMqa,cAAcrD,IAAAA,mCAAsB,EAAC;YACzC,GAAG7P,OAAOmT,WAAW,CAACH,WAAWI,YAAY,CAAC;YAC9C,GAAG7Y,KAAK;YACR,GAAGoD,OAAOA,MAAM;QAClB,GAAGmS,QAAQ;QAEX,IAAIiD,mBAAmB;YACrBpV,OAAO9E,GAAG,CAACsH,OAAO,CAAC,gBAAgB,GAAG;QACxC;QACA6S,WAAWK,MAAM,GAAGH;QACpB,MAAMxT,MAAMsT,WAAWlD,QAAQ;QAE/B,IAAI,CAACpQ,IAAIzG,UAAU,CAAC,SAAS;YAC3B,MAAM,qBAEL,CAFK,IAAIjB,MACR,qFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAE4Y,GAAG,EAAE,GAAGtZ,QAAQ;QACxB,MAAMiI,SAAS,MAAMqR,IAAI;YACvBjX,SAAS,IAAI,CAACA,OAAO;YACrBgV,MAAMmE,SAASnE,IAAI;YACnBC,OAAOkE,SAASlE,KAAK;YACrBiC,mBAAmBiC;YACnBlT,SAAS;gBACPO,SAASxC,OAAO9E,GAAG,CAACsH,OAAO;gBAC3B8P,QAAQtS,OAAO9E,GAAG,CAACoX,MAAM;gBACzB7W,YAAY;oBACVgX,UAAU,IAAI,CAAChX,UAAU,CAACgX,QAAQ;oBAClCtT,MAAM,IAAI,CAAC1D,UAAU,CAAC0D,IAAI;oBAC1BuT,eAAe,IAAI,CAACjX,UAAU,CAACiX,aAAa;gBAC9C;gBACA3Q;gBACAnC,MAAM;oBACJoR,MAAMhR,OAAOJ,IAAI;oBACjB,GAAII,OAAOA,MAAM,IAAI;wBAAEA,QAAQA,OAAOA,MAAM;oBAAC,CAAC;gBAChD;gBACApE,MAAML,IAAAA,2BAAc,EAACyE,OAAO9E,GAAG,EAAE;gBACjCyX,QAAQC,IAAAA,mCAAsB,EAAC5S,OAAO7E,GAAG,CAACqD,gBAAgB;gBAC1DqU,WAAW,IAAI,CAACC,YAAY;YAC9B;YACAK,UAAU;YACV5H,SAASvL,OAAOuL,OAAO;YACvB6H,WAAWpT,OAAOoT,SAAS;YAC3B7U,kBACE,AAACoX,WAAmBC,kBAAkB,IACtCra,IAAAA,2BAAc,EAACyE,OAAO9E,GAAG,EAAE;YAC7B2a,0BAA0Bta,IAAAA,2BAAc,EACtCyE,OAAO9E,GAAG,EACV;QAEJ;QAEA,IAAI0G,OAAOkU,YAAY,EAAE;YACvB9V,OAAO9E,GAAG,CAAC4a,YAAY,GAAGlU,OAAOkU,YAAY;QAC/C;QAEA,IAAI,CAAC9V,OAAO7E,GAAG,CAACQ,UAAU,IAAIqE,OAAO7E,GAAG,CAACQ,UAAU,GAAG,KAAK;YACzDqE,OAAO7E,GAAG,CAACQ,UAAU,GAAGiG,OAAOM,QAAQ,CAACQ,MAAM;YAC9C1C,OAAO7E,GAAG,CAAC4a,aAAa,GAAGnU,OAAOM,QAAQ,CAAC8T,UAAU;QACvD;QAEA,8CAA8C;QAE9CpU,OAAOM,QAAQ,CAACM,OAAO,CAACyT,OAAO,CAAC,CAACtY,OAAOyE;YACtC,yDAAyD;YACzD,IAAIA,IAAI8T,WAAW,OAAO,cAAc;gBACtC,qFAAqF;gBACrF,KAAK,MAAMtC,UAAUF,IAAAA,0BAAkB,EAAC/V,OAAQ;oBAC9CqC,OAAO7E,GAAG,CAACgb,YAAY,CAAC/T,KAAKwR;gBAC/B;YACF,OAAO;gBACL5T,OAAO7E,GAAG,CAACgb,YAAY,CAAC/T,KAAKzE;YAC/B;QACF;QAEA,MAAM,EAAEa,gBAAgB,EAAE,GAAGwB,OAAO7E,GAAG;QACvC,IAAIyG,OAAOM,QAAQ,CAACtG,IAAI,EAAE;YACxB,MAAM+G,IAAAA,gCAAkB,EAACf,OAAOM,QAAQ,CAACtG,IAAI,EAAE4C;QACjD,OAAO;YACLA,iBAAiBoE,GAAG;QACtB;QAEA,OAAOhB;IACT;IAEA,IAAc2D,gBAAwB;QACpC,IAAI,IAAI,CAAC6Q,cAAc,EAAE;YACvB,OAAO,IAAI,CAACA,cAAc;QAC5B;QACA,MAAM7Q,gBAAgBD,IAAAA,UAAI,EAAC,IAAI,CAACtJ,OAAO,EAAEqa,2BAAgB;QACzD,IAAI,CAACD,cAAc,GAAG7Q;QACtB,OAAOA;IACT;IAEA,MAAgB+Q,2BACd5I,IAAa,EAC6B;QAC1C,uEAAuE;QACvE,sBAAsB;QACtB,OAAO;IACT;IAEA,MAAgBvN,8BACd,GAAGoW,IAAqD,EACxD;QACA,MAAM,KAAK,CAACpW,iCAAiCoW;QAE7C,uFAAuF;QACvF,IAAI,CAAC,IAAI,CAAC1Z,UAAU,CAACC,GAAG,EAAE;YACxB,IAAI,CAAC+D,QAAQ,CAAC0V,IAAI,CAAC,EAAE;QACvB;IACF;IAEUC,cAAcC,QAA6B,EAAE;QACrD,IAAI,CAAC1b,gBAAgB,CAAC2b,GAAG,CAACD;IAC5B;IAEA,MAAME,QAAuB;QAC3B,MAAM,IAAI,CAAC5b,gBAAgB,CAAC6b,MAAM;IACpC;IAEUC,uBAAkC;QAC1C,IAAI,CAACC,iBAAiB,KAAK,IAAI,CAACC,uBAAuB;QACvD,OAAO,IAAI,CAACD,iBAAiB;IAC/B;IAEQC,0BAA0B;QAChC,IAAI,IAAI,CAACvb,WAAW,EAAE;YACpB,MAAM,qBAEL,CAFK,IAAIwb,8BAAc,CACtB,mEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,UAAU,IAAIC,oBAAW,CAAC;YAAE3L,SAASrI,QAAQF,KAAK;QAAC;QAEzD,kEAAkE;QAClE,IAAI,CAACwT,aAAa,CAAC,IAAMS,QAAQE,QAAQ;QAEzC,OAAOF,QAAQpE,SAAS;IAC1B;AACF"}