{"version": 3, "sources": ["../../src/telemetry/flush-and-exit.ts"], "sourcesContent": ["import { traceGlobals } from '../trace/shared'\n\nexport async function flushAndExit(code: number) {\n  let telemetry = traceGlobals.get('telemetry') as\n    | InstanceType<typeof import('./storage').Telemetry>\n    | undefined\n  if (telemetry) {\n    await telemetry.flush()\n  }\n  process.exit(code)\n}\n"], "names": ["flushAndExit", "code", "telemetry", "traceGlobals", "get", "flush", "process", "exit"], "mappings": ";;;;+BAEsBA;;;eAAAA;;;wBAFO;AAEtB,eAAeA,aAAaC,IAAY;IAC7C,IAAIC,YAAYC,oBAAY,CAACC,GAAG,CAAC;IAGjC,IAAIF,WAAW;QACb,MAAMA,UAAUG,KAAK;IACvB;IACAC,QAAQC,IAAI,CAACN;AACf"}