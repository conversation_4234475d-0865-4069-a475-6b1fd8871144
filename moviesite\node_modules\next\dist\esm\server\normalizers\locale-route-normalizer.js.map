{"version": 3, "sources": ["../../../src/server/normalizers/locale-route-normalizer.ts"], "sourcesContent": ["import type { I18NProvider } from '../lib/i18n-provider'\nimport type { Normalizer } from './normalizer'\n\n/**\n * Normalizes the pathname by removing the locale prefix if any.\n */\nexport class LocaleRouteNormalizer implements Normalizer {\n  constructor(private readonly provider: I18NProvider) {}\n\n  /**\n   * Normalizes the pathname by removing the locale prefix if any.\n   *\n   * @param pathname The pathname to normalize.\n   * @returns The pathname without the locale prefix (if any).\n   */\n  public normalize(pathname: string): string {\n    const match = this.provider.analyze(pathname)\n    return match.pathname\n  }\n}\n"], "names": ["LocaleRouteNormalizer", "constructor", "provider", "normalize", "pathname", "match", "analyze"], "mappings": "AAGA;;CAEC,GACD,OAAO,MAAMA;IACXC,YAAY,AAAiBC,QAAsB,CAAE;aAAxBA,WAAAA;IAAyB;IAEtD;;;;;GAKC,GACD,AAAOC,UAAUC,QAAgB,EAAU;QACzC,MAAMC,QAAQ,IAAI,CAACH,QAAQ,CAACI,OAAO,CAACF;QACpC,OAAOC,MAAMD,QAAQ;IACvB;AACF"}