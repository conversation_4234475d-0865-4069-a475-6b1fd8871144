'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import axios from 'axios';

interface Movie {
  id: number;
  title: string;
  overview: string;
  poster_path: string | null;
  release_date: string;
  vote_average: number;
}

interface TVShow {
  id: number;
  name: string;
  overview: string;
  poster_path: string | null;
  first_air_date: string;
  vote_average: number;
}

export default function Home() {
  const [movies, setMovies] = useState<Movie[]>([]);
  const [tvShows, setTVShows] = useState<TVShow[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    const fetchContent = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_TMDB_API_KEY;
        if (apiKey && apiKey !== 'your_tmdb_api_key_here') {
          const [moviesResponse, tvResponse] = await Promise.all([
            axios.get(`https://api.themoviedb.org/3/movie/popular?api_key=${apiKey}`),
            axios.get(`https://api.themoviedb.org/3/tv/popular?api_key=${apiKey}`)
          ]);
          setMovies(moviesResponse.data.results.slice(0, 12));
          setTVShows(tvResponse.data.results.slice(0, 12));
        } else {
          // Mock data for demo purposes
          const mockMovies = Array.from({ length: 12 }, (_, i) => ({
            id: i + 1,
            title: `Movie ${i + 1}`,
            overview: `This is a sample movie ${i + 1} for demonstration purposes.`,
            poster_path: null,
            release_date: '2024-01-01',
            vote_average: 7.5 + (i % 3)
          }));

          const mockTVShows = Array.from({ length: 12 }, (_, i) => ({
            id: i + 1,
            name: `TV Show ${i + 1}`,
            overview: `This is a sample TV show ${i + 1} for demonstration purposes.`,
            poster_path: null,
            first_air_date: '2024-01-01',
            vote_average: 8.0 + (i % 3)
          }));

          setMovies(mockMovies);
          setTVShows(mockTVShows);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  const filteredMovies = movies.filter(movie =>
    movie.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredTVShows = tvShows.filter(show =>
    show.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-4">Welcome to MovieSite</h1>
          <p className="text-xl mb-8">Stream your favorite movies and TV shows</p>

          {/* Search Bar */}
          <div className="max-w-md mx-auto">
            <input
              type="text"
              placeholder="Search movies and TV shows..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-3 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Movies Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-6">Popular Movies</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {filteredMovies.map((movie) => (
              <Link key={movie.id} href={`/movie/${movie.id}`}>
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:scale-105 transition-transform cursor-pointer">
                  {movie.poster_path ? (
                    <img
                      src={`https://image.tmdb.org/t/p/w300${movie.poster_path}`}
                      alt={movie.title}
                      className="w-full h-64 object-cover"
                    />
                  ) : (
                    <div className="w-full h-64 bg-gray-700 flex items-center justify-center">
                      <span className="text-gray-400">No Image</span>
                    </div>
                  )}
                  <div className="p-3">
                    <h3 className="font-semibold text-sm mb-1 truncate">{movie.title}</h3>
                    <p className="text-xs text-gray-400">
                      {new Date(movie.release_date).getFullYear()} • ⭐ {movie.vote_average.toFixed(1)}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* TV Shows Section */}
        <section>
          <h2 className="text-3xl font-bold mb-6">Popular TV Shows</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {filteredTVShows.map((show) => (
              <Link key={show.id} href={`/watch/${show.id}?season=1&episode=1`}>
                <div className="bg-gray-800 rounded-lg overflow-hidden hover:scale-105 transition-transform cursor-pointer">
                  {show.poster_path ? (
                    <img
                      src={`https://image.tmdb.org/t/p/w300${show.poster_path}`}
                      alt={show.name}
                      className="w-full h-64 object-cover"
                    />
                  ) : (
                    <div className="w-full h-64 bg-gray-700 flex items-center justify-center">
                      <span className="text-gray-400">No Image</span>
                    </div>
                  )}
                  <div className="p-3">
                    <h3 className="font-semibold text-sm mb-1 truncate">{show.name}</h3>
                    <p className="text-xs text-gray-400">
                      {new Date(show.first_air_date).getFullYear()} • ⭐ {show.vote_average.toFixed(1)}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
