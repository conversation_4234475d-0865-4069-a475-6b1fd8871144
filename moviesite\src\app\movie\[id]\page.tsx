'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import axios from 'axios';

interface Movie {
  id: number;
  title: string;
  overview: string;
  poster_path: string | null;
  backdrop_path: string | null;
  release_date: string;
  vote_average: number;
  runtime: number;
  genres: { id: number; name: string }[];
}

export default function MoviePage() {
  const params = useParams();
  const movieId = params.id as string;
  const [movie, setMovie] = useState<Movie | null>(null);
  const [loading, setLoading] = useState(true);
  const [showPlayer, setShowPlayer] = useState(false);

  useEffect(() => {
    const fetchMovie = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_TMDB_API_KEY;
        if (apiKey && apiKey !== 'your_tmdb_api_key_here') {
          const response = await axios.get(
            `https://api.themoviedb.org/3/movie/${movieId}?api_key=${apiKey}`
          );
          setMovie(response.data);
        } else {
          // Mock data for demo purposes
          setMovie({
            id: parseInt(movieId),
            title: 'Sample Movie',
            overview: 'This is a sample movie for demonstration purposes.',
            poster_path: '/sample-poster.jpg',
            backdrop_path: '/sample-backdrop.jpg',
            release_date: '2024-01-01',
            vote_average: 8.5,
            runtime: 120,
            genres: [{ id: 1, name: 'Action' }, { id: 2, name: 'Adventure' }]
          });
        }
      } catch (error) {
        console.error('Error fetching movie:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMovie();
  }, [movieId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!movie) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Movie not found</div>
      </div>
    );
  }

  const streamUrl = `${process.env.NEXT_PUBLIC_VIDSRC_BASE_URL}/movie/${movieId}`;

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Hero Section */}
      <div 
        className="relative h-96 bg-cover bg-center"
        style={{
          backgroundImage: movie.backdrop_path 
            ? `url(https://image.tmdb.org/t/p/w1280${movie.backdrop_path})`
            : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
        <div className="relative container mx-auto px-4 h-full flex items-end pb-8">
          <div className="flex items-end space-x-6">
            {movie.poster_path && (
              <img
                src={`https://image.tmdb.org/t/p/w300${movie.poster_path}`}
                alt={movie.title}
                className="w-48 h-72 object-cover rounded-lg shadow-lg"
              />
            )}
            <div className="flex-1">
              <h1 className="text-4xl font-bold mb-2">{movie.title}</h1>
              <div className="flex items-center space-x-4 text-sm text-gray-300 mb-4">
                <span>{new Date(movie.release_date).getFullYear()}</span>
                <span>•</span>
                <span>{movie.runtime} min</span>
                <span>•</span>
                <span>⭐ {movie.vote_average.toFixed(1)}</span>
              </div>
              <div className="flex flex-wrap gap-2 mb-4">
                {movie.genres.map((genre) => (
                  <span
                    key={genre.id}
                    className="px-3 py-1 bg-blue-600 rounded-full text-sm"
                  >
                    {genre.name}
                  </span>
                ))}
              </div>
              <button
                onClick={() => setShowPlayer(true)}
                className="bg-red-600 hover:bg-red-700 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                ▶ Watch Now
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Movie Details */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl">
          <h2 className="text-2xl font-bold mb-4">Overview</h2>
          <p className="text-gray-300 leading-relaxed">{movie.overview}</p>
        </div>
      </div>

      {/* Video Player Modal */}
      {showPlayer && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-6xl">
            <button
              onClick={() => setShowPlayer(false)}
              className="absolute -top-12 right-0 text-white text-2xl hover:text-gray-300"
            >
              ✕ Close
            </button>
            <div className="relative w-full h-0 pb-[56.25%]">
              <iframe
                src={streamUrl}
                className="absolute top-0 left-0 w-full h-full border-0 rounded-lg"
                allowFullScreen
                title={`Watch ${movie.title}`}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
