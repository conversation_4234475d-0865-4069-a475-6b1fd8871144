{"version": 3, "sources": ["../../src/build/load-entrypoint.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\n\n// NOTE: this should be updated if this loader file is moved.\nconst PACKAGE_ROOT = path.normalize(path.join(__dirname, '../../..'))\nconst TEMPLATE_FOLDER = path.join(__dirname, 'templates')\nconst TEMPLATES_ESM_FOLDER = path.normalize(\n  path.join(__dirname, '../../dist/esm/build/templates')\n)\n\n/**\n * Load the entrypoint file from the ESM directory and performs string\n * replacements of the template variables specified in the `replacements`\n * argument.\n *\n * For non-string replacements, the template should use the\n * `declare const ${key}: ${type}` syntax. to ensure that the type is correct\n * and the typescript can compile. You may have to use `@ts-expect-error` to\n * handle replacement values that are related to imports.\n *\n * @param entrypoint the entrypoint to load\n * @param replacements string replacements to perform\n * @param injections code injections to perform\n * @param imports optional imports to insert or set to null\n * @returns the loaded file with the replacements\n */\nexport async function loadEntrypoint(\n  entrypoint:\n    | 'app-page'\n    | 'app-route'\n    | 'edge-app-route'\n    | 'edge-ssr'\n    | 'edge-ssr-app'\n    | 'middleware'\n    | 'pages'\n    | 'pages-api',\n  replacements: Record<`VAR_${string}`, string>,\n  injections?: Record<string, string>,\n  imports?: Record<string, string | null>\n): Promise<string> {\n  const filepath = path.resolve(\n    path.join(TEMPLATES_ESM_FOLDER, `${entrypoint}.js`)\n  )\n\n  let file = await fs.readFile(filepath, 'utf8')\n\n  // Update the relative imports to be absolute. This will update any relative\n  // imports to be relative to the root of the `next` package.\n  let count = 0\n  file = file.replaceAll(\n    /from '(\\..*)'|import '(\\..*)'/g,\n    function (_, fromRequest, importRequest) {\n      count++\n\n      const relative = path\n        .relative(\n          PACKAGE_ROOT,\n          path.resolve(TEMPLATE_FOLDER, fromRequest ?? importRequest)\n        )\n        // Ensure that we use linux style path separators for node.\n        .replace(/\\\\/g, '/')\n\n      // Verify that the relative import is relative to the `next` package. This\n      // will catch cases where the constants at the top of the file were not\n      // updated after the file was moved.\n      if (!relative.startsWith('next/')) {\n        throw new Error(\n          `Invariant: Expected relative import to start with \"next/\", found \"${relative}\"`\n        )\n      }\n\n      return fromRequest\n        ? `from ${JSON.stringify(relative)}`\n        : `import ${JSON.stringify(relative)}`\n    }\n  )\n\n  // Verify that at least one import was replaced. It's the case today where\n  // every template file has at least one import to update, so this ensures that\n  // we don't accidentally remove the import replacement code or use the wrong\n  // template file.\n  if (count === 0) {\n    throw new Error('Invariant: Expected to replace at least one import')\n  }\n\n  const replaced = new Set<string>()\n\n  // Replace all the template variables with the actual values. If a template\n  // variable is missing, throw an error.\n  file = file.replaceAll(\n    new RegExp(\n      `${Object.keys(replacements)\n        .map((k) => `'${k}'`)\n        .join('|')}`,\n      'g'\n    ),\n    (match) => {\n      const key = JSON.parse(match.replace(/'/g, `\"`))\n\n      if (!(key in replacements)) {\n        throw new Error(`Invariant: Unexpected template variable ${key}`)\n      }\n\n      replaced.add(key)\n\n      return JSON.stringify(replacements[key])\n    }\n  )\n\n  // Check to see if there's any remaining template variables.\n  let matches = file.match(/VAR_[A-Z_]+/g)\n  if (matches) {\n    throw new Error(\n      `Invariant: Expected to replace all template variables, found ${matches.join(\n        ', '\n      )}`\n    )\n  }\n\n  // Check to see if any template variable was provided but not used.\n  if (replaced.size !== Object.keys(replacements).length) {\n    // Find the difference between the provided replacements and the replaced\n    // template variables. This will let us notify the user of any template\n    // variables that were not used but were provided.\n    const difference = Object.keys(replacements).filter(\n      (key) => !replaced.has(key)\n    )\n\n    throw new Error(\n      `Invariant: Expected to replace all template variables, missing ${difference.join(\n        ', '\n      )} in template`\n    )\n  }\n\n  // Replace the injections.\n  const injected = new Set<string>()\n  if (injections) {\n    // Track all the injections to ensure that we're not missing any.\n    file = file.replaceAll(\n      new RegExp(`// INJECT:(${Object.keys(injections).join('|')})`, 'g'),\n      (_, key) => {\n        if (!(key in injections)) {\n          throw new Error(`Invariant: Unexpected injection ${key}`)\n        }\n\n        injected.add(key)\n\n        return `const ${key} = ${injections[key]}`\n      }\n    )\n  }\n\n  // Check to see if there's any remaining injections.\n  matches = file.match(/\\/\\/ INJECT:[A-Za-z0-9_]+/g)\n  if (matches) {\n    throw new Error(\n      `Invariant: Expected to inject all injections, found ${matches.join(\n        ', '\n      )}`\n    )\n  }\n\n  // Check to see if any injection was provided but not used.\n  if (injected.size !== Object.keys(injections ?? {}).length) {\n    // Find the difference between the provided injections and the injected\n    // injections. This will let us notify the user of any injections that were\n    // not used but were provided.\n    const difference = Object.keys(injections ?? {}).filter(\n      (key) => !injected.has(key)\n    )\n\n    throw new Error(\n      `Invariant: Expected to inject all injections, missing ${difference.join(\n        ', '\n      )} in template`\n    )\n  }\n\n  // Replace the optional imports.\n  const importsAdded = new Set<string>()\n  if (imports) {\n    // Track all the imports to ensure that we're not missing any.\n    file = file.replaceAll(\n      new RegExp(\n        `// OPTIONAL_IMPORT:(\\\\* as )?(${Object.keys(imports).join('|')})`,\n        'g'\n      ),\n      (_, asNamespace = '', key) => {\n        if (!(key in imports)) {\n          throw new Error(`Invariant: Unexpected optional import ${key}`)\n        }\n\n        importsAdded.add(key)\n\n        if (imports[key]) {\n          return `import ${asNamespace}${key} from ${JSON.stringify(\n            imports[key]\n          )}`\n        } else {\n          return `const ${key} = null`\n        }\n      }\n    )\n  }\n\n  // Check to see if there's any remaining imports.\n  matches = file.match(/\\/\\/ OPTIONAL_IMPORT:(\\* as )?[A-Za-z0-9_]+/g)\n  if (matches) {\n    throw new Error(\n      `Invariant: Expected to inject all imports, found ${matches.join(', ')}`\n    )\n  }\n\n  // Check to see if any import was provided but not used.\n  if (importsAdded.size !== Object.keys(imports ?? {}).length) {\n    // Find the difference between the provided imports and the injected\n    // imports. This will let us notify the user of any imports that were\n    // not used but were provided.\n    const difference = Object.keys(imports ?? {}).filter(\n      (key) => !importsAdded.has(key)\n    )\n\n    throw new Error(\n      `Invariant: Expected to inject all imports, missing ${difference.join(\n        ', '\n      )} in template`\n    )\n  }\n\n  return file\n}\n"], "names": ["fs", "path", "PACKAGE_ROOT", "normalize", "join", "__dirname", "TEMPLATE_FOLDER", "TEMPLATES_ESM_FOLDER", "loadEntrypoint", "entrypoint", "replacements", "injections", "imports", "filepath", "resolve", "file", "readFile", "count", "replaceAll", "_", "fromRequest", "importRequest", "relative", "replace", "startsWith", "Error", "JSON", "stringify", "replaced", "Set", "RegExp", "Object", "keys", "map", "k", "match", "key", "parse", "add", "matches", "size", "length", "difference", "filter", "has", "injected", "importsAdded", "asNamespace"], "mappings": "AAAA,OAAOA,QAAQ,cAAa;AAC5B,OAAOC,UAAU,OAAM;AAEvB,6DAA6D;AAC7D,MAAMC,eAAeD,KAAKE,SAAS,CAACF,KAAKG,IAAI,CAACC,WAAW;AACzD,MAAMC,kBAAkBL,KAAKG,IAAI,CAACC,WAAW;AAC7C,MAAME,uBAAuBN,KAAKE,SAAS,CACzCF,KAAKG,IAAI,CAACC,WAAW;AAGvB;;;;;;;;;;;;;;;CAeC,GACD,OAAO,eAAeG,eACpBC,UAQe,EACfC,YAA6C,EAC7CC,UAAmC,EACnCC,OAAuC;IAEvC,MAAMC,WAAWZ,KAAKa,OAAO,CAC3Bb,KAAKG,IAAI,CAACG,sBAAsB,GAAGE,WAAW,GAAG,CAAC;IAGpD,IAAIM,OAAO,MAAMf,GAAGgB,QAAQ,CAACH,UAAU;IAEvC,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAII,QAAQ;IACZF,OAAOA,KAAKG,UAAU,CACpB,kCACA,SAAUC,CAAC,EAAEC,WAAW,EAAEC,aAAa;QACrCJ;QAEA,MAAMK,WAAWrB,KACdqB,QAAQ,CACPpB,cACAD,KAAKa,OAAO,CAACR,iBAAiBc,eAAeC,eAE/C,2DAA2D;SAC1DE,OAAO,CAAC,OAAO;QAElB,0EAA0E;QAC1E,uEAAuE;QACvE,oCAAoC;QACpC,IAAI,CAACD,SAASE,UAAU,CAAC,UAAU;YACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,kEAAkE,EAAEH,SAAS,CAAC,CAAC,GAD5E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOF,cACH,CAAC,KAAK,EAAEM,KAAKC,SAAS,CAACL,WAAW,GAClC,CAAC,OAAO,EAAEI,KAAKC,SAAS,CAACL,WAAW;IAC1C;IAGF,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,iBAAiB;IACjB,IAAIL,UAAU,GAAG;QACf,MAAM,qBAA+D,CAA/D,IAAIQ,MAAM,uDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEA,MAAMG,WAAW,IAAIC;IAErB,2EAA2E;IAC3E,uCAAuC;IACvCd,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OACF,GAAGC,OAAOC,IAAI,CAACtB,cACZuB,GAAG,CAAC,CAACC,IAAM,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,EACnB9B,IAAI,CAAC,MAAM,EACd,MAEF,CAAC+B;QACC,MAAMC,MAAMV,KAAKW,KAAK,CAACF,MAAMZ,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;QAE9C,IAAI,CAAEa,CAAAA,OAAO1B,YAAW,GAAI;YAC1B,MAAM,qBAA2D,CAA3D,IAAIe,MAAM,CAAC,wCAAwC,EAAEW,KAAK,GAA1D,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEAR,SAASU,GAAG,CAACF;QAEb,OAAOV,KAAKC,SAAS,CAACjB,YAAY,CAAC0B,IAAI;IACzC;IAGF,4DAA4D;IAC5D,IAAIG,UAAUxB,KAAKoB,KAAK,CAAC;IACzB,IAAII,SAAS;QACX,MAAM,qBAIL,CAJK,IAAId,MACR,CAAC,6DAA6D,EAAEc,QAAQnC,IAAI,CAC1E,OACC,GAHC,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,mEAAmE;IACnE,IAAIwB,SAASY,IAAI,KAAKT,OAAOC,IAAI,CAACtB,cAAc+B,MAAM,EAAE;QACtD,yEAAyE;QACzE,uEAAuE;QACvE,kDAAkD;QAClD,MAAMC,aAAaX,OAAOC,IAAI,CAACtB,cAAciC,MAAM,CACjD,CAACP,MAAQ,CAACR,SAASgB,GAAG,CAACR;QAGzB,MAAM,qBAIL,CAJK,IAAIX,MACR,CAAC,+DAA+D,EAAEiB,WAAWtC,IAAI,CAC/E,MACA,YAAY,CAAC,GAHX,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,0BAA0B;IAC1B,MAAMyC,WAAW,IAAIhB;IACrB,IAAIlB,YAAY;QACd,iEAAiE;QACjEI,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OAAO,CAAC,WAAW,EAAEC,OAAOC,IAAI,CAACrB,YAAYP,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAC/D,CAACe,GAAGiB;YACF,IAAI,CAAEA,CAAAA,OAAOzB,UAAS,GAAI;gBACxB,MAAM,qBAAmD,CAAnD,IAAIc,MAAM,CAAC,gCAAgC,EAAEW,KAAK,GAAlD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAkD;YAC1D;YAEAS,SAASP,GAAG,CAACF;YAEb,OAAO,CAAC,MAAM,EAAEA,IAAI,GAAG,EAAEzB,UAAU,CAACyB,IAAI,EAAE;QAC5C;IAEJ;IAEA,oDAAoD;IACpDG,UAAUxB,KAAKoB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,qBAIL,CAJK,IAAId,MACR,CAAC,oDAAoD,EAAEc,QAAQnC,IAAI,CACjE,OACC,GAHC,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,2DAA2D;IAC3D,IAAIyC,SAASL,IAAI,KAAKT,OAAOC,IAAI,CAACrB,cAAc,CAAC,GAAG8B,MAAM,EAAE;QAC1D,uEAAuE;QACvE,2EAA2E;QAC3E,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACrB,cAAc,CAAC,GAAGgC,MAAM,CACrD,CAACP,MAAQ,CAACS,SAASD,GAAG,CAACR;QAGzB,MAAM,qBAIL,CAJK,IAAIX,MACR,CAAC,sDAAsD,EAAEiB,WAAWtC,IAAI,CACtE,MACA,YAAY,CAAC,GAHX,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,gCAAgC;IAChC,MAAM0C,eAAe,IAAIjB;IACzB,IAAIjB,SAAS;QACX,8DAA8D;QAC9DG,OAAOA,KAAKG,UAAU,CACpB,IAAIY,OACF,CAAC,8BAA8B,EAAEC,OAAOC,IAAI,CAACpB,SAASR,IAAI,CAAC,KAAK,CAAC,CAAC,EAClE,MAEF,CAACe,GAAG4B,cAAc,EAAE,EAAEX;YACpB,IAAI,CAAEA,CAAAA,OAAOxB,OAAM,GAAI;gBACrB,MAAM,qBAAyD,CAAzD,IAAIa,MAAM,CAAC,sCAAsC,EAAEW,KAAK,GAAxD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwD;YAChE;YAEAU,aAAaR,GAAG,CAACF;YAEjB,IAAIxB,OAAO,CAACwB,IAAI,EAAE;gBAChB,OAAO,CAAC,OAAO,EAAEW,cAAcX,IAAI,MAAM,EAAEV,KAAKC,SAAS,CACvDf,OAAO,CAACwB,IAAI,GACX;YACL,OAAO;gBACL,OAAO,CAAC,MAAM,EAAEA,IAAI,OAAO,CAAC;YAC9B;QACF;IAEJ;IAEA,iDAAiD;IACjDG,UAAUxB,KAAKoB,KAAK,CAAC;IACrB,IAAII,SAAS;QACX,MAAM,qBAEL,CAFK,IAAId,MACR,CAAC,iDAAiD,EAAEc,QAAQnC,IAAI,CAAC,OAAO,GADpE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,wDAAwD;IACxD,IAAI0C,aAAaN,IAAI,KAAKT,OAAOC,IAAI,CAACpB,WAAW,CAAC,GAAG6B,MAAM,EAAE;QAC3D,oEAAoE;QACpE,qEAAqE;QACrE,8BAA8B;QAC9B,MAAMC,aAAaX,OAAOC,IAAI,CAACpB,WAAW,CAAC,GAAG+B,MAAM,CAClD,CAACP,MAAQ,CAACU,aAAaF,GAAG,CAACR;QAG7B,MAAM,qBAIL,CAJK,IAAIX,MACR,CAAC,mDAAmD,EAAEiB,WAAWtC,IAAI,CACnE,MACA,YAAY,CAAC,GAHX,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,OAAOW;AACT"}