{"version": 3, "sources": ["../../../src/server/dev/next-dev-server.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>R<PERSON>ult, NodeRequestHandler } from '../next-server'\nimport type { LoadComponentsReturnType } from '../load-components'\nimport type { Options as ServerOptions } from '../next-server'\nimport type { Params } from '../request/params'\nimport type { ParsedUrl } from '../../shared/lib/router/utils/parse-url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { MiddlewareRoutingItem } from '../base-server'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport type { RouteMatcherManager } from '../route-matcher-managers/route-matcher-manager'\nimport {\n  getRequestMeta,\n  type NextParsedUrlQuery,\n  type NextUrlWithParsedQuery,\n} from '../request-meta'\nimport type { DevBundlerService } from '../lib/dev-bundler-service'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\nimport type { NodeNextResponse, NodeNextRequest } from '../base-http/node'\nimport type { RouteEnsurer } from '../route-matcher-managers/dev-route-matcher-manager'\nimport type { PagesManifest } from '../../build/webpack/plugins/pages-manifest-plugin'\n\nimport fs from 'fs'\nimport { Worker } from 'next/dist/compiled/jest-worker'\nimport { join as pathJoin } from 'path'\nimport { ampValidation } from '../../build/output'\nimport {\n  INSTRUMENTATION_HOOK_FILENAME,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n} from '../../lib/constants'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PAGES_MANIFEST,\n  APP_PATHS_MANIFEST,\n  COMPILER_NAMES,\n} from '../../shared/lib/constants'\nimport Server, { WrappedBuildError } from '../next-server'\nimport { normalizePagePath } from '../../shared/lib/page-path/normalize-page-path'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { Telemetry } from '../../telemetry/storage'\nimport { type Span, setGlobal, trace } from '../../trace'\nimport { findPageFile } from '../lib/find-page-file'\nimport { getFormattedNodeOptionsWithoutInspect } from '../lib/utils'\nimport { withCoalescedInvoke } from '../../lib/coalesced-function'\nimport { loadDefaultErrorComponents } from '../load-default-error-components'\nimport { DecodeError, MiddlewareNotFoundError } from '../../shared/lib/utils'\nimport * as Log from '../../build/output/log'\nimport isError, { getProperError } from '../../lib/is-error'\nimport { isMiddlewareFile } from '../../build/utils'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { DevRouteMatcherManager } from '../route-matcher-managers/dev-route-matcher-manager'\nimport { DevPagesRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-route-matcher-provider'\nimport { DevPagesAPIRouteMatcherProvider } from '../route-matcher-providers/dev/dev-pages-api-route-matcher-provider'\nimport { DevAppPageRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-page-route-matcher-provider'\nimport { DevAppRouteRouteMatcherProvider } from '../route-matcher-providers/dev/dev-app-route-route-matcher-provider'\nimport { NodeManifestLoader } from '../route-matcher-providers/helpers/manifest-loaders/node-manifest-loader'\nimport { BatchedFileReader } from '../route-matcher-providers/dev/helpers/file-reader/batched-file-reader'\nimport { DefaultFileReader } from '../route-matcher-providers/dev/helpers/file-reader/default-file-reader'\nimport { LRUCache } from '../lib/lru-cache'\nimport { getMiddlewareRouteMatcher } from '../../shared/lib/router/utils/middleware-route-matcher'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { isPostpone } from '../lib/router-utils/is-postpone'\nimport { generateInterceptionRoutesRewrites } from '../../lib/generate-interception-routes-rewrites'\nimport { buildCustomRoute } from '../../lib/build-custom-route'\nimport { decorateServerError } from '../../shared/lib/error-source'\nimport type { ServerOnInstrumentationRequestError } from '../app-render/types'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport { logRequests } from './log-requests'\nimport { FallbackMode } from '../../lib/fallback'\nimport type { PagesDevOverlayType } from '../../client/components/react-dev-overlay/pages/pages-dev-overlay'\n\n// Load ReactDevOverlay only when needed\nlet ReactDevOverlayImpl: PagesDevOverlayType\nconst ReactDevOverlay: PagesDevOverlayType = (props) => {\n  if (ReactDevOverlayImpl === undefined) {\n    ReactDevOverlayImpl =\n      require('../../client/components/react-dev-overlay/pages/pages-dev-overlay')\n        .PagesDevOverlay as PagesDevOverlayType\n  }\n  return ReactDevOverlayImpl(props)\n}\n\nexport interface Options extends ServerOptions {\n  /**\n   * Tells of Next.js is running from the `next dev` command\n   */\n  isNextDevCommand?: boolean\n\n  /**\n   * Interface to the development bundler.\n   */\n  bundlerService: DevBundlerService\n\n  /**\n   * Trace span for server startup.\n   */\n  startServerSpan: Span\n}\n\nexport default class DevServer extends Server {\n  /**\n   * The promise that resolves when the server is ready. When this is unset\n   * the server is ready.\n   */\n  private ready? = new DetachedPromise<void>()\n  protected sortedRoutes?: string[]\n  private pagesDir?: string\n  private appDir?: string\n  private actualMiddlewareFile?: string\n  private actualInstrumentationHookFile?: string\n  private middleware?: MiddlewareRoutingItem\n  private originalFetch?: typeof fetch\n  private readonly bundlerService: DevBundlerService\n  private staticPathsCache: LRUCache<\n    UnwrapPromise<ReturnType<DevServer['getStaticPaths']>>\n  >\n  private startServerSpan: Span\n  private readonly serverComponentsHmrCache:\n    | ServerComponentsHmrCache\n    | undefined\n\n  protected staticPathsWorker?: { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  }\n\n  private getStaticPathsWorker(): { [key: string]: any } & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  } {\n    const worker = new Worker(require.resolve('./static-paths-worker'), {\n      maxRetries: 1,\n      // For dev server, it's not necessary to spin up too many workers as long as you are not doing a load test.\n      // This helps reusing the memory a lot.\n      numWorkers: 1,\n      enableWorkerThreads: this.nextConfig.experimental.workerThreads,\n      forkOptions: {\n        env: {\n          ...process.env,\n          // discard --inspect/--inspect-brk flags from process.env.NODE_OPTIONS. Otherwise multiple Node.js debuggers\n          // would be started if user launch Next.js in debugging mode. The number of debuggers is linked to\n          // the number of workers Next.js tries to launch. The only worker users are interested in debugging\n          // is the main Next.js one\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      },\n    }) as Worker & {\n      loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n    }\n\n    worker.getStdout().pipe(process.stdout)\n    worker.getStderr().pipe(process.stderr)\n\n    return worker\n  }\n\n  constructor(options: Options) {\n    try {\n      // Increase the number of stack frames on the server\n      Error.stackTraceLimit = 50\n    } catch {}\n    super({ ...options, dev: true })\n    this.bundlerService = options.bundlerService\n    this.startServerSpan =\n      options.startServerSpan ?? trace('start-next-dev-server')\n    this.renderOpts.dev = true\n    this.renderOpts.ErrorDebug = ReactDevOverlay\n    this.staticPathsCache = new LRUCache(\n      // 5MB\n      5 * 1024 * 1024,\n      function length(value) {\n        return JSON.stringify(value.staticPaths)?.length ?? 0\n      }\n    )\n    this.renderOpts.ampSkipValidation =\n      this.nextConfig.experimental?.amp?.skipValidation ?? false\n    this.renderOpts.ampValidator = (html: string, pathname: string) => {\n      const validatorPath =\n        (this.nextConfig.experimental &&\n          this.nextConfig.experimental.amp &&\n          this.nextConfig.experimental.amp.validator) ||\n        require.resolve(\n          'next/dist/compiled/amphtml-validator/validator_wasm.js'\n        )\n\n      const AmpHtmlValidator =\n        require('next/dist/compiled/amphtml-validator') as typeof import('next/dist/compiled/amphtml-validator')\n      return AmpHtmlValidator.getInstance(validatorPath).then((validator) => {\n        const result = validator.validateString(html)\n        ampValidation(\n          pathname,\n          result.errors\n            .filter((e) => e.severity === 'ERROR')\n            .filter((e) => this._filterAmpDevelopmentScript(html, e)),\n          result.errors.filter((e) => e.severity !== 'ERROR')\n        )\n      })\n    }\n\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n    this.pagesDir = pagesDir\n    this.appDir = appDir\n\n    if (this.nextConfig.experimental.serverComponentsHmrCache) {\n      this.serverComponentsHmrCache = new LRUCache(\n        this.nextConfig.cacheMaxMemorySize,\n        function length(value) {\n          return JSON.stringify(value).length\n        }\n      )\n    }\n  }\n\n  protected override getServerComponentsHmrCache() {\n    return this.serverComponentsHmrCache\n  }\n\n  protected getRouteMatchers(): RouteMatcherManager {\n    const { pagesDir, appDir } = findPagesDir(this.dir)\n\n    const ensurer: RouteEnsurer = {\n      ensure: async (match, pathname) => {\n        await this.ensurePage({\n          definition: match.definition,\n          page: match.definition.page,\n          clientOnly: false,\n          url: pathname,\n        })\n      },\n    }\n\n    const matchers = new DevRouteMatcherManager(\n      super.getRouteMatchers(),\n      ensurer,\n      this.dir\n    )\n    const extensions = this.nextConfig.pageExtensions\n    const extensionsExpression = new RegExp(`\\\\.(?:${extensions.join('|')})$`)\n\n    // If the pages directory is available, then configure those matchers.\n    if (pagesDir) {\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Only allow files that have the correct extensions.\n          pathnameFilter: (pathname) => extensionsExpression.test(pathname),\n        })\n      )\n\n      matchers.push(\n        new DevPagesRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n      matchers.push(\n        new DevPagesAPIRouteMatcherProvider(\n          pagesDir,\n          extensions,\n          fileReader,\n          this.localeNormalizer\n        )\n      )\n    }\n\n    if (appDir) {\n      // We create a new file reader for the app directory because we don't want\n      // to include any folders or files starting with an underscore. This will\n      // prevent the reader from wasting time reading files that we know we\n      // don't care about.\n      const fileReader = new BatchedFileReader(\n        new DefaultFileReader({\n          // Ignore any directory prefixed with an underscore.\n          ignorePartFilter: (part) => part.startsWith('_'),\n        })\n      )\n\n      matchers.push(\n        new DevAppPageRouteMatcherProvider(appDir, extensions, fileReader)\n      )\n      matchers.push(\n        new DevAppRouteRouteMatcherProvider(appDir, extensions, fileReader)\n      )\n    }\n\n    return matchers\n  }\n\n  protected getBuildId(): string {\n    return 'development'\n  }\n\n  protected async prepareImpl(): Promise<void> {\n    setGlobal('distDir', this.distDir)\n    setGlobal('phase', PHASE_DEVELOPMENT_SERVER)\n\n    const telemetry = new Telemetry({ distDir: this.distDir })\n\n    await super.prepareImpl()\n    await this.matchers.reload()\n\n    this.ready?.resolve()\n    this.ready = undefined\n\n    // In dev, this needs to be called after prepare because the build entries won't be known in the constructor\n    this.interceptionRoutePatterns = this.getinterceptionRoutePatterns()\n\n    // This is required by the tracing subsystem.\n    setGlobal('appDir', this.appDir)\n    setGlobal('pagesDir', this.pagesDir)\n    setGlobal('telemetry', telemetry)\n\n    process.on('unhandledRejection', (reason) => {\n      if (isPostpone(reason)) {\n        // React postpones that are unhandled might end up logged here but they're\n        // not really errors. They're just part of rendering.\n        return\n      }\n      this.logErrorWithOriginalStack(reason, 'unhandledRejection')\n    })\n    process.on('uncaughtException', (err) => {\n      this.logErrorWithOriginalStack(err, 'uncaughtException')\n    })\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let normalizedPath: string\n    try {\n      normalizedPath = normalizePagePath(pathname)\n    } catch (err) {\n      console.error(err)\n      // if normalizing the page fails it means it isn't valid\n      // so it doesn't exist so don't throw and return false\n      // to ensure we return 404 instead of 500\n      return false\n    }\n\n    if (isMiddlewareFile(normalizedPath)) {\n      return findPageFile(\n        this.dir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      ).then(Boolean)\n    }\n\n    let appFile: string | null = null\n    let pagesFile: string | null = null\n\n    if (this.appDir) {\n      appFile = await findPageFile(\n        this.appDir,\n        normalizedPath + '/page',\n        this.nextConfig.pageExtensions,\n        true\n      )\n    }\n\n    if (this.pagesDir) {\n      pagesFile = await findPageFile(\n        this.pagesDir,\n        normalizedPath,\n        this.nextConfig.pageExtensions,\n        false\n      )\n    }\n    if (appFile && pagesFile) {\n      return false\n    }\n\n    return Boolean(appFile || pagesFile)\n  }\n\n  async runMiddleware(params: {\n    request: NodeNextRequest\n    response: NodeNextResponse\n    parsedUrl: ParsedUrl\n    parsed: UrlWithParsedQuery\n    middlewareList: MiddlewareRoutingItem[]\n  }) {\n    try {\n      const result = await super.runMiddleware({\n        ...params,\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n\n      if ('finished' in result) {\n        return result\n      }\n\n      result.waitUntil.catch((error) => {\n        this.logErrorWithOriginalStack(error, 'unhandledRejection')\n      })\n      return result\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n\n      /**\n       * We only log the error when it is not a MiddlewareNotFound error as\n       * in that case we should be already displaying a compilation error\n       * which is what makes the module not found.\n       */\n      if (!(error instanceof MiddlewareNotFoundError)) {\n        this.logErrorWithOriginalStack(error)\n      }\n\n      const err = getProperError(error)\n      decorateServerError(err, COMPILER_NAMES.edgeServer)\n      const { request, response, parsedUrl } = params\n\n      /**\n       * When there is a failure for an internal Next.js request from\n       * middleware we bypass the error without finishing the request\n       * so we can serve the required chunks to render the error.\n       */\n      if (\n        request.url.includes('/_next/static') ||\n        request.url.includes('/__nextjs_original-stack-frame') ||\n        request.url.includes('/__nextjs_source-map') ||\n        request.url.includes('/__nextjs_error_feedback')\n      ) {\n        return { finished: false }\n      }\n\n      response.statusCode = 500\n      await this.renderError(err, request, response, parsedUrl.pathname)\n      return { finished: true }\n    }\n  }\n\n  async runEdgeFunction(params: {\n    req: NodeNextRequest\n    res: NodeNextResponse\n    query: ParsedUrlQuery\n    params: Params | undefined\n    page: string\n    appPaths: string[] | null\n    isAppPath: boolean\n  }) {\n    try {\n      return super.runEdgeFunction({\n        ...params,\n        onError: (err) => this.logErrorWithOriginalStack(err, 'app-dir'),\n        onWarning: (warn) => {\n          this.logErrorWithOriginalStack(warn, 'warning')\n        },\n      })\n    } catch (error) {\n      if (error instanceof DecodeError) {\n        throw error\n      }\n      this.logErrorWithOriginalStack(error, 'warning')\n      const err = getProperError(error)\n      const { req, res, page } = params\n\n      res.statusCode = 500\n      await this.renderError(err, req, res, page)\n      return null\n    }\n  }\n\n  public getRequestHandler(): NodeRequestHandler {\n    const handler = super.getRequestHandler()\n\n    return (req, res, parsedUrl) => {\n      const request = this.normalizeReq(req)\n      const response = this.normalizeRes(res)\n      const loggingConfig = this.nextConfig.logging\n\n      if (loggingConfig !== false) {\n        const start = Date.now()\n        const isMiddlewareRequest = getRequestMeta(req, 'middlewareInvoke')\n\n        if (!isMiddlewareRequest) {\n          response.originalResponse.once('close', () => {\n            // NOTE: The route match is only attached to the request's meta data\n            // after the request handler is created, so we need to check it in the\n            // close handler and not before.\n            const routeMatch = getRequestMeta(req).match\n\n            if (!routeMatch) {\n              return\n            }\n\n            logRequests({\n              request,\n              response,\n              loggingConfig,\n              requestDurationInMs: Date.now() - start,\n            })\n          })\n        }\n      }\n\n      return handler(request, response, parsedUrl)\n    }\n  }\n\n  public async handleRequest(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl?: NextUrlWithParsedQuery\n  ): Promise<void> {\n    const span = trace('handle-request', undefined, { url: req.url })\n    const result = await span.traceAsyncFn(async () => {\n      await this.ready?.promise\n      return await super.handleRequest(req, res, parsedUrl)\n    })\n    const memoryUsage = process.memoryUsage()\n    span\n      .traceChild('memory-usage', {\n        url: req.url,\n        'memory.rss': String(memoryUsage.rss),\n        'memory.heapUsed': String(memoryUsage.heapUsed),\n        'memory.heapTotal': String(memoryUsage.heapTotal),\n      })\n      .stop()\n    return result\n  }\n\n  async run(\n    req: NodeNextRequest,\n    res: NodeNextResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.ready?.promise\n\n    const { basePath } = this.nextConfig\n    let originalPathname: string | null = null\n\n    // TODO: see if we can remove this in the future\n    if (basePath && pathHasPrefix(parsedUrl.pathname || '/', basePath)) {\n      // strip basePath before handling dev bundles\n      // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n      originalPathname = parsedUrl.pathname\n      parsedUrl.pathname = removePathPrefix(parsedUrl.pathname || '/', basePath)\n    }\n\n    const { pathname } = parsedUrl\n\n    if (pathname!.startsWith('/_next')) {\n      if (fs.existsSync(pathJoin(this.publicDir, '_next'))) {\n        throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n      }\n    }\n\n    if (originalPathname) {\n      // restore the path before continuing so that custom-routes can accurately determine\n      // if they should match against the basePath or not\n      parsedUrl.pathname = originalPathname\n    }\n    try {\n      return await super.run(req, res, parsedUrl)\n    } catch (error) {\n      const err = getProperError(error)\n      formatServerError(err)\n      this.logErrorWithOriginalStack(err)\n      if (!res.sent) {\n        res.statusCode = 500\n        try {\n          return await this.renderError(err, req, res, pathname!, {\n            __NEXT_PAGE: (isError(err) && err.page) || pathname || '',\n          })\n        } catch (internalErr) {\n          console.error(internalErr)\n          res.body('Internal Server Error').send()\n        }\n      }\n    }\n  }\n\n  protected logErrorWithOriginalStack(\n    err?: unknown,\n    type?: 'unhandledRejection' | 'uncaughtException' | 'warning' | 'app-dir'\n  ): void {\n    this.bundlerService.logErrorWithOriginalStack(err, type)\n  }\n\n  protected getPagesManifest(): PagesManifest | undefined {\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, PAGES_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getAppPathsManifest(): PagesManifest | undefined {\n    if (!this.enabledDirectories.app) return undefined\n\n    return (\n      NodeManifestLoader.require(\n        pathJoin(this.serverDistDir, APP_PATHS_MANIFEST)\n      ) ?? undefined\n    )\n  }\n\n  protected getinterceptionRoutePatterns(): RegExp[] {\n    const rewrites = generateInterceptionRoutesRewrites(\n      Object.keys(this.appPathRoutes ?? {}),\n      this.nextConfig.basePath\n    ).map((route) => new RegExp(buildCustomRoute('rewrite', route).regex))\n\n    if (this.nextConfig.output === 'export' && rewrites.length > 0) {\n      Log.error(\n        'Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features'\n      )\n\n      process.exit(1)\n    }\n\n    return rewrites ?? []\n  }\n\n  protected async getMiddleware() {\n    // We need to populate the match\n    // field as it isn't serializable\n    if (this.middleware?.match === null) {\n      this.middleware.match = getMiddlewareRouteMatcher(\n        this.middleware.matchers || []\n      )\n    }\n    return this.middleware\n  }\n\n  protected getNextFontManifest() {\n    return undefined\n  }\n\n  protected async hasMiddleware(): Promise<boolean> {\n    return this.hasPage(this.actualMiddlewareFile!)\n  }\n\n  protected async ensureMiddleware(url: string) {\n    return this.ensurePage({\n      page: this.actualMiddlewareFile!,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  protected async loadInstrumentationModule(): Promise<any> {\n    let instrumentationModule: any\n    if (\n      this.actualInstrumentationHookFile &&\n      (await this.ensurePage({\n        page: this.actualInstrumentationHookFile!,\n        clientOnly: false,\n        definition: undefined,\n      })\n        .then(() => true)\n        .catch(() => false))\n    ) {\n      try {\n        instrumentationModule = await require(\n          pathJoin(this.distDir, 'server', INSTRUMENTATION_HOOK_FILENAME)\n        )\n      } catch (err: any) {\n        err.message = `An error occurred while loading instrumentation hook: ${err.message}`\n        throw err\n      }\n    }\n    return instrumentationModule\n  }\n\n  protected async runInstrumentationHookIfAvailable() {\n    await this.startServerSpan\n      .traceChild('run-instrumentation-hook')\n      .traceAsyncFn(() => this.instrumentation?.register?.())\n  }\n\n  protected async ensureEdgeFunction({\n    page,\n    appPaths,\n    url,\n  }: {\n    page: string\n    appPaths: string[] | null\n    url: string\n  }) {\n    return this.ensurePage({\n      page,\n      appPaths,\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  generateRoutes(_dev?: boolean) {\n    // In development we expose all compiled files for react-error-overlay's line show feature\n    // We use unshift so that we're sure the routes is defined before Next's default routes\n    // routes.unshift({\n    //   match: getPathMatch('/_next/development/:path*'),\n    //   type: 'route',\n    //   name: '_next/development catchall',\n    //   fn: async (req, res, params) => {\n    //     const p = pathJoin(this.distDir, ...(params.path || []))\n    //     await this.serveStatic(req, res, p)\n    //     return {\n    //       finished: true,\n    //     }\n    //   },\n    // })\n  }\n\n  _filterAmpDevelopmentScript(\n    html: string,\n    event: { line: number; col: number; code: string }\n  ): boolean {\n    if (event.code !== 'DISALLOWED_SCRIPT_TAG') {\n      return true\n    }\n\n    const snippetChunks = html.split('\\n')\n\n    let snippet\n    if (\n      !(snippet = html.split('\\n')[event.line - 1]) ||\n      !(snippet = snippet.substring(event.col))\n    ) {\n      return true\n    }\n\n    snippet = snippet + snippetChunks.slice(event.line).join('\\n')\n    snippet = snippet.substring(0, snippet.indexOf('</script>'))\n\n    return !snippet.includes('data-amp-development-mode-only')\n  }\n\n  protected async getStaticPaths({\n    pathname,\n    requestHeaders,\n    page,\n    isAppPath,\n  }: {\n    pathname: string\n    requestHeaders: IncrementalCache['requestHeaders']\n    page: string\n    isAppPath: boolean\n  }): Promise<{\n    staticPaths?: string[]\n    fallbackMode?: FallbackMode\n  }> {\n    // we lazy load the staticPaths to prevent the user\n    // from waiting on them for the page to load in dev mode\n\n    const __getStaticPaths = async () => {\n      const {\n        configFileName,\n        publicRuntimeConfig,\n        serverRuntimeConfig,\n        httpAgentOptions,\n      } = this.nextConfig\n      const { locales, defaultLocale } = this.nextConfig.i18n || {}\n      const staticPathsWorker = this.getStaticPathsWorker()\n\n      try {\n        const pathsResult = await staticPathsWorker.loadStaticPaths({\n          dir: this.dir,\n          distDir: this.distDir,\n          pathname,\n          config: {\n            pprConfig: this.nextConfig.experimental.ppr,\n            configFileName,\n            publicRuntimeConfig,\n            serverRuntimeConfig,\n            dynamicIO: Boolean(this.nextConfig.experimental.dynamicIO),\n          },\n          httpAgentOptions,\n          locales,\n          defaultLocale,\n          page,\n          isAppPath,\n          requestHeaders,\n          cacheHandler: this.nextConfig.cacheHandler,\n          cacheHandlers: this.nextConfig.experimental.cacheHandlers,\n          cacheLifeProfiles: this.nextConfig.experimental.cacheLife,\n          fetchCacheKeyPrefix: this.nextConfig.experimental.fetchCacheKeyPrefix,\n          isrFlushToDisk: this.nextConfig.experimental.isrFlushToDisk,\n          maxMemoryCacheSize: this.nextConfig.cacheMaxMemorySize,\n          nextConfigOutput: this.nextConfig.output,\n          buildId: this.buildId,\n          authInterrupts: Boolean(this.nextConfig.experimental.authInterrupts),\n          sriEnabled: Boolean(this.nextConfig.experimental.sri?.algorithm),\n        })\n        return pathsResult\n      } finally {\n        // we don't re-use workers so destroy the used one\n        staticPathsWorker.end()\n      }\n    }\n    const result = this.staticPathsCache.get(pathname)\n\n    const nextInvoke = withCoalescedInvoke(__getStaticPaths)(\n      `staticPaths-${pathname}`,\n      []\n    )\n      .then((res) => {\n        const { prerenderedRoutes: staticPaths, fallbackMode: fallback } =\n          res.value\n        if (!isAppPath && this.nextConfig.output === 'export') {\n          if (fallback === FallbackMode.BLOCKING_STATIC_RENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: blocking\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          } else if (fallback === FallbackMode.PRERENDER) {\n            throw new Error(\n              'getStaticPaths with \"fallback: true\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'\n            )\n          }\n        }\n\n        const value: {\n          staticPaths: string[] | undefined\n          fallbackMode: FallbackMode | undefined\n        } = {\n          staticPaths: staticPaths?.map((route) => route.pathname),\n          fallbackMode: fallback,\n        }\n        this.staticPathsCache.set(pathname, value)\n        return value\n      })\n      .catch((err) => {\n        this.staticPathsCache.remove(pathname)\n        if (!result) throw err\n        Log.error(`Failed to generate static paths for ${pathname}:`)\n        console.error(err)\n      })\n\n    if (result) {\n      return result\n    }\n    return nextInvoke as NonNullable<typeof result>\n  }\n\n  protected async ensurePage(opts: {\n    page: string\n    clientOnly: boolean\n    appPaths?: ReadonlyArray<string> | null\n    definition: RouteDefinition | undefined\n    url?: string\n  }): Promise<void> {\n    await this.bundlerService.ensurePage(opts)\n  }\n\n  protected async findPageComponents({\n    locale,\n    page,\n    query,\n    params,\n    isAppPath,\n    appPaths = null,\n    shouldEnsure,\n    url,\n  }: {\n    locale: string | undefined\n    page: string\n    query: NextParsedUrlQuery\n    params: Params\n    isAppPath: boolean\n    sriEnabled?: boolean\n    appPaths?: ReadonlyArray<string> | null\n    shouldEnsure: boolean\n    url?: string\n  }): Promise<FindComponentsResult | null> {\n    await this.ready?.promise\n\n    const compilationErr = await this.getCompilationError(page)\n    if (compilationErr) {\n      // Wrap build errors so that they don't get logged again\n      throw new WrappedBuildError(compilationErr)\n    }\n    if (shouldEnsure || this.serverOptions.customServer) {\n      await this.ensurePage({\n        page,\n        appPaths,\n        clientOnly: false,\n        definition: undefined,\n        url,\n      })\n    }\n\n    this.nextFontManifest = super.getNextFontManifest()\n\n    return await super.findPageComponents({\n      page,\n      query,\n      params,\n      locale,\n      isAppPath,\n      shouldEnsure,\n      url,\n    })\n  }\n\n  protected async getFallbackErrorComponents(\n    url?: string\n  ): Promise<LoadComponentsReturnType | null> {\n    await this.bundlerService.getFallbackErrorComponents(url)\n    return await loadDefaultErrorComponents(this.distDir)\n  }\n\n  async getCompilationError(page: string): Promise<any> {\n    return await this.bundlerService.getCompilationError(page)\n  }\n\n  protected async instrumentationOnRequestError(\n    ...args: Parameters<ServerOnInstrumentationRequestError>\n  ) {\n    await super.instrumentationOnRequestError(...args)\n\n    const err = args[0]\n    this.logErrorWithOriginalStack(err, 'app-dir')\n  }\n}\n"], "names": ["DevServer", "ReactDevOverlayImpl", "ReactDevOverlay", "props", "undefined", "require", "<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON>", "Server", "getStaticPathsWorker", "worker", "Worker", "resolve", "maxRetries", "numWorkers", "enableWorkerThreads", "nextConfig", "experimental", "workerThreads", "forkOptions", "env", "process", "NODE_OPTIONS", "getFormattedNodeOptionsWithoutInspect", "getStdout", "pipe", "stdout", "getStderr", "stderr", "constructor", "options", "Error", "stackTraceLimit", "dev", "ready", "Detached<PERSON>romise", "bundlerService", "startServerSpan", "trace", "renderOpts", "ErrorDebug", "staticPathsCache", "L<PERSON><PERSON><PERSON>", "length", "value", "JSON", "stringify", "staticPaths", "ampSkipValidation", "amp", "skipValidation", "ampValidator", "html", "pathname", "validatorPath", "validator", "AmpHtmlValidator", "getInstance", "then", "result", "validateString", "ampValidation", "errors", "filter", "e", "severity", "_filterAmpDevelopmentScript", "pagesDir", "appDir", "findPagesDir", "dir", "serverComponentsHmrCache", "cacheMaxMemorySize", "getServerComponentsHmrCache", "getRouteMatchers", "ensurer", "ensure", "match", "ensurePage", "definition", "page", "clientOnly", "url", "matchers", "DevRouteMatcherManager", "extensions", "pageExtensions", "extensionsExpression", "RegExp", "join", "fileReader", "BatchedFileReader", "DefaultFileReader", "pathnameFilter", "test", "push", "DevPagesRouteMatcherProvider", "localeNormalizer", "DevPagesAPIRouteMatcherProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "startsWith", "DevAppPageRouteMatcherProvider", "DevAppRouteRouteMatcherProvider", "getBuildId", "prepareImpl", "setGlobal", "distDir", "PHASE_DEVELOPMENT_SERVER", "telemetry", "Telemetry", "reload", "interceptionRoutePatterns", "getinterceptionRoutePatterns", "on", "reason", "isPostpone", "logErrorWithOriginalStack", "err", "hasPage", "normalizedPath", "normalizePagePath", "console", "error", "isMiddlewareFile", "findPageFile", "Boolean", "appFile", "pagesFile", "runMiddleware", "params", "onWarning", "warn", "waitUntil", "catch", "DecodeError", "MiddlewareNotFoundError", "getProperError", "decorateServerError", "COMPILER_NAMES", "edgeServer", "request", "response", "parsedUrl", "includes", "finished", "statusCode", "renderError", "runEdgeFunction", "onError", "req", "res", "getRequestHandler", "handler", "normalizeReq", "normalizeRes", "loggingConfig", "logging", "start", "Date", "now", "isMiddlewareRequest", "getRequestMeta", "originalResponse", "once", "routeMatch", "logRequests", "requestDurationInMs", "handleRequest", "span", "traceAsyncFn", "promise", "memoryUsage", "<PERSON><PERSON><PERSON><PERSON>", "String", "rss", "heapUsed", "heapTotal", "stop", "run", "basePath", "originalPathname", "pathHasPrefix", "removePathPrefix", "fs", "existsSync", "pathJoin", "publicDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "formatServerError", "sent", "__NEXT_PAGE", "isError", "internalErr", "body", "send", "type", "getPagesManifest", "NodeManifestLoader", "serverDistDir", "PAGES_MANIFEST", "getAppPathsManifest", "enabledDirectories", "app", "APP_PATHS_MANIFEST", "rewrites", "generateInterceptionRoutesRewrites", "Object", "keys", "appPathRoutes", "map", "route", "buildCustomRoute", "regex", "output", "Log", "exit", "getMiddleware", "middleware", "getMiddlewareRouteMatcher", "getNextFontManifest", "hasMiddleware", "actualMiddlewareFile", "ensureMiddleware", "loadInstrumentationModule", "instrumentationModule", "actualInstrumentationHookFile", "INSTRUMENTATION_HOOK_FILENAME", "message", "runInstrumentationHookIfAvailable", "instrumentation", "register", "ensureEdgeFunction", "appPaths", "generateRoutes", "_dev", "event", "code", "snippetChunks", "split", "snippet", "line", "substring", "col", "slice", "indexOf", "getStaticPaths", "requestHeaders", "isAppPath", "__getStaticPaths", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "httpAgentOptions", "locales", "defaultLocale", "i18n", "staticPathsWorker", "pathsResult", "loadStaticPaths", "config", "pprConfig", "ppr", "dynamicIO", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "cacheLifeProfiles", "cacheLife", "fetchCacheKeyPrefix", "isrFlushToDisk", "maxMemoryCacheSize", "nextConfigOutput", "buildId", "authInterrupts", "sriEnabled", "sri", "algorithm", "end", "get", "nextInvoke", "withCoalescedInvoke", "prerenderedRoutes", "fallbackMode", "fallback", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "set", "remove", "opts", "findPageComponents", "locale", "query", "shouldEnsure", "compilationErr", "getCompilationError", "WrappedBuildError", "serverOptions", "customServer", "nextFontManifest", "getFallbackErrorComponents", "loadDefaultErrorComponents", "instrumentationOnRequestError", "args"], "mappings": ";;;;+BAqGA;;;eAAqBA;;;6BAvFd;2DAQQ;4BACQ;sBACU;wBACH;2BAIvB;8BACsB;4BAMtB;oEACmC;mCACR;+BACJ;kCACG;yBACP;uBACkB;8BACf;uBACyB;mCAClB;4CACO;wBACU;6DAChC;iEACmB;wBACP;mCACC;wCACK;8CACM;iDACG;gDACD;iDACC;oCACb;mCACD;mCACA;0BACT;wCACiB;iCACV;4BACL;oDACwB;kCAClB;6BACG;6BAGR;0BACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG7B,wCAAwC;AACxC,IAAIC;AACJ,MAAMC,kBAAuC,CAACC;IAC5C,IAAIF,wBAAwBG,WAAW;QACrCH,sBACEI,QAAQ,qEACLC,eAAe;IACtB;IACA,OAAOL,oBAAoBE;AAC7B;AAmBe,MAAMH,kBAAkBO,mBAAM;IA0BnCC,uBAEN;QACA,MAAMC,SAAS,IAAIC,kBAAM,CAACL,QAAQM,OAAO,CAAC,0BAA0B;YAClEC,YAAY;YACZ,2GAA2G;YAC3G,uCAAuC;YACvCC,YAAY;YACZC,qBAAqB,IAAI,CAACC,UAAU,CAACC,YAAY,CAACC,aAAa;YAC/DC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACd,4GAA4G;oBAC5G,kGAAkG;oBAClG,mGAAmG;oBACnG,0BAA0B;oBAC1BE,cAAcC,IAAAA,4CAAqC;gBACrD;YACF;QACF;QAIAb,OAAOc,SAAS,GAAGC,IAAI,CAACJ,QAAQK,MAAM;QACtChB,OAAOiB,SAAS,GAAGF,IAAI,CAACJ,QAAQO,MAAM;QAEtC,OAAOlB;IACT;IAEAmB,YAAYC,OAAgB,CAAE;YAmB1B,mCAAA;QAlBF,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,OAAM,CAAC;QACT,KAAK,CAAC;YAAE,GAAGF,OAAO;YAAEG,KAAK;QAAK,IA3DhC;;;GAGC,QACOC,QAAS,IAAIC,gCAAe;QAwDlC,IAAI,CAACC,cAAc,GAAGN,QAAQM,cAAc;QAC5C,IAAI,CAACC,eAAe,GAClBP,QAAQO,eAAe,IAAIC,IAAAA,YAAK,EAAC;QACnC,IAAI,CAACC,UAAU,CAACN,GAAG,GAAG;QACtB,IAAI,CAACM,UAAU,CAACC,UAAU,GAAGrC;QAC7B,IAAI,CAACsC,gBAAgB,GAAG,IAAIC,kBAAQ,CAClC,MAAM;QACN,IAAI,OAAO,MACX,SAASC,OAAOC,KAAK;gBACZC;YAAP,OAAOA,EAAAA,kBAAAA,KAAKC,SAAS,CAACF,MAAMG,WAAW,sBAAhCF,gBAAmCF,MAAM,KAAI;QACtD;QAEF,IAAI,CAACJ,UAAU,CAACS,iBAAiB,GAC/B,EAAA,gCAAA,IAAI,CAAChC,UAAU,CAACC,YAAY,sBAA5B,oCAAA,8BAA8BgC,GAAG,qBAAjC,kCAAmCC,cAAc,KAAI;QACvD,IAAI,CAACX,UAAU,CAACY,YAAY,GAAG,CAACC,MAAcC;YAC5C,MAAMC,gBACJ,AAAC,IAAI,CAACtC,UAAU,CAACC,YAAY,IAC3B,IAAI,CAACD,UAAU,CAACC,YAAY,CAACgC,GAAG,IAChC,IAAI,CAACjC,UAAU,CAACC,YAAY,CAACgC,GAAG,CAACM,SAAS,IAC5CjD,QAAQM,OAAO,CACb;YAGJ,MAAM4C,mBACJlD,QAAQ;YACV,OAAOkD,iBAAiBC,WAAW,CAACH,eAAeI,IAAI,CAAC,CAACH;gBACvD,MAAMI,SAASJ,UAAUK,cAAc,CAACR;gBACxCS,IAAAA,qBAAa,EACXR,UACAM,OAAOG,MAAM,CACVC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK,SAC7BF,MAAM,CAAC,CAACC,IAAM,IAAI,CAACE,2BAA2B,CAACd,MAAMY,KACxDL,OAAOG,MAAM,CAACC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,KAAK;YAE/C;QACF;QAEA,MAAM,EAAEE,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAC,IAAI,CAACC,GAAG;QAClD,IAAI,CAACH,QAAQ,GAAGA;QAChB,IAAI,CAACC,MAAM,GAAGA;QAEd,IAAI,IAAI,CAACpD,UAAU,CAACC,YAAY,CAACsD,wBAAwB,EAAE;YACzD,IAAI,CAACA,wBAAwB,GAAG,IAAI7B,kBAAQ,CAC1C,IAAI,CAAC1B,UAAU,CAACwD,kBAAkB,EAClC,SAAS7B,OAAOC,KAAK;gBACnB,OAAOC,KAAKC,SAAS,CAACF,OAAOD,MAAM;YACrC;QAEJ;IACF;IAEmB8B,8BAA8B;QAC/C,OAAO,IAAI,CAACF,wBAAwB;IACtC;IAEUG,mBAAwC;QAChD,MAAM,EAAEP,QAAQ,EAAEC,MAAM,EAAE,GAAGC,IAAAA,0BAAY,EAAC,IAAI,CAACC,GAAG;QAElD,MAAMK,UAAwB;YAC5BC,QAAQ,OAAOC,OAAOxB;gBACpB,MAAM,IAAI,CAACyB,UAAU,CAAC;oBACpBC,YAAYF,MAAME,UAAU;oBAC5BC,MAAMH,MAAME,UAAU,CAACC,IAAI;oBAC3BC,YAAY;oBACZC,KAAK7B;gBACP;YACF;QACF;QAEA,MAAM8B,WAAW,IAAIC,8CAAsB,CACzC,KAAK,CAACV,oBACNC,SACA,IAAI,CAACL,GAAG;QAEV,MAAMe,aAAa,IAAI,CAACrE,UAAU,CAACsE,cAAc;QACjD,MAAMC,uBAAuB,IAAIC,OAAO,CAAC,MAAM,EAAEH,WAAWI,IAAI,CAAC,KAAK,EAAE,CAAC;QAEzE,sEAAsE;QACtE,IAAItB,UAAU;YACZ,MAAMuB,aAAa,IAAIC,oCAAiB,CACtC,IAAIC,oCAAiB,CAAC;gBACpB,qDAAqD;gBACrDC,gBAAgB,CAACxC,WAAakC,qBAAqBO,IAAI,CAACzC;YAC1D;YAGF8B,SAASY,IAAI,CACX,IAAIC,0DAA4B,CAC9B7B,UACAkB,YACAK,YACA,IAAI,CAACO,gBAAgB;YAGzBd,SAASY,IAAI,CACX,IAAIG,gEAA+B,CACjC/B,UACAkB,YACAK,YACA,IAAI,CAACO,gBAAgB;QAG3B;QAEA,IAAI7B,QAAQ;YACV,0EAA0E;YAC1E,yEAAyE;YACzE,qEAAqE;YACrE,oBAAoB;YACpB,MAAMsB,aAAa,IAAIC,oCAAiB,CACtC,IAAIC,oCAAiB,CAAC;gBACpB,oDAAoD;gBACpDO,kBAAkB,CAACC,OAASA,KAAKC,UAAU,CAAC;YAC9C;YAGFlB,SAASY,IAAI,CACX,IAAIO,8DAA8B,CAAClC,QAAQiB,YAAYK;YAEzDP,SAASY,IAAI,CACX,IAAIQ,gEAA+B,CAACnC,QAAQiB,YAAYK;QAE5D;QAEA,OAAOP;IACT;IAEUqB,aAAqB;QAC7B,OAAO;IACT;IAEA,MAAgBC,cAA6B;YAS3C;QARAC,IAAAA,gBAAS,EAAC,WAAW,IAAI,CAACC,OAAO;QACjCD,IAAAA,gBAAS,EAAC,SAASE,oCAAwB;QAE3C,MAAMC,YAAY,IAAIC,kBAAS,CAAC;YAAEH,SAAS,IAAI,CAACA,OAAO;QAAC;QAExD,MAAM,KAAK,CAACF;QACZ,MAAM,IAAI,CAACtB,QAAQ,CAAC4B,MAAM;SAE1B,cAAA,IAAI,CAAC7E,KAAK,qBAAV,YAAYtB,OAAO;QACnB,IAAI,CAACsB,KAAK,GAAG7B;QAEb,4GAA4G;QAC5G,IAAI,CAAC2G,yBAAyB,GAAG,IAAI,CAACC,4BAA4B;QAElE,6CAA6C;QAC7CP,IAAAA,gBAAS,EAAC,UAAU,IAAI,CAACtC,MAAM;QAC/BsC,IAAAA,gBAAS,EAAC,YAAY,IAAI,CAACvC,QAAQ;QACnCuC,IAAAA,gBAAS,EAAC,aAAaG;QAEvBxF,QAAQ6F,EAAE,CAAC,sBAAsB,CAACC;YAChC,IAAIC,IAAAA,sBAAU,EAACD,SAAS;gBACtB,0EAA0E;gBAC1E,qDAAqD;gBACrD;YACF;YACA,IAAI,CAACE,yBAAyB,CAACF,QAAQ;QACzC;QACA9F,QAAQ6F,EAAE,CAAC,qBAAqB,CAACI;YAC/B,IAAI,CAACD,yBAAyB,CAACC,KAAK;QACtC;IACF;IAEA,MAAgBC,QAAQlE,QAAgB,EAAoB;QAC1D,IAAImE;QACJ,IAAI;YACFA,iBAAiBC,IAAAA,oCAAiB,EAACpE;QACrC,EAAE,OAAOiE,KAAK;YACZI,QAAQC,KAAK,CAACL;YACd,wDAAwD;YACxD,sDAAsD;YACtD,yCAAyC;YACzC,OAAO;QACT;QAEA,IAAIM,IAAAA,wBAAgB,EAACJ,iBAAiB;YACpC,OAAOK,IAAAA,0BAAY,EACjB,IAAI,CAACvD,GAAG,EACRkD,gBACA,IAAI,CAACxG,UAAU,CAACsE,cAAc,EAC9B,OACA5B,IAAI,CAACoE;QACT;QAEA,IAAIC,UAAyB;QAC7B,IAAIC,YAA2B;QAE/B,IAAI,IAAI,CAAC5D,MAAM,EAAE;YACf2D,UAAU,MAAMF,IAAAA,0BAAY,EAC1B,IAAI,CAACzD,MAAM,EACXoD,iBAAiB,SACjB,IAAI,CAACxG,UAAU,CAACsE,cAAc,EAC9B;QAEJ;QAEA,IAAI,IAAI,CAACnB,QAAQ,EAAE;YACjB6D,YAAY,MAAMH,IAAAA,0BAAY,EAC5B,IAAI,CAAC1D,QAAQ,EACbqD,gBACA,IAAI,CAACxG,UAAU,CAACsE,cAAc,EAC9B;QAEJ;QACA,IAAIyC,WAAWC,WAAW;YACxB,OAAO;QACT;QAEA,OAAOF,QAAQC,WAAWC;IAC5B;IAEA,MAAMC,cAAcC,MAMnB,EAAE;QACD,IAAI;YACF,MAAMvE,SAAS,MAAM,KAAK,CAACsE,cAAc;gBACvC,GAAGC,MAAM;gBACTC,WAAW,CAACC;oBACV,IAAI,CAACf,yBAAyB,CAACe,MAAM;gBACvC;YACF;YAEA,IAAI,cAAczE,QAAQ;gBACxB,OAAOA;YACT;YAEAA,OAAO0E,SAAS,CAACC,KAAK,CAAC,CAACX;gBACtB,IAAI,CAACN,yBAAyB,CAACM,OAAO;YACxC;YACA,OAAOhE;QACT,EAAE,OAAOgE,OAAO;YACd,IAAIA,iBAAiBY,mBAAW,EAAE;gBAChC,MAAMZ;YACR;YAEA;;;;OAIC,GACD,IAAI,CAAEA,CAAAA,iBAAiBa,+BAAuB,AAAD,GAAI;gBAC/C,IAAI,CAACnB,yBAAyB,CAACM;YACjC;YAEA,MAAML,MAAMmB,IAAAA,uBAAc,EAACd;YAC3Be,IAAAA,gCAAmB,EAACpB,KAAKqB,0BAAc,CAACC,UAAU;YAClD,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGb;YAEzC;;;;OAIC,GACD,IACEW,QAAQ3D,GAAG,CAAC8D,QAAQ,CAAC,oBACrBH,QAAQ3D,GAAG,CAAC8D,QAAQ,CAAC,qCACrBH,QAAQ3D,GAAG,CAAC8D,QAAQ,CAAC,2BACrBH,QAAQ3D,GAAG,CAAC8D,QAAQ,CAAC,6BACrB;gBACA,OAAO;oBAAEC,UAAU;gBAAM;YAC3B;YAEAH,SAASI,UAAU,GAAG;YACtB,MAAM,IAAI,CAACC,WAAW,CAAC7B,KAAKuB,SAASC,UAAUC,UAAU1F,QAAQ;YACjE,OAAO;gBAAE4F,UAAU;YAAK;QAC1B;IACF;IAEA,MAAMG,gBAAgBlB,MAQrB,EAAE;QACD,IAAI;YACF,OAAO,KAAK,CAACkB,gBAAgB;gBAC3B,GAAGlB,MAAM;gBACTmB,SAAS,CAAC/B,MAAQ,IAAI,CAACD,yBAAyB,CAACC,KAAK;gBACtDa,WAAW,CAACC;oBACV,IAAI,CAACf,yBAAyB,CAACe,MAAM;gBACvC;YACF;QACF,EAAE,OAAOT,OAAO;YACd,IAAIA,iBAAiBY,mBAAW,EAAE;gBAChC,MAAMZ;YACR;YACA,IAAI,CAACN,yBAAyB,CAACM,OAAO;YACtC,MAAML,MAAMmB,IAAAA,uBAAc,EAACd;YAC3B,MAAM,EAAE2B,GAAG,EAAEC,GAAG,EAAEvE,IAAI,EAAE,GAAGkD;YAE3BqB,IAAIL,UAAU,GAAG;YACjB,MAAM,IAAI,CAACC,WAAW,CAAC7B,KAAKgC,KAAKC,KAAKvE;YACtC,OAAO;QACT;IACF;IAEOwE,oBAAwC;QAC7C,MAAMC,UAAU,KAAK,CAACD;QAEtB,OAAO,CAACF,KAAKC,KAAKR;YAChB,MAAMF,UAAU,IAAI,CAACa,YAAY,CAACJ;YAClC,MAAMR,WAAW,IAAI,CAACa,YAAY,CAACJ;YACnC,MAAMK,gBAAgB,IAAI,CAAC5I,UAAU,CAAC6I,OAAO;YAE7C,IAAID,kBAAkB,OAAO;gBAC3B,MAAME,QAAQC,KAAKC,GAAG;gBACtB,MAAMC,sBAAsBC,IAAAA,2BAAc,EAACZ,KAAK;gBAEhD,IAAI,CAACW,qBAAqB;oBACxBnB,SAASqB,gBAAgB,CAACC,IAAI,CAAC,SAAS;wBACtC,oEAAoE;wBACpE,sEAAsE;wBACtE,gCAAgC;wBAChC,MAAMC,aAAaH,IAAAA,2BAAc,EAACZ,KAAKzE,KAAK;wBAE5C,IAAI,CAACwF,YAAY;4BACf;wBACF;wBAEAC,IAAAA,wBAAW,EAAC;4BACVzB;4BACAC;4BACAc;4BACAW,qBAAqBR,KAAKC,GAAG,KAAKF;wBACpC;oBACF;gBACF;YACF;YAEA,OAAOL,QAAQZ,SAASC,UAAUC;QACpC;IACF;IAEA,MAAayB,cACXlB,GAAoB,EACpBC,GAAqB,EACrBR,SAAkC,EACnB;QACf,MAAM0B,OAAOnI,IAAAA,YAAK,EAAC,kBAAkBjC,WAAW;YAAE6E,KAAKoE,IAAIpE,GAAG;QAAC;QAC/D,MAAMvB,SAAS,MAAM8G,KAAKC,YAAY,CAAC;gBAC/B;YAAN,QAAM,cAAA,IAAI,CAACxI,KAAK,qBAAV,YAAYyI,OAAO;YACzB,OAAO,MAAM,KAAK,CAACH,cAAclB,KAAKC,KAAKR;QAC7C;QACA,MAAM6B,cAAcvJ,QAAQuJ,WAAW;QACvCH,KACGI,UAAU,CAAC,gBAAgB;YAC1B3F,KAAKoE,IAAIpE,GAAG;YACZ,cAAc4F,OAAOF,YAAYG,GAAG;YACpC,mBAAmBD,OAAOF,YAAYI,QAAQ;YAC9C,oBAAoBF,OAAOF,YAAYK,SAAS;QAClD,GACCC,IAAI;QACP,OAAOvH;IACT;IAEA,MAAMwH,IACJ7B,GAAoB,EACpBC,GAAqB,EACrBR,SAA6B,EACd;YACT;QAAN,QAAM,cAAA,IAAI,CAAC7G,KAAK,qBAAV,YAAYyI,OAAO;QAEzB,MAAM,EAAES,QAAQ,EAAE,GAAG,IAAI,CAACpK,UAAU;QACpC,IAAIqK,mBAAkC;QAEtC,gDAAgD;QAChD,IAAID,YAAYE,IAAAA,4BAAa,EAACvC,UAAU1F,QAAQ,IAAI,KAAK+H,WAAW;YAClE,6CAA6C;YAC7C,uGAAuG;YACvGC,mBAAmBtC,UAAU1F,QAAQ;YACrC0F,UAAU1F,QAAQ,GAAGkI,IAAAA,kCAAgB,EAACxC,UAAU1F,QAAQ,IAAI,KAAK+H;QACnE;QAEA,MAAM,EAAE/H,QAAQ,EAAE,GAAG0F;QAErB,IAAI1F,SAAUgD,UAAU,CAAC,WAAW;YAClC,IAAImF,WAAE,CAACC,UAAU,CAACC,IAAAA,UAAQ,EAAC,IAAI,CAACC,SAAS,EAAE,WAAW;gBACpD,MAAM,qBAAyC,CAAzC,IAAI5J,MAAM6J,yCAA8B,GAAxC,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,IAAIP,kBAAkB;YACpB,oFAAoF;YACpF,mDAAmD;YACnDtC,UAAU1F,QAAQ,GAAGgI;QACvB;QACA,IAAI;YACF,OAAO,MAAM,KAAK,CAACF,IAAI7B,KAAKC,KAAKR;QACnC,EAAE,OAAOpB,OAAO;YACd,MAAML,MAAMmB,IAAAA,uBAAc,EAACd;YAC3BkE,IAAAA,oCAAiB,EAACvE;YAClB,IAAI,CAACD,yBAAyB,CAACC;YAC/B,IAAI,CAACiC,IAAIuC,IAAI,EAAE;gBACbvC,IAAIL,UAAU,GAAG;gBACjB,IAAI;oBACF,OAAO,MAAM,IAAI,CAACC,WAAW,CAAC7B,KAAKgC,KAAKC,KAAKlG,UAAW;wBACtD0I,aAAa,AAACC,IAAAA,gBAAO,EAAC1E,QAAQA,IAAItC,IAAI,IAAK3B,YAAY;oBACzD;gBACF,EAAE,OAAO4I,aAAa;oBACpBvE,QAAQC,KAAK,CAACsE;oBACd1C,IAAI2C,IAAI,CAAC,yBAAyBC,IAAI;gBACxC;YACF;QACF;IACF;IAEU9E,0BACRC,GAAa,EACb8E,IAAyE,EACnE;QACN,IAAI,CAAChK,cAAc,CAACiF,yBAAyB,CAACC,KAAK8E;IACrD;IAEUC,mBAA8C;QACtD,OACEC,sCAAkB,CAAChM,OAAO,CACxBoL,IAAAA,UAAQ,EAAC,IAAI,CAACa,aAAa,EAAEC,0BAAc,MACxCnM;IAET;IAEUoM,sBAAiD;QACzD,IAAI,CAAC,IAAI,CAACC,kBAAkB,CAACC,GAAG,EAAE,OAAOtM;QAEzC,OACEiM,sCAAkB,CAAChM,OAAO,CACxBoL,IAAAA,UAAQ,EAAC,IAAI,CAACa,aAAa,EAAEK,8BAAkB,MAC5CvM;IAET;IAEU4G,+BAAyC;QACjD,MAAM4F,WAAWC,IAAAA,sEAAkC,EACjDC,OAAOC,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,CAAC,IACnC,IAAI,CAACjM,UAAU,CAACoK,QAAQ,EACxB8B,GAAG,CAAC,CAACC,QAAU,IAAI3H,OAAO4H,IAAAA,kCAAgB,EAAC,WAAWD,OAAOE,KAAK;QAEpE,IAAI,IAAI,CAACrM,UAAU,CAACsM,MAAM,KAAK,YAAYT,SAASlK,MAAM,GAAG,GAAG;YAC9D4K,KAAI5F,KAAK,CACP;YAGFtG,QAAQmM,IAAI,CAAC;QACf;QAEA,OAAOX,YAAY,EAAE;IACvB;IAEA,MAAgBY,gBAAgB;YAG1B;QAFJ,gCAAgC;QAChC,iCAAiC;QACjC,IAAI,EAAA,mBAAA,IAAI,CAACC,UAAU,qBAAf,iBAAiB7I,KAAK,MAAK,MAAM;YACnC,IAAI,CAAC6I,UAAU,CAAC7I,KAAK,GAAG8I,IAAAA,iDAAyB,EAC/C,IAAI,CAACD,UAAU,CAACvI,QAAQ,IAAI,EAAE;QAElC;QACA,OAAO,IAAI,CAACuI,UAAU;IACxB;IAEUE,sBAAsB;QAC9B,OAAOvN;IACT;IAEA,MAAgBwN,gBAAkC;QAChD,OAAO,IAAI,CAACtG,OAAO,CAAC,IAAI,CAACuG,oBAAoB;IAC/C;IAEA,MAAgBC,iBAAiB7I,GAAW,EAAE;QAC5C,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAAC8I,oBAAoB;YAC/B7I,YAAY;YACZF,YAAY1E;YACZ6E;QACF;IACF;IAEA,MAAgB8I,4BAA0C;QACxD,IAAIC;QACJ,IACE,IAAI,CAACC,6BAA6B,IACjC,MAAM,IAAI,CAACpJ,UAAU,CAAC;YACrBE,MAAM,IAAI,CAACkJ,6BAA6B;YACxCjJ,YAAY;YACZF,YAAY1E;QACd,GACGqD,IAAI,CAAC,IAAM,MACX4E,KAAK,CAAC,IAAM,QACf;YACA,IAAI;gBACF2F,wBAAwB,MAAM3N,QAC5BoL,IAAAA,UAAQ,EAAC,IAAI,CAAC/E,OAAO,EAAE,UAAUwH,wCAA6B;YAElE,EAAE,OAAO7G,KAAU;gBACjBA,IAAI8G,OAAO,GAAG,CAAC,sDAAsD,EAAE9G,IAAI8G,OAAO,EAAE;gBACpF,MAAM9G;YACR;QACF;QACA,OAAO2G;IACT;IAEA,MAAgBI,oCAAoC;QAClD,MAAM,IAAI,CAAChM,eAAe,CACvBwI,UAAU,CAAC,4BACXH,YAAY,CAAC;gBAAM,gCAAA;oBAAA,wBAAA,IAAI,CAAC4D,eAAe,sBAApB,iCAAA,sBAAsBC,QAAQ,qBAA9B,oCAAA;;IACxB;IAEA,MAAgBC,mBAAmB,EACjCxJ,IAAI,EACJyJ,QAAQ,EACRvJ,GAAG,EAKJ,EAAE;QACD,OAAO,IAAI,CAACJ,UAAU,CAAC;YACrBE;YACAyJ;YACAxJ,YAAY;YACZF,YAAY1E;YACZ6E;QACF;IACF;IAEAwJ,eAAeC,IAAc,EAAE;IAC7B,0FAA0F;IAC1F,uFAAuF;IACvF,mBAAmB;IACnB,sDAAsD;IACtD,mBAAmB;IACnB,wCAAwC;IACxC,sCAAsC;IACtC,+DAA+D;IAC/D,0CAA0C;IAC1C,eAAe;IACf,wBAAwB;IACxB,QAAQ;IACR,OAAO;IACP,KAAK;IACP;IAEAzK,4BACEd,IAAY,EACZwL,KAAkD,EACzC;QACT,IAAIA,MAAMC,IAAI,KAAK,yBAAyB;YAC1C,OAAO;QACT;QAEA,MAAMC,gBAAgB1L,KAAK2L,KAAK,CAAC;QAEjC,IAAIC;QACJ,IACE,CAAEA,CAAAA,UAAU5L,KAAK2L,KAAK,CAAC,KAAK,CAACH,MAAMK,IAAI,GAAG,EAAE,AAAD,KAC3C,CAAED,CAAAA,UAAUA,QAAQE,SAAS,CAACN,MAAMO,GAAG,CAAA,GACvC;YACA,OAAO;QACT;QAEAH,UAAUA,UAAUF,cAAcM,KAAK,CAACR,MAAMK,IAAI,EAAExJ,IAAI,CAAC;QACzDuJ,UAAUA,QAAQE,SAAS,CAAC,GAAGF,QAAQK,OAAO,CAAC;QAE/C,OAAO,CAACL,QAAQhG,QAAQ,CAAC;IAC3B;IAEA,MAAgBsG,eAAe,EAC7BjM,QAAQ,EACRkM,cAAc,EACdvK,IAAI,EACJwK,SAAS,EAMV,EAGE;QACD,mDAAmD;QACnD,wDAAwD;QAExD,MAAMC,mBAAmB;YACvB,MAAM,EACJC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EACjB,GAAG,IAAI,CAAC7O,UAAU;YACnB,MAAM,EAAE8O,OAAO,EAAEC,aAAa,EAAE,GAAG,IAAI,CAAC/O,UAAU,CAACgP,IAAI,IAAI,CAAC;YAC5D,MAAMC,oBAAoB,IAAI,CAACxP,oBAAoB;YAEnD,IAAI;oBA2BoB;gBA1BtB,MAAMyP,cAAc,MAAMD,kBAAkBE,eAAe,CAAC;oBAC1D7L,KAAK,IAAI,CAACA,GAAG;oBACbqC,SAAS,IAAI,CAACA,OAAO;oBACrBtD;oBACA+M,QAAQ;wBACNC,WAAW,IAAI,CAACrP,UAAU,CAACC,YAAY,CAACqP,GAAG;wBAC3CZ;wBACAC;wBACAC;wBACAW,WAAWzI,QAAQ,IAAI,CAAC9G,UAAU,CAACC,YAAY,CAACsP,SAAS;oBAC3D;oBACAV;oBACAC;oBACAC;oBACA/K;oBACAwK;oBACAD;oBACAiB,cAAc,IAAI,CAACxP,UAAU,CAACwP,YAAY;oBAC1CC,eAAe,IAAI,CAACzP,UAAU,CAACC,YAAY,CAACwP,aAAa;oBACzDC,mBAAmB,IAAI,CAAC1P,UAAU,CAACC,YAAY,CAAC0P,SAAS;oBACzDC,qBAAqB,IAAI,CAAC5P,UAAU,CAACC,YAAY,CAAC2P,mBAAmB;oBACrEC,gBAAgB,IAAI,CAAC7P,UAAU,CAACC,YAAY,CAAC4P,cAAc;oBAC3DC,oBAAoB,IAAI,CAAC9P,UAAU,CAACwD,kBAAkB;oBACtDuM,kBAAkB,IAAI,CAAC/P,UAAU,CAACsM,MAAM;oBACxC0D,SAAS,IAAI,CAACA,OAAO;oBACrBC,gBAAgBnJ,QAAQ,IAAI,CAAC9G,UAAU,CAACC,YAAY,CAACgQ,cAAc;oBACnEC,YAAYpJ,SAAQ,oCAAA,IAAI,CAAC9G,UAAU,CAACC,YAAY,CAACkQ,GAAG,qBAAhC,kCAAkCC,SAAS;gBACjE;gBACA,OAAOlB;YACT,SAAU;gBACR,kDAAkD;gBAClDD,kBAAkBoB,GAAG;YACvB;QACF;QACA,MAAM1N,SAAS,IAAI,CAAClB,gBAAgB,CAAC6O,GAAG,CAACjO;QAEzC,MAAMkO,aAAaC,IAAAA,sCAAmB,EAAC/B,kBACrC,CAAC,YAAY,EAAEpM,UAAU,EACzB,EAAE,EAEDK,IAAI,CAAC,CAAC6F;YACL,MAAM,EAAEkI,mBAAmB1O,WAAW,EAAE2O,cAAcC,QAAQ,EAAE,GAC9DpI,IAAI3G,KAAK;YACX,IAAI,CAAC4M,aAAa,IAAI,CAACxO,UAAU,CAACsM,MAAM,KAAK,UAAU;gBACrD,IAAIqE,aAAaC,sBAAY,CAACC,sBAAsB,EAAE;oBACpD,MAAM,qBAEL,CAFK,IAAI9P,MACR,oKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAI4P,aAAaC,sBAAY,CAACE,SAAS,EAAE;oBAC9C,MAAM,qBAEL,CAFK,IAAI/P,MACR,gKADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMa,QAGF;gBACFG,WAAW,EAAEA,+BAAAA,YAAamK,GAAG,CAAC,CAACC,QAAUA,MAAM9J,QAAQ;gBACvDqO,cAAcC;YAChB;YACA,IAAI,CAAClP,gBAAgB,CAACsP,GAAG,CAAC1O,UAAUT;YACpC,OAAOA;QACT,GACC0F,KAAK,CAAC,CAAChB;YACN,IAAI,CAAC7E,gBAAgB,CAACuP,MAAM,CAAC3O;YAC7B,IAAI,CAACM,QAAQ,MAAM2D;YACnBiG,KAAI5F,KAAK,CAAC,CAAC,oCAAoC,EAAEtE,SAAS,CAAC,CAAC;YAC5DqE,QAAQC,KAAK,CAACL;QAChB;QAEF,IAAI3D,QAAQ;YACV,OAAOA;QACT;QACA,OAAO4N;IACT;IAEA,MAAgBzM,WAAWmN,IAM1B,EAAiB;QAChB,MAAM,IAAI,CAAC7P,cAAc,CAAC0C,UAAU,CAACmN;IACvC;IAEA,MAAgBC,mBAAmB,EACjCC,MAAM,EACNnN,IAAI,EACJoN,KAAK,EACLlK,MAAM,EACNsH,SAAS,EACTf,WAAW,IAAI,EACf4D,YAAY,EACZnN,GAAG,EAWJ,EAAwC;YACjC;QAAN,QAAM,cAAA,IAAI,CAAChD,KAAK,qBAAV,YAAYyI,OAAO;QAEzB,MAAM2H,iBAAiB,MAAM,IAAI,CAACC,mBAAmB,CAACvN;QACtD,IAAIsN,gBAAgB;YAClB,wDAAwD;YACxD,MAAM,IAAIE,6BAAiB,CAACF;QAC9B;QACA,IAAID,gBAAgB,IAAI,CAACI,aAAa,CAACC,YAAY,EAAE;YACnD,MAAM,IAAI,CAAC5N,UAAU,CAAC;gBACpBE;gBACAyJ;gBACAxJ,YAAY;gBACZF,YAAY1E;gBACZ6E;YACF;QACF;QAEA,IAAI,CAACyN,gBAAgB,GAAG,KAAK,CAAC/E;QAE9B,OAAO,MAAM,KAAK,CAACsE,mBAAmB;YACpClN;YACAoN;YACAlK;YACAiK;YACA3C;YACA6C;YACAnN;QACF;IACF;IAEA,MAAgB0N,2BACd1N,GAAY,EAC8B;QAC1C,MAAM,IAAI,CAAC9C,cAAc,CAACwQ,0BAA0B,CAAC1N;QACrD,OAAO,MAAM2N,IAAAA,sDAA0B,EAAC,IAAI,CAAClM,OAAO;IACtD;IAEA,MAAM4L,oBAAoBvN,IAAY,EAAgB;QACpD,OAAO,MAAM,IAAI,CAAC5C,cAAc,CAACmQ,mBAAmB,CAACvN;IACvD;IAEA,MAAgB8N,8BACd,GAAGC,IAAqD,EACxD;QACA,MAAM,KAAK,CAACD,iCAAiCC;QAE7C,MAAMzL,MAAMyL,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC1L,yBAAyB,CAACC,KAAK;IACtC;AACF"}