{"version": 3, "sources": ["../../../../src/server/typescript/rules/metadata.ts"], "sourcesContent": ["import { NEXT_TS_ERRORS } from '../constant'\nimport {\n  getSource,\n  getSourceFromVirtualTsEnv,\n  getTs,\n  getType<PERSON><PERSON><PERSON>,\n  isPositionInsideNode,\n  log,\n  virtualTsEnv,\n} from '../utils'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst TYPE_ANNOTATION = ': Metadata | null'\nconst TYPE_ANNOTATION_ASYNC = ': Promise<Metadata | null>'\nconst TYPE_IMPORT = `\\n\\nimport type { Metadata } from 'next'`\n\n// Find the `export const metadata = ...` node.\nfunction getMetadataExport(fileName: string, position: number) {\n  const source = getSource(fileName)\n  let metadataExport: tsModule.VariableDeclaration | undefined\n\n  if (source) {\n    const ts = getTs()\n    ts.forEachChild(source, function visit(node) {\n      if (metadataExport) return\n\n      // Covered by this node\n      if (isPositionInsideNode(position, node)) {\n        // Export variable\n        if (\n          ts.isVariableStatement(node) &&\n          node.modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)\n        ) {\n          if (ts.isVariableDeclarationList(node.declarationList)) {\n            for (const declaration of node.declarationList.declarations) {\n              if (\n                isPositionInsideNode(position, declaration) &&\n                declaration.name.getText() === 'metadata'\n              ) {\n                // `export const metadata = ...`\n                metadataExport = declaration\n                return\n              }\n            }\n          }\n        }\n      }\n    })\n  }\n  return metadataExport\n}\n\nfunction updateVirtualFileWithType(\n  fileName: string,\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration,\n  isGenerateMetadata?: boolean\n) {\n  const source = getSource(fileName)\n  if (!source) return\n\n  // We annotate with the type in a virtual language service\n  const sourceText = source.getFullText()\n  let nodeEnd: number\n  let annotation: string\n\n  const ts = getTs()\n  if (ts.isFunctionDeclaration(node)) {\n    if (isGenerateMetadata) {\n      nodeEnd = node.body!.getFullStart()\n      const isAsync = node.modifiers?.some(\n        (m) => m.kind === ts.SyntaxKind.AsyncKeyword\n      )\n      annotation = isAsync ? TYPE_ANNOTATION_ASYNC : TYPE_ANNOTATION\n    } else {\n      return\n    }\n  } else {\n    nodeEnd = node.name.getFullStart() + node.name.getFullWidth()\n    annotation = TYPE_ANNOTATION\n  }\n\n  const newSource =\n    sourceText.slice(0, nodeEnd) +\n    annotation +\n    sourceText.slice(nodeEnd) +\n    TYPE_IMPORT\n\n  if (virtualTsEnv.getSourceFile(fileName)) {\n    log('Updating file: ' + fileName)\n    virtualTsEnv.updateFile(fileName, newSource)\n  } else {\n    log('Creating file: ' + fileName)\n    virtualTsEnv.createFile(fileName, newSource)\n  }\n\n  return [nodeEnd, annotation.length]\n}\n\nfunction isTyped(\n  node: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  return node.type !== undefined\n}\n\nfunction proxyDiagnostics(\n  fileName: string,\n  pos: number[],\n  n: tsModule.VariableDeclaration | tsModule.FunctionDeclaration\n) {\n  // Get diagnostics\n  const diagnostics =\n    virtualTsEnv.languageService.getSemanticDiagnostics(fileName)\n  const source = getSourceFromVirtualTsEnv(fileName)\n\n  // Filter and map the results\n  return diagnostics\n    .filter((d) => {\n      if (d.start === undefined || d.length === undefined) return false\n      if (d.start < n.getFullStart()) return false\n      if (d.start + d.length >= n.getFullStart() + n.getFullWidth() + pos[1])\n        return false\n      return true\n    })\n    .map((d) => {\n      return {\n        file: source,\n        category: d.category,\n        code: d.code,\n        messageText: d.messageText,\n        start: d.start! < pos[0] ? d.start : d.start! - pos[1],\n        length: d.length,\n      }\n    })\n}\n\nconst metadata = {\n  filterCompletionsAtPosition(\n    fileName: string,\n    position: number,\n    _options: any,\n    prior: tsModule.WithMetadata<tsModule.CompletionInfo>\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return prior\n    if (isTyped(node)) return prior\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return prior\n\n    // Get completions\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const completions = virtualTsEnv.languageService.getCompletionsAtPosition(\n      fileName,\n      newPos,\n      undefined\n    )\n\n    if (completions) {\n      const ts = getTs()\n      completions.isIncomplete = true\n      // https://github.com/microsoft/TypeScript/blob/4dc677b292354f4b9162452b2e00f4d7dd118221/src/services/types.ts#L1428-L1433\n      if (completions.optionalReplacementSpan) {\n        // Adjust the start position of the text span to original source.\n        completions.optionalReplacementSpan.start -= newPos - position\n      }\n      completions.entries = completions.entries\n        .filter((e) => {\n          return [\n            ts.ScriptElementKind.memberVariableElement,\n            ts.ScriptElementKind.typeElement,\n            ts.ScriptElementKind.string,\n          ].includes(e.kind)\n        })\n        .map((e) => {\n          const insertText =\n            e.kind === ts.ScriptElementKind.memberVariableElement &&\n            /^[a-zA-Z0-9_]+$/.test(e.name)\n              ? e.name + ': '\n              : e.name\n\n          return {\n            name: e.name,\n            insertText,\n            kind: e.kind,\n            kindModifiers: e.kindModifiers,\n            sortText: '!' + e.name,\n            labelDetails: {\n              description: `Next.js metadata`,\n            },\n            data: e.data,\n          }\n        })\n\n      return completions\n    }\n\n    return prior\n  },\n\n  getSemanticDiagnosticsForExportVariableStatementInClientEntry(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const source = getSource(fileName)\n    const ts = getTs()\n\n    // It is not allowed to export `metadata` or `generateMetadata` in client entry\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        return [\n          {\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js 'generateMetadata' API is not allowed in a client component.`,\n            start: node.name.getStart(),\n            length: node.name.getWidth(),\n          },\n        ]\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        const name = declaration.name.getText()\n        if (name === 'metadata') {\n          return [\n            {\n              file: source,\n              category: ts.DiagnosticCategory.Error,\n              code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n              messageText: `The Next.js 'metadata' API is not allowed in a client component.`,\n              start: declaration.name.getStart(),\n              length: declaration.name.getWidth(),\n            },\n          ]\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportVariableStatement(\n    fileName: string,\n    node: tsModule.VariableStatement | tsModule.FunctionDeclaration\n  ) {\n    const ts = getTs()\n\n    if (ts.isFunctionDeclaration(node)) {\n      if (node.name?.getText() === 'generateMetadata') {\n        if (isTyped(node)) return []\n\n        // We annotate with the type in a virtual language service\n        const pos = updateVirtualFileWithType(fileName, node, true)\n        if (!pos) return []\n\n        return proxyDiagnostics(fileName, pos, node)\n      }\n    } else {\n      for (const declaration of node.declarationList.declarations) {\n        if (declaration.name.getText() === 'metadata') {\n          if (isTyped(declaration)) break\n\n          // We annotate with the type in a virtual language service\n          const pos = updateVirtualFileWithType(fileName, declaration)\n          if (!pos) break\n\n          return proxyDiagnostics(fileName, pos, declaration)\n        }\n      }\n    }\n    return []\n  },\n\n  getSemanticDiagnosticsForExportDeclarationInClientEntry(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n    const source = getSource(fileName)\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (['generateMetadata', 'metadata'].includes(e.name.getText())) {\n          diagnostics.push({\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n            messageText: `The Next.js '${e.name.getText()}' API is not allowed in a client component.`,\n            start: e.name.getStart(),\n            length: e.name.getWidth(),\n          })\n        }\n      }\n    }\n\n    return diagnostics\n  },\n\n  getSemanticDiagnosticsForExportDeclaration(\n    fileName: string,\n    node: tsModule.ExportDeclaration\n  ) {\n    const ts = getTs()\n\n    const exportClause = node.exportClause\n    if (exportClause && ts.isNamedExports(exportClause)) {\n      for (const e of exportClause.elements) {\n        if (e.name.getText() === 'metadata') {\n          // Get the original declaration node of element\n          const typeChecker = getTypeChecker()\n          if (typeChecker) {\n            const symbol = typeChecker.getSymbolAtLocation(e.name)\n            if (symbol) {\n              const metadataSymbol = typeChecker.getAliasedSymbol(symbol)\n              if (metadataSymbol && metadataSymbol.declarations) {\n                const declaration = metadataSymbol.declarations[0]\n                if (declaration && ts.isVariableDeclaration(declaration)) {\n                  if (isTyped(declaration)) break\n\n                  const declarationFileName =\n                    declaration.getSourceFile().fileName\n                  const isSameFile = declarationFileName === fileName\n\n                  // We annotate with the type in a virtual language service\n                  const pos = updateVirtualFileWithType(\n                    declarationFileName,\n                    declaration\n                  )\n                  if (!pos) break\n\n                  const diagnostics = proxyDiagnostics(\n                    declarationFileName,\n                    pos,\n                    declaration\n                  )\n                  if (diagnostics.length) {\n                    if (isSameFile) {\n                      return diagnostics\n                    } else {\n                      return [\n                        {\n                          file: getSource(fileName),\n                          category: ts.DiagnosticCategory.Error,\n                          code: NEXT_TS_ERRORS.INVALID_METADATA_EXPORT,\n                          messageText: `The 'metadata' export value is not typed correctly, please make sure it is typed as 'Metadata':\\nhttps://nextjs.org/docs/app/building-your-application/optimizing/metadata#static-metadata`,\n                          start: e.name.getStart(),\n                          length: e.name.getWidth(),\n                        },\n                      ]\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return []\n  },\n\n  getCompletionEntryDetails(\n    fileName: string,\n    position: number,\n    entryName: string,\n    formatOptions: tsModule.FormatCodeOptions,\n    source: string,\n    preferences: tsModule.UserPreferences,\n    data: tsModule.CompletionEntryData\n  ) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const details = virtualTsEnv.languageService.getCompletionEntryDetails(\n      fileName,\n      newPos,\n      entryName,\n      formatOptions,\n      source,\n      preferences,\n      data\n    )\n    return details\n  },\n\n  getQuickInfoAtPosition(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n\n    const newPos = position <= pos[0] ? position : position + pos[1]\n    const insight = virtualTsEnv.languageService.getQuickInfoAtPosition(\n      fileName,\n      newPos\n    )\n    return insight\n  },\n\n  getDefinitionAndBoundSpan(fileName: string, position: number) {\n    const node = getMetadataExport(fileName, position)\n    if (!node) return\n    if (isTyped(node)) return\n    if (!isPositionInsideNode(position, node)) return\n    // We annotate with the type in a virtual language service\n    const pos = updateVirtualFileWithType(fileName, node)\n    if (pos === undefined) return\n    const newPos = position <= pos[0] ? position : position + pos[1]\n\n    const definitionInfoAndBoundSpan =\n      virtualTsEnv.languageService.getDefinitionAndBoundSpan(fileName, newPos)\n\n    if (definitionInfoAndBoundSpan) {\n      // Adjust the start position of the text span\n      if (definitionInfoAndBoundSpan.textSpan.start > pos[0]) {\n        definitionInfoAndBoundSpan.textSpan.start -= pos[1]\n      }\n    }\n    return definitionInfoAndBoundSpan\n  },\n}\n\nexport default metadata\n"], "names": ["NEXT_TS_ERRORS", "getSource", "getSourceFromVirtualTsEnv", "getTs", "getType<PERSON><PERSON>cker", "isPositionInsideNode", "log", "virtualTsEnv", "TYPE_ANNOTATION", "TYPE_ANNOTATION_ASYNC", "TYPE_IMPORT", "getMetadataExport", "fileName", "position", "source", "metadataExport", "ts", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "name", "getText", "updateVirtualFileWithType", "isGenerateMetadata", "sourceText", "getFullText", "nodeEnd", "annotation", "isFunctionDeclaration", "body", "getFullStart", "isAsync", "AsyncKeyword", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSource", "slice", "getSourceFile", "updateFile", "createFile", "length", "isTyped", "type", "undefined", "proxyDiagnostics", "pos", "n", "diagnostics", "languageService", "getSemanticDiagnostics", "filter", "d", "start", "map", "file", "category", "code", "messageText", "metadata", "filterCompletionsAtPosition", "_options", "prior", "newPos", "completions", "getCompletionsAtPosition", "isIncomplete", "optionalReplacementSpan", "entries", "e", "ScriptElementKind", "memberVariableElement", "typeElement", "string", "includes", "insertText", "test", "kindModifiers", "sortText", "labelDetails", "description", "data", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "DiagnosticCategory", "Error", "INVALID_METADATA_EXPORT", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "exportClause", "isNamedExports", "elements", "push", "getSemanticDiagnosticsForExportDeclaration", "typeC<PERSON>cker", "symbol", "getSymbolAtLocation", "metadataSymbol", "getAliasedSymbol", "isVariableDeclaration", "declarationFileName", "isSameFile", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "details", "getQuickInfoAtPosition", "insight", "getDefinitionAndBoundSpan", "definitionInfoAndBoundSpan", "textSpan"], "mappings": "AAAA,SAASA,cAAc,QAAQ,cAAa;AAC5C,SACEC,SAAS,EACTC,yBAAyB,EACzBC,KAAK,EACLC,cAAc,EACdC,oBAAoB,EACpBC,GAAG,EACHC,YAAY,QACP,WAAU;AAIjB,MAAMC,kBAAkB;AACxB,MAAMC,wBAAwB;AAC9B,MAAMC,cAAc,CAAC,wCAAwC,CAAC;AAE9D,+CAA+C;AAC/C,SAASC,kBAAkBC,QAAgB,EAAEC,QAAgB;IAC3D,MAAMC,SAASb,UAAUW;IACzB,IAAIG;IAEJ,IAAID,QAAQ;QACV,MAAME,KAAKb;QACXa,GAAGC,YAAY,CAACH,QAAQ,SAASI,MAAMC,IAAI;YACzC,IAAIJ,gBAAgB;YAEpB,uBAAuB;YACvB,IAAIV,qBAAqBQ,UAAUM,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEH,GAAGI,mBAAmB,CAACD,WACvBA,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIV,GAAGW,yBAAyB,CAACR,KAAKS,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;4BAC3D,IACEzB,qBAAqBQ,UAAUgB,gBAC/BA,YAAYE,IAAI,CAACC,OAAO,OAAO,YAC/B;gCACA,gCAAgC;gCAChCjB,iBAAiBc;gCACjB;4BACF;wBACF;oBACF;gBACF;YACF;QACF;IACF;IACA,OAAOd;AACT;AAEA,SAASkB,0BACPrB,QAAgB,EAChBO,IAAiE,EACjEe,kBAA4B;IAE5B,MAAMpB,SAASb,UAAUW;IACzB,IAAI,CAACE,QAAQ;IAEb,0DAA0D;IAC1D,MAAMqB,aAAarB,OAAOsB,WAAW;IACrC,IAAIC;IACJ,IAAIC;IAEJ,MAAMtB,KAAKb;IACX,IAAIa,GAAGuB,qBAAqB,CAACpB,OAAO;QAClC,IAAIe,oBAAoB;gBAENf;YADhBkB,UAAUlB,KAAKqB,IAAI,CAAEC,YAAY;YACjC,MAAMC,WAAUvB,kBAAAA,KAAKE,SAAS,qBAAdF,gBAAgBG,IAAI,CAClC,CAACC,IAAMA,EAAEC,IAAI,KAAKR,GAAGS,UAAU,CAACkB,YAAY;YAE9CL,aAAaI,UAAUjC,wBAAwBD;QACjD,OAAO;YACL;QACF;IACF,OAAO;QACL6B,UAAUlB,KAAKY,IAAI,CAACU,YAAY,KAAKtB,KAAKY,IAAI,CAACa,YAAY;QAC3DN,aAAa9B;IACf;IAEA,MAAMqC,YACJV,WAAWW,KAAK,CAAC,GAAGT,WACpBC,aACAH,WAAWW,KAAK,CAACT,WACjB3B;IAEF,IAAIH,aAAawC,aAAa,CAACnC,WAAW;QACxCN,IAAI,oBAAoBM;QACxBL,aAAayC,UAAU,CAACpC,UAAUiC;IACpC,OAAO;QACLvC,IAAI,oBAAoBM;QACxBL,aAAa0C,UAAU,CAACrC,UAAUiC;IACpC;IAEA,OAAO;QAACR;QAASC,WAAWY,MAAM;KAAC;AACrC;AAEA,SAASC,QACPhC,IAAiE;IAEjE,OAAOA,KAAKiC,IAAI,KAAKC;AACvB;AAEA,SAASC,iBACP1C,QAAgB,EAChB2C,GAAa,EACbC,CAA8D;IAE9D,kBAAkB;IAClB,MAAMC,cACJlD,aAAamD,eAAe,CAACC,sBAAsB,CAAC/C;IACtD,MAAME,SAASZ,0BAA0BU;IAEzC,6BAA6B;IAC7B,OAAO6C,YACJG,MAAM,CAAC,CAACC;QACP,IAAIA,EAAEC,KAAK,KAAKT,aAAaQ,EAAEX,MAAM,KAAKG,WAAW,OAAO;QAC5D,IAAIQ,EAAEC,KAAK,GAAGN,EAAEf,YAAY,IAAI,OAAO;QACvC,IAAIoB,EAAEC,KAAK,GAAGD,EAAEX,MAAM,IAAIM,EAAEf,YAAY,KAAKe,EAAEZ,YAAY,KAAKW,GAAG,CAAC,EAAE,EACpE,OAAO;QACT,OAAO;IACT,GACCQ,GAAG,CAAC,CAACF;QACJ,OAAO;YACLG,MAAMlD;YACNmD,UAAUJ,EAAEI,QAAQ;YACpBC,MAAML,EAAEK,IAAI;YACZC,aAAaN,EAAEM,WAAW;YAC1BL,OAAOD,EAAEC,KAAK,GAAIP,GAAG,CAAC,EAAE,GAAGM,EAAEC,KAAK,GAAGD,EAAEC,KAAK,GAAIP,GAAG,CAAC,EAAE;YACtDL,QAAQW,EAAEX,MAAM;QAClB;IACF;AACJ;AAEA,MAAMkB,WAAW;IACfC,6BACEzD,QAAgB,EAChBC,QAAgB,EAChByD,QAAa,EACbC,KAAqD;QAErD,MAAMpD,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM,OAAOoD;QAClB,IAAIpB,QAAQhC,OAAO,OAAOoD;QAE1B,0DAA0D;QAC1D,MAAMhB,MAAMtB,0BAA0BrB,UAAUO;QAChD,IAAIoC,QAAQF,WAAW,OAAOkB;QAE9B,kBAAkB;QAClB,MAAMC,SAAS3D,YAAY0C,GAAG,CAAC,EAAE,GAAG1C,WAAWA,WAAW0C,GAAG,CAAC,EAAE;QAChE,MAAMkB,cAAclE,aAAamD,eAAe,CAACgB,wBAAwB,CACvE9D,UACA4D,QACAnB;QAGF,IAAIoB,aAAa;YACf,MAAMzD,KAAKb;YACXsE,YAAYE,YAAY,GAAG;YAC3B,0HAA0H;YAC1H,IAAIF,YAAYG,uBAAuB,EAAE;gBACvC,iEAAiE;gBACjEH,YAAYG,uBAAuB,CAACd,KAAK,IAAIU,SAAS3D;YACxD;YACA4D,YAAYI,OAAO,GAAGJ,YAAYI,OAAO,CACtCjB,MAAM,CAAC,CAACkB;gBACP,OAAO;oBACL9D,GAAG+D,iBAAiB,CAACC,qBAAqB;oBAC1ChE,GAAG+D,iBAAiB,CAACE,WAAW;oBAChCjE,GAAG+D,iBAAiB,CAACG,MAAM;iBAC5B,CAACC,QAAQ,CAACL,EAAEtD,IAAI;YACnB,GACCuC,GAAG,CAAC,CAACe;gBACJ,MAAMM,aACJN,EAAEtD,IAAI,KAAKR,GAAG+D,iBAAiB,CAACC,qBAAqB,IACrD,kBAAkBK,IAAI,CAACP,EAAE/C,IAAI,IACzB+C,EAAE/C,IAAI,GAAG,OACT+C,EAAE/C,IAAI;gBAEZ,OAAO;oBACLA,MAAM+C,EAAE/C,IAAI;oBACZqD;oBACA5D,MAAMsD,EAAEtD,IAAI;oBACZ8D,eAAeR,EAAEQ,aAAa;oBAC9BC,UAAU,MAAMT,EAAE/C,IAAI;oBACtByD,cAAc;wBACZC,aAAa,CAAC,gBAAgB,CAAC;oBACjC;oBACAC,MAAMZ,EAAEY,IAAI;gBACd;YACF;YAEF,OAAOjB;QACT;QAEA,OAAOF;IACT;IAEAoB,+DACE/E,QAAgB,EAChBO,IAA+D;QAE/D,MAAML,SAASb,UAAUW;QACzB,MAAMI,KAAKb;QAEX,+EAA+E;QAC/E,IAAIa,GAAGuB,qBAAqB,CAACpB,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,OAAO;oBACL;wBACEgC,MAAMlD;wBACNmD,UAAUjD,GAAG4E,kBAAkB,CAACC,KAAK;wBACrC3B,MAAMlE,eAAe8F,uBAAuB;wBAC5C3B,aAAa,CAAC,wEAAwE,CAAC;wBACvFL,OAAO3C,KAAKY,IAAI,CAACgE,QAAQ;wBACzB7C,QAAQ/B,KAAKY,IAAI,CAACiE,QAAQ;oBAC5B;iBACD;YACH;QACF,OAAO;YACL,KAAK,MAAMnE,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,OAAOF,YAAYE,IAAI,CAACC,OAAO;gBACrC,IAAID,SAAS,YAAY;oBACvB,OAAO;wBACL;4BACEiC,MAAMlD;4BACNmD,UAAUjD,GAAG4E,kBAAkB,CAACC,KAAK;4BACrC3B,MAAMlE,eAAe8F,uBAAuB;4BAC5C3B,aAAa,CAAC,gEAAgE,CAAC;4BAC/EL,OAAOjC,YAAYE,IAAI,CAACgE,QAAQ;4BAChC7C,QAAQrB,YAAYE,IAAI,CAACiE,QAAQ;wBACnC;qBACD;gBACH;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAC,kDACErF,QAAgB,EAChBO,IAA+D;QAE/D,MAAMH,KAAKb;QAEX,IAAIa,GAAGuB,qBAAqB,CAACpB,OAAO;gBAC9BA;YAAJ,IAAIA,EAAAA,aAAAA,KAAKY,IAAI,qBAATZ,WAAWa,OAAO,QAAO,oBAAoB;gBAC/C,IAAImB,QAAQhC,OAAO,OAAO,EAAE;gBAE5B,0DAA0D;gBAC1D,MAAMoC,MAAMtB,0BAA0BrB,UAAUO,MAAM;gBACtD,IAAI,CAACoC,KAAK,OAAO,EAAE;gBAEnB,OAAOD,iBAAiB1C,UAAU2C,KAAKpC;YACzC;QACF,OAAO;YACL,KAAK,MAAMU,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,IAAID,YAAYE,IAAI,CAACC,OAAO,OAAO,YAAY;oBAC7C,IAAImB,QAAQtB,cAAc;oBAE1B,0DAA0D;oBAC1D,MAAM0B,MAAMtB,0BAA0BrB,UAAUiB;oBAChD,IAAI,CAAC0B,KAAK;oBAEV,OAAOD,iBAAiB1C,UAAU2C,KAAK1B;gBACzC;YACF;QACF;QACA,OAAO,EAAE;IACX;IAEAqE,yDACEtF,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKb;QACX,MAAMW,SAASb,UAAUW;QACzB,MAAM6C,cAAqC,EAAE;QAE7C,MAAM0C,eAAehF,KAAKgF,YAAY;QACtC,IAAIA,gBAAgBnF,GAAGoF,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAI;oBAAC;oBAAoB;iBAAW,CAAClB,QAAQ,CAACL,EAAE/C,IAAI,CAACC,OAAO,KAAK;oBAC/DyB,YAAY6C,IAAI,CAAC;wBACftC,MAAMlD;wBACNmD,UAAUjD,GAAG4E,kBAAkB,CAACC,KAAK;wBACrC3B,MAAMlE,eAAe8F,uBAAuB;wBAC5C3B,aAAa,CAAC,aAAa,EAAEW,EAAE/C,IAAI,CAACC,OAAO,GAAG,2CAA2C,CAAC;wBAC1F8B,OAAOgB,EAAE/C,IAAI,CAACgE,QAAQ;wBACtB7C,QAAQ4B,EAAE/C,IAAI,CAACiE,QAAQ;oBACzB;gBACF;YACF;QACF;QAEA,OAAOvC;IACT;IAEA8C,4CACE3F,QAAgB,EAChBO,IAAgC;QAEhC,MAAMH,KAAKb;QAEX,MAAMgG,eAAehF,KAAKgF,YAAY;QACtC,IAAIA,gBAAgBnF,GAAGoF,cAAc,CAACD,eAAe;YACnD,KAAK,MAAMrB,KAAKqB,aAAaE,QAAQ,CAAE;gBACrC,IAAIvB,EAAE/C,IAAI,CAACC,OAAO,OAAO,YAAY;oBACnC,+CAA+C;oBAC/C,MAAMwE,cAAcpG;oBACpB,IAAIoG,aAAa;wBACf,MAAMC,SAASD,YAAYE,mBAAmB,CAAC5B,EAAE/C,IAAI;wBACrD,IAAI0E,QAAQ;4BACV,MAAME,iBAAiBH,YAAYI,gBAAgB,CAACH;4BACpD,IAAIE,kBAAkBA,eAAe7E,YAAY,EAAE;gCACjD,MAAMD,cAAc8E,eAAe7E,YAAY,CAAC,EAAE;gCAClD,IAAID,eAAeb,GAAG6F,qBAAqB,CAAChF,cAAc;oCACxD,IAAIsB,QAAQtB,cAAc;oCAE1B,MAAMiF,sBACJjF,YAAYkB,aAAa,GAAGnC,QAAQ;oCACtC,MAAMmG,aAAaD,wBAAwBlG;oCAE3C,0DAA0D;oCAC1D,MAAM2C,MAAMtB,0BACV6E,qBACAjF;oCAEF,IAAI,CAAC0B,KAAK;oCAEV,MAAME,cAAcH,iBAClBwD,qBACAvD,KACA1B;oCAEF,IAAI4B,YAAYP,MAAM,EAAE;wCACtB,IAAI6D,YAAY;4CACd,OAAOtD;wCACT,OAAO;4CACL,OAAO;gDACL;oDACEO,MAAM/D,UAAUW;oDAChBqD,UAAUjD,GAAG4E,kBAAkB,CAACC,KAAK;oDACrC3B,MAAMlE,eAAe8F,uBAAuB;oDAC5C3B,aAAa,CAAC,0LAA0L,CAAC;oDACzML,OAAOgB,EAAE/C,IAAI,CAACgE,QAAQ;oDACtB7C,QAAQ4B,EAAE/C,IAAI,CAACiE,QAAQ;gDACzB;6CACD;wCACH;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,EAAE;IACX;IAEAgB,2BACEpG,QAAgB,EAChBC,QAAgB,EAChBoG,SAAiB,EACjBC,aAAyC,EACzCpG,MAAc,EACdqG,WAAqC,EACrCzB,IAAkC;QAElC,MAAMvE,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAIgC,QAAQhC,OAAO;QAEnB,0DAA0D;QAC1D,MAAMoC,MAAMtB,0BAA0BrB,UAAUO;QAChD,IAAIoC,QAAQF,WAAW;QAEvB,MAAMmB,SAAS3D,YAAY0C,GAAG,CAAC,EAAE,GAAG1C,WAAWA,WAAW0C,GAAG,CAAC,EAAE;QAEhE,MAAM6D,UAAU7G,aAAamD,eAAe,CAACsD,yBAAyB,CACpEpG,UACA4D,QACAyC,WACAC,eACApG,QACAqG,aACAzB;QAEF,OAAO0B;IACT;IAEAC,wBAAuBzG,QAAgB,EAAEC,QAAgB;QACvD,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAIgC,QAAQhC,OAAO;QAEnB,0DAA0D;QAC1D,MAAMoC,MAAMtB,0BAA0BrB,UAAUO;QAChD,IAAIoC,QAAQF,WAAW;QAEvB,MAAMmB,SAAS3D,YAAY0C,GAAG,CAAC,EAAE,GAAG1C,WAAWA,WAAW0C,GAAG,CAAC,EAAE;QAChE,MAAM+D,UAAU/G,aAAamD,eAAe,CAAC2D,sBAAsB,CACjEzG,UACA4D;QAEF,OAAO8C;IACT;IAEAC,2BAA0B3G,QAAgB,EAAEC,QAAgB;QAC1D,MAAMM,OAAOR,kBAAkBC,UAAUC;QACzC,IAAI,CAACM,MAAM;QACX,IAAIgC,QAAQhC,OAAO;QACnB,IAAI,CAACd,qBAAqBQ,UAAUM,OAAO;QAC3C,0DAA0D;QAC1D,MAAMoC,MAAMtB,0BAA0BrB,UAAUO;QAChD,IAAIoC,QAAQF,WAAW;QACvB,MAAMmB,SAAS3D,YAAY0C,GAAG,CAAC,EAAE,GAAG1C,WAAWA,WAAW0C,GAAG,CAAC,EAAE;QAEhE,MAAMiE,6BACJjH,aAAamD,eAAe,CAAC6D,yBAAyB,CAAC3G,UAAU4D;QAEnE,IAAIgD,4BAA4B;YAC9B,6CAA6C;YAC7C,IAAIA,2BAA2BC,QAAQ,CAAC3D,KAAK,GAAGP,GAAG,CAAC,EAAE,EAAE;gBACtDiE,2BAA2BC,QAAQ,CAAC3D,KAAK,IAAIP,GAAG,CAAC,EAAE;YACrD;QACF;QACA,OAAOiE;IACT;AACF;AAEA,eAAepD,SAAQ"}