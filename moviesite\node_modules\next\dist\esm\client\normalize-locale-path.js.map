{"version": 3, "sources": ["../../src/client/normalize-locale-path.ts"], "sourcesContent": ["import type { normalizeLocalePath as Fn } from '../shared/lib/i18n/normalize-locale-path'\n\nexport const normalizeLocalePath: typeof Fn = (pathname, locales) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return require('../shared/lib/i18n/normalize-locale-path').normalizeLocalePath(\n      pathname,\n      locales\n    )\n  }\n  return { pathname, detectedLocale: undefined }\n}\n"], "names": ["normalizeLocalePath", "pathname", "locales", "process", "env", "__NEXT_I18N_SUPPORT", "require", "detectedLocale", "undefined"], "mappings": "AAEA,OAAO,MAAMA,sBAAiC,CAACC,UAAUC;IACvD,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,OAAOC,QAAQ,4CAA4CN,mBAAmB,CAC5EC,UACAC;IAEJ;IACA,OAAO;QAAED;QAAUM,gBAAgBC;IAAU;AAC/C,EAAC"}