{"version": 3, "sources": ["../../src/server/font-utils.ts"], "sourcesContent": ["import {\n  DEFAULT_SERIF_FONT,\n  DEFAULT_SANS_SERIF_FONT,\n} from '../shared/lib/constants'\nconst capsizeFontsMetrics = require('next/dist/server/capsize-font-metrics.json')\n\nfunction formatName(str: string): string {\n  return str\n    .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, function (word, index) {\n      return index === 0 ? word.toLowerCase() : word.toUpperCase()\n    })\n    .replace(/\\s+/g, '')\n}\n\nfunction formatOverrideValue(val: number) {\n  return Math.abs(val * 100).toFixed(2)\n}\n\nexport function calculateSizeAdjustValues(fontName: string) {\n  const fontKey = formatName(fontName)\n  const fontMetrics = capsizeFontsMetrics[fontKey]\n  let { category, ascent, descent, lineGap, unitsPerEm, xWidthAvg } =\n    fontMetrics\n  const mainFontAvgWidth = xWidthAvg / unitsPerEm\n  const fallbackFont =\n    category === 'serif' ? DEFAULT_SERIF_FONT : DEFAULT_SANS_SERIF_FONT\n  const fallbackFontName = formatName(fallbackFont.name)\n  const fallbackFontMetrics = capsizeFontsMetrics[fallbackFontName]\n  const fallbackFontAvgWidth =\n    fallbackFontMetrics.xWidthAvg / fallbackFontMetrics.unitsPerEm\n  let sizeAdjust = xWidthAvg ? mainFontAvgWidth / fallbackFontAvgWidth : 1\n\n  ascent = formatOverrideValue(ascent / (unitsPerEm * sizeAdjust))\n  descent = formatOverrideValue(descent / (unitsPerEm * sizeAdjust))\n  lineGap = formatOverrideValue(lineGap / (unitsPerEm * sizeAdjust))\n\n  return {\n    ascent,\n    descent,\n    lineGap,\n    fallbackFont: fallbackFont.name,\n    sizeAdjust: formatOverrideValue(sizeAdjust),\n  }\n}\n"], "names": ["DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "capsizeFontsMetrics", "require", "formatName", "str", "replace", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "calculateSizeAdjustValues", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "xWidthAvg", "mainFontAvgWidth", "fallbackFont", "fallback<PERSON>ontName", "name", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust"], "mappings": "AAAA,SACEA,kBAAkB,EAClBC,uBAAuB,QAClB,0BAAyB;AAChC,MAAMC,sBAAsBC,QAAQ;AAEpC,SAASC,WAAWC,GAAW;IAC7B,OAAOA,IACJC,OAAO,CAAC,uBAAuB,SAAUC,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCJ,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASK,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEA,OAAO,SAASC,0BAA0BC,QAAgB;IACxD,MAAMC,UAAUd,WAAWa;IAC3B,MAAME,cAAcjB,mBAAmB,CAACgB,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE,GAC/DN;IACF,MAAMO,mBAAmBD,YAAYD;IACrC,MAAMG,eACJP,aAAa,UAAUpB,qBAAqBC;IAC9C,MAAM2B,mBAAmBxB,WAAWuB,aAAaE,IAAI;IACrD,MAAMC,sBAAsB5B,mBAAmB,CAAC0B,iBAAiB;IACjE,MAAMG,uBACJD,oBAAoBL,SAAS,GAAGK,oBAAoBN,UAAU;IAChE,IAAIQ,aAAaP,YAAYC,mBAAmBK,uBAAuB;IAEvEV,SAASV,oBAAoBU,SAAUG,CAAAA,aAAaQ,UAAS;IAC7DV,UAAUX,oBAAoBW,UAAWE,CAAAA,aAAaQ,UAAS;IAC/DT,UAAUZ,oBAAoBY,UAAWC,CAAAA,aAAaQ,UAAS;IAE/D,OAAO;QACLX;QACAC;QACAC;QACAI,cAAcA,aAAaE,IAAI;QAC/BG,YAAYrB,oBAAoBqB;IAClC;AACF"}