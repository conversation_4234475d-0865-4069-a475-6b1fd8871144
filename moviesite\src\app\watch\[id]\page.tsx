'use client';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import axios from 'axios';

interface TVShow {
  id: number;
  name: string;
  overview: string;
  poster_path: string | null;
  backdrop_path: string | null;
  first_air_date: string;
  vote_average: number;
  number_of_seasons: number;
  genres: { id: number; name: string }[];
}

export default function WatchPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const showId = params.id as string;
  const season = searchParams.get('season') || '1';
  const episode = searchParams.get('episode') || '1';
  
  const [tvShow, setTvShow] = useState<TVShow | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentSeason, setCurrentSeason] = useState(parseInt(season));
  const [currentEpisode, setCurrentEpisode] = useState(parseInt(episode));

  useEffect(() => {
    const fetchTVShow = async () => {
      try {
        const apiKey = process.env.NEXT_PUBLIC_TMDB_API_KEY;
        if (apiKey && apiKey !== '7c24e23bdbd4b53b1067dc3041392769') {
          console.log('Fetching real data from TMDB...');
          const response = await axios.get(
            `https://api.themoviedb.org/3/tv/${showId}?api_key=${apiKey}`
          );
          setTvShow(response.data);
        } else {
          // Mock data for demo purposes
          setTvShow({
            id: parseInt(showId),
            name: 'Sample TV Show',
            overview: 'This is a sample TV show for demonstration purposes.',
            poster_path: '/sample-poster.jpg',
            backdrop_path: '/sample-backdrop.jpg',
            first_air_date: '2024-01-01',
            vote_average: 8.5,
            number_of_seasons: 3,
            genres: [{ id: 1, name: 'Drama' }, { id: 2, name: 'Thriller' }]
          });
        }
      } catch (error) {
        console.error('Error fetching TV show:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTVShow();
  }, [showId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!tvShow) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-xl">TV Show not found</div>
      </div>
    );
  }

  const streamUrl = `${process.env.NEXT_PUBLIC_VIDSRC_BASE_URL}/tv/${showId}/${currentSeason}/${currentEpisode}`;

  const handleSeasonChange = (newSeason: number) => {
    setCurrentSeason(newSeason);
    setCurrentEpisode(1);
  };

  const handleEpisodeChange = (newEpisode: number) => {
    setCurrentEpisode(newEpisode);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Video Player */}
      <div className="w-full bg-black relative">
        {/* Player Header */}
        <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black to-transparent p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.history.back()}
                className="text-white text-xl hover:text-gray-300 flex items-center space-x-2"
              >
                <span>←</span>
                <span>Back</span>
              </button>
              <div className="text-white">
                <h3 className="text-lg font-semibold">{tvShow.name}</h3>
                <p className="text-sm text-gray-300">
                  Season {currentSeason}, Episode {currentEpisode} • {new Date(tvShow.first_air_date).getFullYear()} • ⭐ {tvShow.vote_average.toFixed(1)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="text-white hover:text-gray-300 p-2">
                <span className="text-xl">⚙️</span>
              </button>
              <button className="text-white hover:text-gray-300 p-2">
                <span className="text-xl">⛶</span>
              </button>
            </div>
          </div>
        </div>

        {/* Video Container */}
        <div className="relative w-full h-0 pb-[56.25%]">
          <iframe
            src={streamUrl}
            className="absolute top-0 left-0 w-full h-full border-0"
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            title={`Watch ${tvShow.name} S${currentSeason}E${currentEpisode}`}
          />

          {/* Loading overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center pointer-events-none">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>Loading episode...</p>
            </div>
          </div>
        </div>

        {/* Player Controls */}
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-4">
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full">
                <span className="text-xl">⏸️</span>
              </button>
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full">
                <span className="text-xl">🔊</span>
              </button>
              <span className="text-sm">Episode {currentEpisode}</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => currentEpisode > 1 && handleEpisodeChange(currentEpisode - 1)}
                disabled={currentEpisode <= 1}
                className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1 rounded text-sm"
              >
                Previous
              </button>
              <button
                onClick={() => handleEpisodeChange(currentEpisode + 1)}
                className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Show Info and Controls */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Show Details */}
          <div className="flex-1">
            <div className="flex items-start space-x-4 mb-6">
              {tvShow.poster_path && (
                <img
                  src={`https://image.tmdb.org/t/p/w200${tvShow.poster_path}`}
                  alt={tvShow.name}
                  className="w-24 h-36 object-cover rounded-lg"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold mb-2">{tvShow.name}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-300 mb-3">
                  <span>Season {currentSeason}, Episode {currentEpisode}</span>
                  <span>•</span>
                  <span>{new Date(tvShow.first_air_date).getFullYear()}</span>
                  <span>•</span>
                  <span>⭐ {tvShow.vote_average.toFixed(1)}</span>
                </div>
                <div className="flex flex-wrap gap-2 mb-4">
                  {tvShow.genres.map((genre) => (
                    <span
                      key={genre.id}
                      className="px-2 py-1 bg-blue-600 rounded text-xs"
                    >
                      {genre.name}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">{tvShow.overview}</p>
          </div>

          {/* Episode Controls */}
          <div className="lg:w-80">
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-4">Episodes</h3>
              
              {/* Season Selector */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Season</label>
                <select
                  value={currentSeason}
                  onChange={(e) => handleSeasonChange(parseInt(e.target.value))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  {Array.from({ length: tvShow.number_of_seasons }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      Season {i + 1}
                    </option>
                  ))}
                </select>
              </div>

              {/* Episode Selector */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Episode</label>
                <select
                  value={currentEpisode}
                  onChange={(e) => handleEpisodeChange(parseInt(e.target.value))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  {Array.from({ length: 20 }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      Episode {i + 1}
                    </option>
                  ))}
                </select>
              </div>

              {/* Navigation Buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={() => currentEpisode > 1 && handleEpisodeChange(currentEpisode - 1)}
                  disabled={currentEpisode <= 1}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2 rounded text-sm"
                >
                  Previous
                </button>
                <button
                  onClick={() => handleEpisodeChange(currentEpisode + 1)}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
