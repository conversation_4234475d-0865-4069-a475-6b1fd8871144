'use client';

import { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface VideoPlayerProps {
  src: string;
  title: string;
  onClose: () => void;
  controls?: {
    previous?: () => void;
    next?: () => void;
    previousDisabled?: boolean;
    nextDisabled?: boolean;
  };
  metadata?: {
    subtitle?: string;
    duration?: string;
    progress?: string;
  };
}

export default function VideoPlayer({ 
  src, 
  title, 
  onClose, 
  controls,
  metadata 
}: VideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [src]);

  useEffect(() => {
    let hideTimer: NodeJS.Timeout;
    
    const resetTimer = () => {
      setShowControls(true);
      clearTimeout(hideTimer);
      hideTimer = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    };

    const handleMouseMove = () => resetTimer();
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
      resetTimer();
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('keydown', handleKeyPress);
    resetTimer();

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('keydown', handleKeyPress);
      clearTimeout(hideTimer);
    };
  }, [onClose]);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black z-50 flex flex-col">
      {/* Header Controls */}
      <div className={`absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black via-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button
              onClick={onClose}
              className="text-white text-xl hover:text-gray-300 flex items-center space-x-2 transition-colors"
            >
              <span>←</span>
              <span>Back</span>
            </button>
            <div className="text-white">
              <h3 className="text-lg font-semibold">{title}</h3>
              {metadata?.subtitle && (
                <p className="text-sm text-gray-300">{metadata.subtitle}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={toggleFullscreen}
              className="text-white hover:text-gray-300 p-2 transition-colors"
              title="Toggle Fullscreen"
            >
              <span className="text-xl">{isFullscreen ? '⛶' : '⛶'}</span>
            </button>
            <button
              onClick={onClose}
              className="text-white text-2xl hover:text-gray-300 transition-colors"
              title="Close Player"
            >
              ✕
            </button>
          </div>
        </div>
      </div>

      {/* Video Container */}
      <div className="relative flex-1 w-full">
        <iframe
          src={src}
          className="w-full h-full border-0"
          allowFullScreen
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          title={title}
          onLoad={() => setIsLoading(false)}
        />
        
        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
            <div className="text-center">
              <LoadingSpinner
                message="Loading video... Please wait while the stream loads"
                size="lg"
              />
            </div>
          </div>
        )}
      </div>

      {/* Bottom Controls */}
      <div className={`absolute bottom-0 left-0 right-0 z-20 bg-gradient-to-t from-black via-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-4">
            <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all">
              <span className="text-xl">⏸️</span>
            </button>
            <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all">
              <span className="text-xl">🔊</span>
            </button>
            {metadata?.progress && (
              <span className="text-sm">{metadata.progress}</span>
            )}
            {metadata?.duration && (
              <span className="text-sm text-gray-300">/ {metadata.duration}</span>
            )}
          </div>
          
          {controls && (
            <div className="flex items-center space-x-2">
              {controls.previous && (
                <button
                  onClick={controls.previous}
                  disabled={controls.previousDisabled}
                  className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded text-sm font-medium transition-all"
                >
                  Previous
                </button>
              )}
              {controls.next && (
                <button
                  onClick={controls.next}
                  disabled={controls.nextDisabled}
                  className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed px-4 py-2 rounded text-sm font-medium transition-all"
                >
                  Next
                </button>
              )}
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all">
              <span className="text-xl">⚙️</span>
            </button>
            <button
              onClick={toggleFullscreen}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 p-3 rounded-full transition-all"
            >
              <span className="text-xl">⛶</span>
            </button>
          </div>
        </div>
      </div>

      {/* Click overlay to show/hide controls */}
      <div 
        className="absolute inset-0 z-10"
        onClick={() => setShowControls(!showControls)}
      />
    </div>
  );
}
