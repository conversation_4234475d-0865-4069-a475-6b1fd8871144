{"version": 3, "sources": ["../../../src/server/lib/lru-cache.ts"], "sourcesContent": ["export class LRUCache<T> {\n  private cache: Map<string, T>\n  private sizes: Map<string, number>\n  private totalSize: number\n  private maxSize: number\n  private calculateSize: (value: T) => number\n\n  constructor(maxSize: number, calculateSize?: (value: T) => number) {\n    this.cache = new Map()\n    this.sizes = new Map()\n    this.totalSize = 0\n    this.maxSize = maxSize\n    this.calculateSize = calculateSize || (() => 1)\n  }\n\n  set(key?: string | null, value?: T): void {\n    if (!key || !value) return\n\n    const size = this.calculateSize(value)\n\n    if (size > this.maxSize) {\n      console.warn('Single item size exceeds maxSize')\n      return\n    }\n\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n    }\n\n    this.cache.set(key, value)\n    this.sizes.set(key, size)\n    this.totalSize += size\n\n    this.touch(key)\n  }\n\n  has(key?: string | null): boolean {\n    if (!key) return false\n\n    this.touch(key)\n    return Boolean(this.cache.get(key))\n  }\n\n  get(key?: string | null): T | undefined {\n    if (!key) return\n\n    const value = this.cache.get(key)\n    if (value === undefined) {\n      return undefined\n    }\n\n    this.touch(key)\n    return value\n  }\n\n  private touch(key: string): void {\n    const value = this.cache.get(key)\n    if (value !== undefined) {\n      this.cache.delete(key)\n      this.cache.set(key, value)\n      this.evictIfNecessary()\n    }\n  }\n\n  private evictIfNecessary(): void {\n    while (this.totalSize > this.maxSize && this.cache.size > 0) {\n      this.evictLeastRecentlyUsed()\n    }\n  }\n\n  private evictLeastRecentlyUsed(): void {\n    const lruKey = this.cache.keys().next().value\n    if (lruKey !== undefined) {\n      const lruSize = this.sizes.get(lruKey) || 0\n      this.totalSize -= lruSize\n      this.cache.delete(lruKey)\n      this.sizes.delete(lruKey)\n    }\n  }\n\n  reset() {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  keys() {\n    return [...this.cache.keys()]\n  }\n\n  remove(key: string): void {\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n      this.cache.delete(key)\n      this.sizes.delete(key)\n    }\n  }\n\n  clear(): void {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  get size(): number {\n    return this.cache.size\n  }\n\n  get currentSize(): number {\n    return this.totalSize\n  }\n}\n"], "names": ["L<PERSON><PERSON><PERSON>", "constructor", "maxSize", "calculateSize", "cache", "Map", "sizes", "totalSize", "set", "key", "value", "size", "console", "warn", "has", "get", "touch", "Boolean", "undefined", "delete", "evictIfNecessary", "evictLeastRecentlyUsed", "lruKey", "keys", "next", "lruSize", "reset", "clear", "remove", "currentSize"], "mappings": "AAAA,OAAO,MAAMA;IAOXC,YAAYC,OAAe,EAAEC,aAAoC,CAAE;QACjE,IAAI,CAACC,KAAK,GAAG,IAAIC;QACjB,IAAI,CAACC,KAAK,GAAG,IAAID;QACjB,IAAI,CAACE,SAAS,GAAG;QACjB,IAAI,CAACL,OAAO,GAAGA;QACf,IAAI,CAACC,aAAa,GAAGA,iBAAkB,CAAA,IAAM,CAAA;IAC/C;IAEAK,IAAIC,GAAmB,EAAEC,KAAS,EAAQ;QACxC,IAAI,CAACD,OAAO,CAACC,OAAO;QAEpB,MAAMC,OAAO,IAAI,CAACR,aAAa,CAACO;QAEhC,IAAIC,OAAO,IAAI,CAACT,OAAO,EAAE;YACvBU,QAAQC,IAAI,CAAC;YACb;QACF;QAEA,IAAI,IAAI,CAACT,KAAK,CAACU,GAAG,CAACL,MAAM;YACvB,IAAI,CAACF,SAAS,IAAI,IAAI,CAACD,KAAK,CAACS,GAAG,CAACN,QAAQ;QAC3C;QAEA,IAAI,CAACL,KAAK,CAACI,GAAG,CAACC,KAAKC;QACpB,IAAI,CAACJ,KAAK,CAACE,GAAG,CAACC,KAAKE;QACpB,IAAI,CAACJ,SAAS,IAAII;QAElB,IAAI,CAACK,KAAK,CAACP;IACb;IAEAK,IAAIL,GAAmB,EAAW;QAChC,IAAI,CAACA,KAAK,OAAO;QAEjB,IAAI,CAACO,KAAK,CAACP;QACX,OAAOQ,QAAQ,IAAI,CAACb,KAAK,CAACW,GAAG,CAACN;IAChC;IAEAM,IAAIN,GAAmB,EAAiB;QACtC,IAAI,CAACA,KAAK;QAEV,MAAMC,QAAQ,IAAI,CAACN,KAAK,CAACW,GAAG,CAACN;QAC7B,IAAIC,UAAUQ,WAAW;YACvB,OAAOA;QACT;QAEA,IAAI,CAACF,KAAK,CAACP;QACX,OAAOC;IACT;IAEQM,MAAMP,GAAW,EAAQ;QAC/B,MAAMC,QAAQ,IAAI,CAACN,KAAK,CAACW,GAAG,CAACN;QAC7B,IAAIC,UAAUQ,WAAW;YACvB,IAAI,CAACd,KAAK,CAACe,MAAM,CAACV;YAClB,IAAI,CAACL,KAAK,CAACI,GAAG,CAACC,KAAKC;YACpB,IAAI,CAACU,gBAAgB;QACvB;IACF;IAEQA,mBAAyB;QAC/B,MAAO,IAAI,CAACb,SAAS,GAAG,IAAI,CAACL,OAAO,IAAI,IAAI,CAACE,KAAK,CAACO,IAAI,GAAG,EAAG;YAC3D,IAAI,CAACU,sBAAsB;QAC7B;IACF;IAEQA,yBAA+B;QACrC,MAAMC,SAAS,IAAI,CAAClB,KAAK,CAACmB,IAAI,GAAGC,IAAI,GAAGd,KAAK;QAC7C,IAAIY,WAAWJ,WAAW;YACxB,MAAMO,UAAU,IAAI,CAACnB,KAAK,CAACS,GAAG,CAACO,WAAW;YAC1C,IAAI,CAACf,SAAS,IAAIkB;YAClB,IAAI,CAACrB,KAAK,CAACe,MAAM,CAACG;YAClB,IAAI,CAAChB,KAAK,CAACa,MAAM,CAACG;QACpB;IACF;IAEAI,QAAQ;QACN,IAAI,CAACtB,KAAK,CAACuB,KAAK;QAChB,IAAI,CAACrB,KAAK,CAACqB,KAAK;QAChB,IAAI,CAACpB,SAAS,GAAG;IACnB;IAEAgB,OAAO;QACL,OAAO;eAAI,IAAI,CAACnB,KAAK,CAACmB,IAAI;SAAG;IAC/B;IAEAK,OAAOnB,GAAW,EAAQ;QACxB,IAAI,IAAI,CAACL,KAAK,CAACU,GAAG,CAACL,MAAM;YACvB,IAAI,CAACF,SAAS,IAAI,IAAI,CAACD,KAAK,CAACS,GAAG,CAACN,QAAQ;YACzC,IAAI,CAACL,KAAK,CAACe,MAAM,CAACV;YAClB,IAAI,CAACH,KAAK,CAACa,MAAM,CAACV;QACpB;IACF;IAEAkB,QAAc;QACZ,IAAI,CAACvB,KAAK,CAACuB,KAAK;QAChB,IAAI,CAACrB,KAAK,CAACqB,KAAK;QAChB,IAAI,CAACpB,SAAS,GAAG;IACnB;IAEA,IAAII,OAAe;QACjB,OAAO,IAAI,CAACP,KAAK,CAACO,IAAI;IACxB;IAEA,IAAIkB,cAAsB;QACxB,OAAO,IAAI,CAACtB,SAAS;IACvB;AACF"}