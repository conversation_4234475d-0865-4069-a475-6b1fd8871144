'use client';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';

interface Anime {
  id: number;
  title: string;
  overview: string;
  poster_path: string;
  backdrop_path: string;
  first_air_date: string;
  vote_average: number;
  number_of_episodes: number;
  genres: { id: number; name: string }[];
}

export default function AnimePage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const animeId = params.id as string;
  const episode = searchParams.get('episode') || '1';
  const dub = searchParams.get('dub') || 'sub';
  const skip = searchParams.get('skip') || 'false';
  
  const [anime, setAnime] = useState<Anime | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentEpisode, setCurrentEpisode] = useState(parseInt(episode));
  const [isDub, setIsDub] = useState(dub === 'dub');
  const [skipIntro, setSkipIntro] = useState(skip === 'true');

  useEffect(() => {
    const fetchAnime = async () => {
      try {
        // Mock data for demo purposes since TMDB doesn't have great anime support
        setAnime({
          id: parseInt(animeId),
          title: 'Sample Anime Series',
          overview: 'This is a sample anime series for demonstration purposes. Follow the adventures of our heroes as they embark on an epic journey.',
          poster_path: '/sample-anime-poster.jpg',
          backdrop_path: '/sample-anime-backdrop.jpg',
          first_air_date: '2024-01-01',
          vote_average: 9.2,
          number_of_episodes: 24,
          genres: [{ id: 1, name: 'Action' }, { id: 2, name: 'Adventure' }, { id: 3, name: 'Supernatural' }]
        });
      } catch (error) {
        console.error('Error fetching anime:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnime();
  }, [animeId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!anime) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-xl">Anime not found</div>
      </div>
    );
  }

  const streamUrl = `${process.env.NEXT_PUBLIC_VIDSRC_BASE_URL}/anime/${animeId}/${currentEpisode}/${isDub ? 'dub' : 'sub'}/${skipIntro}`;

  const handleEpisodeChange = (newEpisode: number) => {
    setCurrentEpisode(newEpisode);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Video Player */}
      <div className="w-full bg-black relative">
        {/* Player Header */}
        <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black to-transparent p-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.history.back()}
                className="text-white text-xl hover:text-gray-300 flex items-center space-x-2"
              >
                <span>←</span>
                <span>Back</span>
              </button>
              <div className="text-white">
                <h3 className="text-lg font-semibold">{anime.title}</h3>
                <p className="text-sm text-gray-300">
                  Episode {currentEpisode} • {isDub ? 'Dubbed' : 'Subbed'} • {new Date(anime.first_air_date).getFullYear()} • ⭐ {anime.vote_average.toFixed(1)}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsDub(!isDub)}
                className={`px-3 py-1 rounded text-sm ${isDub ? 'bg-purple-600' : 'bg-gray-600'} hover:opacity-80`}
              >
                {isDub ? 'DUB' : 'SUB'}
              </button>
              <button
                onClick={() => setSkipIntro(!skipIntro)}
                className={`px-3 py-1 rounded text-sm ${skipIntro ? 'bg-purple-600' : 'bg-gray-600'} hover:opacity-80`}
              >
                Skip Intro
              </button>
            </div>
          </div>
        </div>

        {/* Video Container */}
        <div className="relative w-full h-0 pb-[56.25%]">
          <iframe
            src={streamUrl}
            className="absolute top-0 left-0 w-full h-full border-0"
            allowFullScreen
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            title={`Watch ${anime.title} Episode ${currentEpisode}`}
          />

          {/* Loading overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center pointer-events-none">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>Loading anime episode...</p>
            </div>
          </div>
        </div>

        {/* Player Controls */}
        <div className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-4">
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full">
                <span className="text-xl">⏸️</span>
              </button>
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-full">
                <span className="text-xl">🔊</span>
              </button>
              <span className="text-sm">Episode {currentEpisode} of {anime.number_of_episodes}</span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => currentEpisode > 1 && handleEpisodeChange(currentEpisode - 1)}
                disabled={currentEpisode <= 1}
                className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1 rounded text-sm"
              >
                Previous
              </button>
              <button
                onClick={() => currentEpisode < anime.number_of_episodes && handleEpisodeChange(currentEpisode + 1)}
                disabled={currentEpisode >= anime.number_of_episodes}
                className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-1 rounded text-sm"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Anime Info and Controls */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Anime Details */}
          <div className="flex-1">
            <div className="flex items-start space-x-4 mb-6">
              <div className="w-24 h-36 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-2xl">🎌</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold mb-2">{anime.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-300 mb-3">
                  <span>Episode {currentEpisode}</span>
                  <span>•</span>
                  <span>{isDub ? 'Dubbed' : 'Subbed'}</span>
                  <span>•</span>
                  <span>{new Date(anime.first_air_date).getFullYear()}</span>
                  <span>•</span>
                  <span>⭐ {anime.vote_average.toFixed(1)}</span>
                </div>
                <div className="flex flex-wrap gap-2 mb-4">
                  {anime.genres.map((genre) => (
                    <span
                      key={genre.id}
                      className="px-2 py-1 bg-purple-600 rounded text-xs"
                    >
                      {genre.name}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">{anime.overview}</p>
          </div>

          {/* Episode Controls */}
          <div className="lg:w-80">
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-4">Episode Settings</h3>
              
              {/* Episode Selector */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Episode</label>
                <select
                  value={currentEpisode}
                  onChange={(e) => handleEpisodeChange(parseInt(e.target.value))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  {Array.from({ length: anime.number_of_episodes }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      Episode {i + 1}
                    </option>
                  ))}
                </select>
              </div>

              {/* Audio Options */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">Audio</label>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setIsDub(false)}
                    className={`flex-1 py-2 px-3 rounded text-sm ${
                      !isDub ? 'bg-purple-600' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    Subbed
                  </button>
                  <button
                    onClick={() => setIsDub(true)}
                    className={`flex-1 py-2 px-3 rounded text-sm ${
                      isDub ? 'bg-purple-600' : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                  >
                    Dubbed
                  </button>
                </div>
              </div>

              {/* Skip Intro Option */}
              <div className="mb-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={skipIntro}
                    onChange={(e) => setSkipIntro(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">Skip Intro</span>
                </label>
              </div>

              {/* Navigation Buttons */}
              <div className="flex space-x-2">
                <button
                  onClick={() => currentEpisode > 1 && handleEpisodeChange(currentEpisode - 1)}
                  disabled={currentEpisode <= 1}
                  className="flex-1 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2 rounded text-sm"
                >
                  Previous
                </button>
                <button
                  onClick={() => currentEpisode < anime.number_of_episodes && handleEpisodeChange(currentEpisode + 1)}
                  disabled={currentEpisode >= anime.number_of_episodes}
                  className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed px-3 py-2 rounded text-sm"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
