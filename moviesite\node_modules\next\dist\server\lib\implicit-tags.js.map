{"version": 3, "sources": ["../../../src/server/lib/implicit-tags.ts"], "sourcesContent": ["import { NEXT_CACHE_IMPLICIT_TAG_ID } from '../../lib/constants'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { getCacheHandlerEntries } from '../use-cache/handlers'\nimport { create<PERSON>azyR<PERSON>ult, type <PERSON><PERSON><PERSON><PERSON>ult } from './lazy-result'\n\nexport interface ImplicitTags {\n  /**\n   * For legacy usage, the implicit tags are passed to the incremental cache\n   * handler in `get` calls.\n   */\n  readonly tags: string[]\n\n  /**\n   * Modern cache handlers don't receive implicit tags. Instead, the implicit\n   * tags' expirations are stored in the work unit store, and used to compare\n   * with a cache entry's timestamp.\n   *\n   * Note: This map contains lazy results so that we can evaluate them when the\n   * first cache entry is read. It allows us to skip fetching the expiration\n   * values if no caches are read at all.\n   */\n  readonly expirationsByCacheKind: Map<string, LazyResult<number>>\n}\n\nconst getDerivedTags = (pathname: string): string[] => {\n  const derivedTags: string[] = [`/layout`]\n\n  // we automatically add the current path segments as tags\n  // for revalidatePath handling\n  if (pathname.startsWith('/')) {\n    const pathnameParts = pathname.split('/')\n\n    for (let i = 1; i < pathnameParts.length + 1; i++) {\n      let curPathname = pathnameParts.slice(0, i).join('/')\n\n      if (curPathname) {\n        // all derived tags other than the page are layout tags\n        if (!curPathname.endsWith('/page') && !curPathname.endsWith('/route')) {\n          curPathname = `${curPathname}${\n            !curPathname.endsWith('/') ? '/' : ''\n          }layout`\n        }\n        derivedTags.push(curPathname)\n      }\n    }\n  }\n  return derivedTags\n}\n\n/**\n * Creates a map with lazy results that fetch the expiration value for the given\n * tags and respective cache kind when they're awaited for the first time.\n */\nfunction createTagsExpirationsByCacheKind(\n  tags: string[]\n): Map<string, LazyResult<number>> {\n  const expirationsByCacheKind = new Map<string, LazyResult<number>>()\n  const cacheHandlers = getCacheHandlerEntries()\n\n  if (cacheHandlers) {\n    for (const [kind, cacheHandler] of cacheHandlers) {\n      if ('getExpiration' in cacheHandler) {\n        expirationsByCacheKind.set(\n          kind,\n          createLazyResult(async () => cacheHandler.getExpiration(...tags))\n        )\n      }\n    }\n  }\n\n  return expirationsByCacheKind\n}\n\nexport async function getImplicitTags(\n  page: string,\n  url: {\n    pathname: string\n    search?: string\n  },\n  fallbackRouteParams: null | FallbackRouteParams\n): Promise<ImplicitTags> {\n  const tags: string[] = []\n  const hasFallbackRouteParams =\n    fallbackRouteParams && fallbackRouteParams.size > 0\n\n  // Add the derived tags from the page.\n  const derivedTags = getDerivedTags(page)\n  for (let tag of derivedTags) {\n    tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${tag}`\n    tags.push(tag)\n  }\n\n  // Add the tags from the pathname. If the route has unknown params, we don't\n  // want to add the pathname as a tag, as it will be invalid.\n  if (url.pathname && !hasFallbackRouteParams) {\n    const tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${url.pathname}`\n    tags.push(tag)\n  }\n\n  return {\n    tags,\n    expirationsByCacheKind: createTagsExpirationsByCacheKind(tags),\n  }\n}\n"], "names": ["getImplicitTags", "getDerivedTags", "pathname", "derivedTags", "startsWith", "pathnameParts", "split", "i", "length", "curPathname", "slice", "join", "endsWith", "push", "createTagsExpirationsByCacheKind", "tags", "expirationsByCacheKind", "Map", "cacheHandlers", "getCacheHandlerEntries", "kind", "cache<PERSON><PERSON><PERSON>", "set", "createLazyResult", "getExpiration", "page", "url", "fallbackRouteParams", "hasFallbackRouteParams", "size", "tag", "NEXT_CACHE_IMPLICIT_TAG_ID"], "mappings": ";;;;+BAyEsBA;;;eAAAA;;;2BAzEqB;0BAEJ;4BACW;AAqBlD,MAAMC,iBAAiB,CAACC;IACtB,MAAMC,cAAwB;QAAC,CAAC,OAAO,CAAC;KAAC;IAEzC,yDAAyD;IACzD,8BAA8B;IAC9B,IAAID,SAASE,UAAU,CAAC,MAAM;QAC5B,MAAMC,gBAAgBH,SAASI,KAAK,CAAC;QAErC,IAAK,IAAIC,IAAI,GAAGA,IAAIF,cAAcG,MAAM,GAAG,GAAGD,IAAK;YACjD,IAAIE,cAAcJ,cAAcK,KAAK,CAAC,GAAGH,GAAGI,IAAI,CAAC;YAEjD,IAAIF,aAAa;gBACf,uDAAuD;gBACvD,IAAI,CAACA,YAAYG,QAAQ,CAAC,YAAY,CAACH,YAAYG,QAAQ,CAAC,WAAW;oBACrEH,cAAc,GAAGA,cACf,CAACA,YAAYG,QAAQ,CAAC,OAAO,MAAM,GACpC,MAAM,CAAC;gBACV;gBACAT,YAAYU,IAAI,CAACJ;YACnB;QACF;IACF;IACA,OAAON;AACT;AAEA;;;CAGC,GACD,SAASW,iCACPC,IAAc;IAEd,MAAMC,yBAAyB,IAAIC;IACnC,MAAMC,gBAAgBC,IAAAA,gCAAsB;IAE5C,IAAID,eAAe;QACjB,KAAK,MAAM,CAACE,MAAMC,aAAa,IAAIH,cAAe;YAChD,IAAI,mBAAmBG,cAAc;gBACnCL,uBAAuBM,GAAG,CACxBF,MACAG,IAAAA,4BAAgB,EAAC,UAAYF,aAAaG,aAAa,IAAIT;YAE/D;QACF;IACF;IAEA,OAAOC;AACT;AAEO,eAAehB,gBACpByB,IAAY,EACZC,GAGC,EACDC,mBAA+C;IAE/C,MAAMZ,OAAiB,EAAE;IACzB,MAAMa,yBACJD,uBAAuBA,oBAAoBE,IAAI,GAAG;IAEpD,sCAAsC;IACtC,MAAM1B,cAAcF,eAAewB;IACnC,KAAK,IAAIK,OAAO3B,YAAa;QAC3B2B,MAAM,GAAGC,qCAA0B,GAAGD,KAAK;QAC3Cf,KAAKF,IAAI,CAACiB;IACZ;IAEA,4EAA4E;IAC5E,4DAA4D;IAC5D,IAAIJ,IAAIxB,QAAQ,IAAI,CAAC0B,wBAAwB;QAC3C,MAAME,MAAM,GAAGC,qCAA0B,GAAGL,IAAIxB,QAAQ,EAAE;QAC1Da,KAAKF,IAAI,CAACiB;IACZ;IAEA,OAAO;QACLf;QACAC,wBAAwBF,iCAAiCC;IAC3D;AACF"}