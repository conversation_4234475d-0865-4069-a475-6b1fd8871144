{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-client-pages-loader.ts"], "sourcesContent": ["import { stringifyRequest } from '../stringify-request'\n\nexport type ClientPagesLoaderOptions = {\n  absolutePagePath: string\n  page: string\n}\n\n// this parameter: https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\nfunction nextClientPagesLoader(this: any) {\n  const pagesLoaderSpan = this.currentTraceSpan.traceChild(\n    'next-client-pages-loader'\n  )\n\n  return pagesLoaderSpan.traceFn(() => {\n    const { absolutePagePath, page } =\n      this.getOptions() as ClientPagesLoaderOptions\n\n    pagesLoaderSpan.setAttribute('absolutePagePath', absolutePagePath)\n\n    const stringifiedPageRequest = stringifyRequest(this, absolutePagePath)\n    const stringifiedPage = JSON.stringify(page)\n\n    return `\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      ${stringifiedPage},\n      function () {\n        return require(${stringifiedPageRequest});\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([${stringifiedPage}])\n      });\n    }\n  `\n  })\n}\n\nexport default nextClientPagesLoader\n"], "names": ["stringifyRequest", "nextClientPagesLoader", "pagesLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "absolutePagePath", "page", "getOptions", "setAttribute", "stringifiedPageRequest", "stringifiedPage", "JSON", "stringify"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,uBAAsB;AAOvD,8FAA8F;AAC9F,SAASC;IACP,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CACtD;IAGF,OAAOF,gBAAgBG,OAAO,CAAC;QAC7B,MAAM,EAAEC,gBAAgB,EAAEC,IAAI,EAAE,GAC9B,IAAI,CAACC,UAAU;QAEjBN,gBAAgBO,YAAY,CAAC,oBAAoBH;QAEjD,MAAMI,yBAAyBV,iBAAiB,IAAI,EAAEM;QACtD,MAAMK,kBAAkBC,KAAKC,SAAS,CAACN;QAEvC,OAAO,CAAC;;MAEN,EAAEI,gBAAgB;;uBAED,EAAED,uBAAuB;;;;;8BAKlB,EAAEC,gBAAgB;;;EAG9C,CAAC;IACD;AACF;AAEA,eAAeV,sBAAqB"}