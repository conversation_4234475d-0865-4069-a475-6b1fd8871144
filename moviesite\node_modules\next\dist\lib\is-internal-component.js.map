{"version": 3, "sources": ["../../src/lib/is-internal-component.ts"], "sourcesContent": ["export function isInternalComponent(pathname: string): boolean {\n  switch (pathname) {\n    case 'next/dist/pages/_app':\n    case 'next/dist/pages/_document':\n      return true\n    default:\n      return false\n  }\n}\n\nexport function isNonRoutePagesPage(pathname: string): boolean {\n  return pathname === '/_app' || pathname === '/_document'\n}\n"], "names": ["isInternalComponent", "isNonRoutePagesPage", "pathname"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,mBAAmB;eAAnBA;;IAUAC,mBAAmB;eAAnBA;;;AAVT,SAASD,oBAAoBE,QAAgB;IAClD,OAAQA;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,SAASD,oBAAoBC,QAAgB;IAClD,OAAOA,aAAa,WAAWA,aAAa;AAC9C"}